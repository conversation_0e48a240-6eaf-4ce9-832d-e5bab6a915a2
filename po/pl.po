# translation of pl.po to polski
# Polish translations for dnsmasq package.
# This file is put in the public domain.
#
# <PERSON><PERSON> <<EMAIL>>, 2005.
# <PERSON> <<EMAIL>>, 2008-2015.
#
msgid ""
msgstr ""
"Project-Id-Version: pl\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2009-06-10 20:57+0100\n"
"PO-Revision-Date: 2017-07-17 18:30+0100\n"
"Last-Translator: <PERSON> <jasiups<PERSON>@gmail.com>\n"
"Language-Team: polski <>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"
"X-Generator: Poedit 1.8.7\n"
"X-Language: pl_PL\n"

#: cache.c:652
msgid "Internal error in cache."
msgstr "Wewnętrzny błąd w pamięci podręcznej."

#: cache.c:1179
#, c-format
msgid "failed to load names from %s: %s"
msgstr "nie potrafię wczytać nazw z %s: %s"

#: cache.c:1201 dhcp.c:943
#, c-format
msgid "bad address at %s line %d"
msgstr "błędny adres w pliku %s, w linii %d"

#: cache.c:1254 dhcp.c:959
#, c-format
msgid "bad name at %s line %d"
msgstr "błędna nazwa w pliku %s, w linii %d"

#: cache.c:1265
#, fuzzy, c-format
msgid "read %s - %d names"
msgstr "wczytałem %s - %d adresów"

#: cache.c:1381
msgid "cleared cache"
msgstr "wyczyszczono pamięć podręczną"

#: cache.c:1445
#, c-format
msgid "No IPv4 address found for %s"
msgstr "Nie znalazłem adresu IPv4 komputera %s"

#: cache.c:1491
#, c-format
msgid "%s is a CNAME, not giving it to the DHCP lease of %s"
msgstr "%s to nazwa CNAME, nie przypisuję jej dzierżawie DHCP %s"

#: cache.c:1515
#, c-format
msgid "not giving name %s to the DHCP lease of %s because the name exists in %s with address %s"
msgstr "nazwa %s nie została nadana dzierżawie DHCP %s, ponieważ nazwa istnieje w %s i ma już adres %s"

#: cache.c:1760
#, c-format
msgid "time %lu"
msgstr "czas %lu"

#: cache.c:1761
#, c-format
msgid "cache size %d, %d/%d cache insertions re-used unexpired cache entries."
msgstr "wielkość pamięci podręcznej: %d; %d z %d miejsc aktualnych wpisów użyto ponownie."

#: cache.c:1763
#, c-format
msgid "queries forwarded %u, queries answered locally %u"
msgstr "%u zapytań przesłanych dalej, %u odpowiedzi udzielonych samodzielnie"

#: cache.c:1766
#, c-format
msgid "queries answered from stale cache %u"
msgstr ""

#: cache.c:1768
#, c-format
msgid "queries for authoritative zones %u"
msgstr "zapytań do stref autorytatywnych %u"

#: cache.c:1796
#, fuzzy, c-format
msgid "server %s#%d: queries sent %u, retried %u, failed %u, nxdomain replies %u, avg. latency %ums"
msgstr "serwer %s#%d: %u zapytań wysłanych, %u ponowionych lub nieudanych"

#: util.c:51
#, c-format
msgid "failed to seed the random number generator: %s"
msgstr "brak możliwości użycia generatora liczb losowych: %s"

#: util.c:246
msgid "failed to allocate memory"
msgstr "nie udało się przydzielić pamięci"

#: util.c:305 option.c:696
msgid "could not get memory"
msgstr "nie można dostać pamięci"

#: util.c:326
#, c-format
msgid "cannot create pipe: %s"
msgstr "błąd podczas próby utworzenia potoku: %s"

#: util.c:334
#, c-format
msgid "failed to allocate %d bytes"
msgstr "niemożliwość przydzielenia %d bajtów pamięci"

#: util.c:344
#, fuzzy, c-format
msgid "failed to reallocate %d bytes"
msgstr "niemożliwość przydzielenia %d bajtów pamięci"

#: util.c:465
#, fuzzy, c-format
msgid "cannot read monotonic clock: %s"
msgstr "nie potrafię utworzyć połączenia netlink %s"

#: util.c:579
#, c-format
msgid "infinite"
msgstr "nieskończona"

#: util.c:867
#, c-format
msgid "failed to find kernel version: %s"
msgstr "niezgodna wersja jądra: %s"

#: option.c:393
msgid "Specify local address(es) to listen on."
msgstr "Wskazanie adresów, na których należy nasłuchiwać."

#: option.c:394
msgid "Return ipaddr for all hosts in specified domains."
msgstr "Zwracanie adresu IP dla wszystkich hostów we wskazanych domenach."

#: option.c:395
msgid "Fake reverse lookups for RFC1918 private address ranges."
msgstr "Wyłączenie przekazywania zapytań odwrotnych dla prywatnych zakresów IP."

#: option.c:396
msgid "Treat ipaddr as NXDOMAIN (defeats Verisign wildcard)."
msgstr "Traktowanie adresu IP jako NXDOMAIN (unieważnia ,,Verisign wildcard'')."

#: option.c:397
#, c-format
msgid "Specify the size of the cache in entries (defaults to %s)."
msgstr "Wskazanie wielkości pamięci podręcznej (domyślnie: %s miejsc)."

#: option.c:398
#, c-format
msgid "Specify configuration file (defaults to %s)."
msgstr "Wskazanie pliku konfiguracyjnego (domyślnie: %s)."

#: option.c:399
msgid "Do NOT fork into the background: run in debug mode."
msgstr "NIE twórz procesu potomnego w tle: działanie w trybie debugowania."

#: option.c:400
msgid "Do NOT forward queries with no domain part."
msgstr "Wyłączenie przekazywania zapytań bez podanej części domenowej."

#: option.c:401
msgid "Return self-pointing MX records for local hosts."
msgstr "Zwracanie samowskazującego rekordu MX dla lokalnych hostów."

#: option.c:402
msgid "Expand simple names in /etc/hosts with domain-suffix."
msgstr "Rozwijanie prostych nazw z /etc/hosts przyrostkiem domenowym."

#: option.c:403
msgid "Don't forward spurious DNS requests from Windows hosts."
msgstr "Wyłączenie przekazywania pozornych zapytań DNS z komputerów działających pod Windows."

#: option.c:404
msgid "Don't include IPv4 addresses in DNS answers."
msgstr ""

#: option.c:405
msgid "Don't include IPv6 addresses in DNS answers."
msgstr ""

#: option.c:406
msgid "Enable DHCP in the range given with lease duration."
msgstr "Włączenie serwera DHCP dla wskazanego zakresu adresów."

#: option.c:407
#, c-format
msgid "Change to this group after startup (defaults to %s)."
msgstr "Po uruchomieniu zmiana grupy procesu na podaną (domyślnie: %s)."

#: option.c:408
msgid "Set address or hostname for a specified machine."
msgstr "Ustawienie adresu lub nazwy dla wskazanego komputera."

#: option.c:409
msgid "Read DHCP host specs from file."
msgstr "Wskazanie pliku z wartościami 'dhcp-host='."

#: option.c:410
msgid "Read DHCP option specs from file."
msgstr "Wskazanie pliku z wartościami 'dhcp-option='."

#: option.c:411
msgid "Read DHCP host specs from a directory."
msgstr "Odczyt specyfikacji hostów dla DHCP z katalogu."

#: option.c:412
msgid "Read DHCP options from a directory."
msgstr "Odczyt opcji DHCP z katalogu."

#: option.c:413
msgid "Evaluate conditional tag expression."
msgstr "Warunkowe ustawianie znaczników."

#: option.c:414
#, c-format
msgid "Do NOT load %s file."
msgstr "NIE wczytywanie pliku %s."

#: option.c:415
#, c-format
msgid "Specify a hosts file to be read in addition to %s."
msgstr "Wskazanie dodatkowego pliku 'hosts' oprócz %s."

#: option.c:416
msgid "Read hosts files from a directory."
msgstr "Odczyt pliku hostów z katalogu."

#: option.c:417
msgid "Specify interface(s) to listen on."
msgstr "Interfejsy, na których nasłuchiwać."

#: option.c:418
msgid "Specify interface(s) NOT to listen on."
msgstr "Interfejsy, na których NIE nasłuchiwać."

#: option.c:419
msgid "Map DHCP user class to tag."
msgstr "Przyporządkowanie znacznika w zależności od klasy użytkownika DHCP."

#: option.c:420
msgid "Map RFC3046 circuit-id to tag."
msgstr "Przyporządkowanie znacznika w zależności od numeru obwodu (w rozumieniu RFC3046)."

#: option.c:421
msgid "Map RFC3046 remote-id to tag."
msgstr "Przyporządkowanie znacznika w zależności od numeru agenta (w rozumieniu RFC3046)."

#: option.c:422
msgid "Map RFC3993 subscriber-id to tag."
msgstr "Przyporządkowanie znacznika w zależności od numeru subskrybenta (w rozumieniu RFC3993)."

#: option.c:423
msgid "Specify vendor class to match for PXE requests."
msgstr ""

#: option.c:424
msgid "Don't do DHCP for hosts with tag set."
msgstr "Wyłączenie DHCP dla hostów z określonym znacznikiem."

#: option.c:425
msgid "Force broadcast replies for hosts with tag set."
msgstr "Wymuszenie odpowiedzi w trybie rozgłoszeniowym dla hostów z określonym znacznikiem."

#: option.c:426
msgid "Do NOT fork into the background, do NOT run in debug mode."
msgstr "NIE twórz procesu potomnego w tle i NIE włączaj trybu debugowania."

#: option.c:427
msgid "Assume we are the only DHCP server on the local network."
msgstr "Zakładanie, że jesteśmy jedynym serwerem DHCP w sieci lokalnej."

#: option.c:428
#, c-format
msgid "Specify where to store DHCP leases (defaults to %s)."
msgstr "Ścieżka przechowywania pliku dzierżaw DHCP (domyślnie: %s)."

#: option.c:429
msgid "Return MX records for local hosts."
msgstr "Włączenie zwracania rekordu MX dla hostów lokalnych."

#: option.c:430
msgid "Specify an MX record."
msgstr "Specyfikacja rekordu MX."

#: option.c:431
msgid "Specify BOOTP options to DHCP server."
msgstr "Określenie opcji BOOTP serwera DHCP."

#: option.c:432
#, c-format
msgid "Do NOT poll %s file, reload only on SIGHUP."
msgstr "Wyłączenie obserwowania pliku %s; ponowne odczytywanie tylko po odebraniu sygnału SIGHUP."

#: option.c:433
msgid "Do NOT cache failed search results."
msgstr "Wyłączenie przechowywania w pamięci podręcznej wyników nieudanych wyszukiwań."

#: option.c:434
msgid "Use expired cache data for faster reply."
msgstr ""

#: option.c:435
#, c-format
msgid "Use nameservers strictly in the order given in %s."
msgstr "Odpytywanie serwerów nazw w kolejności ich wystąpienia w %s."

#: option.c:436
msgid "Specify options to be sent to DHCP clients."
msgstr "Specyfikacja opcji wysyłanej do klientów DHCP."

#: option.c:437
msgid "DHCP option sent even if the client does not request it."
msgstr "Opcja DHCP wysyłana nawet jeżeli klient o nią nie prosi."

#: option.c:438
msgid "Specify port to listen for DNS requests on (defaults to 53)."
msgstr "Wskazanie portu do nasłuchiwania zapytań DNS (domyślnie: 53)."

#: option.c:439
#, c-format
msgid "Maximum supported UDP packet size for EDNS.0 (defaults to %s)."
msgstr "Maksymalna obsługiwana wielkość pakietu EDNS.0 (domyślnie: %s)."

#: option.c:440
msgid "Log DNS queries."
msgstr "Włączenie spisywania zapytań DNS do logu."

#: option.c:441
msgid "Force the originating port for upstream DNS queries."
msgstr "Wymuszenie użycia wskazanego portu UDP do odpytywania nadrzędnych serwerów DNS i odbierania od nich odpowiedzi."

#: option.c:442
msgid "Set maximum number of random originating ports for a query."
msgstr ""

#: option.c:443
msgid "Do NOT read resolv.conf."
msgstr "Wyłączenie czytania pliku resolv.conf."

#: option.c:444
#, c-format
msgid "Specify path to resolv.conf (defaults to %s)."
msgstr "Wskazanie położenia pliku resolv.conf (domyślnie: %s)."

#: option.c:445
msgid "Specify path to file with server= options"
msgstr "Wskazanie położenia pliku z opcjami server="

#: option.c:446
msgid "Specify address(es) of upstream servers with optional domains."
msgstr "Wskazywanie adresów serwerów nazw, opcjonalnie z przypisaniem do domeny."

#: option.c:447
msgid "Specify address of upstream servers for reverse address queries"
msgstr "Wskazanie serwerów nazw do odwrotnej translacji adresów."

#: option.c:448
msgid "Never forward queries to specified domains."
msgstr "Wyłączenie przekazywania zapytań do wskazanych domen."

#: option.c:449
msgid "Specify the domain to be assigned in DHCP leases."
msgstr "Wskazanie domeny dla serwera DHCP."

#: option.c:450
msgid "Specify default target in an MX record."
msgstr "Określenie domyślnego celu w rekordzie MX."

#: option.c:451
msgid "Specify time-to-live in seconds for replies from /etc/hosts."
msgstr "Określenie (w sekundach) czasu ważności odpowiedzi udzielonych na podstawie /etc/hosts (domyślnie 0)."

#: option.c:452
msgid "Specify time-to-live in seconds for negative caching."
msgstr "Określenie (w sekundach) czasu ważności negatywnych odpowiedzi."

#: option.c:453
msgid "Specify time-to-live in seconds for maximum TTL to send to clients."
msgstr "Ograniczenie maksymalnego czasu ważności odpowiedzi (TTL) podawanego klientom [w sekundach]."

#: option.c:454
msgid "Specify time-to-live ceiling for cache."
msgstr "Określenie górnej granicy czasu ważności dla wpisów w pamięci podręcznej."

#: option.c:455
msgid "Specify time-to-live floor for cache."
msgstr "Określenie dolnej granicy czasu ważności dla wpisów w pamięci podręcznej."

#: option.c:456
msgid "Retry DNS queries after this many milliseconds."
msgstr ""

#: option.c:457
#, c-format
msgid "Change to this user after startup. (defaults to %s)."
msgstr "Zmiana użytkownika procesu na wskazanego (po uruchomieniu, domyślnie: %s)."

#: option.c:458
msgid "Map DHCP vendor class to tag."
msgstr "Przyporządkowanie znacznika w zależności od typu klienta DHCP."

#: option.c:459
msgid "Display dnsmasq version and copyright information."
msgstr "Wydrukowanie informacji o programie i ochronie praw autorskich."

#: option.c:460
msgid "Translate IPv4 addresses from upstream servers."
msgstr "Tłumaczenie adresów IPv4 z serwerów nadrzędnych."

#: option.c:461
msgid "Specify a SRV record."
msgstr "Określenie rekordu SRV."

#: option.c:462
msgid "Display this message. Use --help dhcp or --help dhcp6 for known DHCP options."
msgstr "Wyświetla ten komunikat. Chcąc przejrzeć listę dostępnych opcji DHCP użyj '--help dhcp' lub '--help dhcp6' ."

#: option.c:463
#, c-format
msgid "Specify path of PID file (defaults to %s)."
msgstr "Określenie ścieżki do pliku PID (domyślnie: %s)."

#: option.c:464
#, c-format
msgid "Specify maximum number of DHCP leases (defaults to %s)."
msgstr "Maksymalna liczba dzierżaw DHCP (domyślnie: %s)."

#: option.c:465
msgid "Answer DNS queries based on the interface a query was sent to."
msgstr "Uzależnienie odpowiedzi DNS od interfejsu, na którym odebrano zapytanie (wygodne dla serwerów kilku podsieci z różnymi adresami w /etc/hosts)."

#: option.c:466
msgid "Specify TXT DNS record."
msgstr "Specyfikacja rekordu DNS TXT."

#: option.c:467
msgid "Specify PTR DNS record."
msgstr "Specyfikacja rekordu DNS PTR."

#: option.c:468
msgid "Give DNS name to IPv4 address of interface."
msgstr "Zwraca nazwę domenową powiązaną z adresem interfejsu sieciowego."

#: option.c:469
msgid "Bind only to interfaces in use."
msgstr "Nasłuchiwanie tylko na wykorzystywanych interfejsach (umożliwia uruchomienie osobnych serwerów dla różnych kart)."

#: option.c:470
#, c-format
msgid "Read DHCP static host information from %s."
msgstr "Wczytanie przyporządkowań adresów z %s."

#: option.c:471
msgid "Enable the DBus interface for setting upstream servers, etc."
msgstr "Włączenie używania interfejsu DBus do informowania o zmianach konfiguracji."

#: option.c:472
msgid "Enable the UBus interface."
msgstr ""

#: option.c:473
msgid "Do not provide DHCP on this interface, only provide DNS."
msgstr "Uruchomienie na wskazanym interfejsie tylko DNS-a, bez usług DHCP i TFTP."

#: option.c:474
msgid "Enable dynamic address allocation for bootp."
msgstr "Włączenie dynamicznego przydzielania adresów dla klientów BOOTP."

#: option.c:475
msgid "Map MAC address (with wildcards) to option set."
msgstr "Przyporządkowanie znacznika w zależności od adresu MAC (można używać uogólnień: *)."

#: option.c:476
msgid "Treat DHCP requests on aliases as arriving from interface."
msgstr "Traktowanie żądań DHCP odebranych na interfejsach alias, ..., jako odebranych na iface."

#: option.c:477
msgid "Specify extra networks sharing a broadcast domain for DHCP"
msgstr ""

#: option.c:478
msgid "Disable ICMP echo address checking in the DHCP server."
msgstr "Pominięcie sprawdzania za pomocą ICMP niezajętości adresu przed jego wydzierżawieniem."

#: option.c:479
msgid "Shell script to run on DHCP lease creation and destruction."
msgstr "Skrypt powłoki uruchamiany po przyznaniu lub zwolnieniu adresu."

#: option.c:480
msgid "Lua script to run on DHCP lease creation and destruction."
msgstr "Skrypt Lua uruchamiany po przyznaniu lub zwolnieniu adresu."

#: option.c:481
msgid "Run lease-change scripts as this user."
msgstr "Wskazanie użytkownika z którego uprawnieniami będą uruchamiane skrypty."

#: option.c:482
msgid "Call dhcp-script with changes to local ARP table."
msgstr "Wywoływanie dhcp-script w reakcji na zmiany w tablicy ARP."

#: option.c:483
msgid "Read configuration from all the files in this directory."
msgstr "Wczytanie wszystkich plików ze wskazanego katalogu jako konfiguracyjnych."

#: option.c:484
msgid "Execute file and read configuration from stdin."
msgstr ""

#: option.c:485
msgid "Log to this syslog facility or file. (defaults to DAEMON)"
msgstr "Wskazanie kanału syslog-a do którego mają trafiać komunikaty (domyślnie: DAEMON)"

#: option.c:486
msgid "Do not use leasefile."
msgstr "Nieużywanie bazy dzierżaw."

#: option.c:487
#, c-format
msgid "Maximum number of concurrent DNS queries. (defaults to %s)"
msgstr "Maksymalna liczba jednocześnie obsługiwanych zapytań DNS (domyślnie: %s)"

#: option.c:488
#, c-format
msgid "Clear DNS cache when reloading %s."
msgstr "Czyszczenie pamięci podręcznej serwera nazw w przypadku ponownego odczytu %s."

#: option.c:489
msgid "Ignore hostnames provided by DHCP clients."
msgstr "Nie zwracanie uwagi na nazwę podawaną przez klienta w przypadku dopasowania wszystkich wymienionych znaczników."

#: option.c:490
msgid "Do NOT reuse filename and server fields for extra DHCP options."
msgstr "Wyłączenie oszczędzania miejsca w pakiecie DHCP przez przesuwanie pól servername i filename do opcji DHCP. Wymusza prostszy tryb budowy pakietu rozwiązując problemy z nieprzystosowanymi klientami DHCP."

#: option.c:491
msgid "Enable integrated read-only TFTP server."
msgstr "Włączenie wbudowanego serwera TFTP (tylko do wysyłania)."

#: option.c:492
msgid "Export files by TFTP only from the specified subtree."
msgstr "Ograniczenie działania serwera TFTP do wskazanego katalogu i podkatalogów. Nazwy z .. są odrzucane, / odnosi się do wskazanego katalogu."

#: option.c:493
#, fuzzy
msgid "Add client IP or hardware address to tftp-root."
msgstr "Doklejanie adresu IP klienta do głównego katalogu TFTP. Jeżeli wynikowy katalog nie istnieje, nadal wykorzystuje się tftp-root."

#: option.c:494
msgid "Allow access only to files owned by the user running dnsmasq."
msgstr "Ograniczenie dostępu do plików przez TFTP do tych, których właścicielem jest użytkownik uruchamiający dnsmasq-a."

#: option.c:495
msgid "Do not terminate the service if TFTP directories are inaccessible."
msgstr "Nieprzerywanie działania serwisu mimo braku dostępu do katalogów TFTP."

#: option.c:496
#, fuzzy, c-format
msgid "Maximum number of concurrent TFTP transfers (defaults to %s)."
msgstr "Maksymalna liczba jednocześnie obsługiwanych połączeń TFTP (domyślnie %s)."

#: option.c:497
msgid "Maximum MTU to use for TFTP transfers."
msgstr "Ograniczenie MTU w komunikacji TFTP."

#: option.c:498
msgid "Disable the TFTP blocksize extension."
msgstr "Wyłączenie możliwości negocjowania wielkości bloku dla przesyłów przez TFTP."

#: option.c:499
msgid "Convert TFTP filenames to lowercase"
msgstr "Konwertowanie nazw plików żądanych przez TFTP do małych liter"

#: option.c:500
msgid "Ephemeral port range for use by TFTP transfers."
msgstr "Wskazanie zakresu portów do użytku TFTP."

#: option.c:501
msgid "Use only one port for TFTP server."
msgstr ""

#: option.c:502
msgid "Extra logging for DHCP."
msgstr "Włączenie spisywania w logu operacji DHCP."

#: option.c:503
msgid "Enable async. logging; optionally set queue length."
msgstr "Włączenie asynchronicznego zapisywania do logu z ewentualnym wskazaniem długości kolejki."

#: option.c:504
msgid "Stop DNS rebinding. Filter private IP ranges when resolving."
msgstr "Odfiltrowywanie adresów wskazujących na komputery w sieciach wewnętrznych spośród odpowiedzi od zewnętrznych serwerów DNS."

#: option.c:505
msgid "Allow rebinding of *********/8, for RBL servers."
msgstr "Zezwolenie na przekazywanie odpowiedzi w klasie *********/8. Dla serwerów RBL."

#: option.c:506
msgid "Inhibit DNS-rebind protection on this domain."
msgstr "Dezaktywacja zabezpieczenia przed atakami DNS-rebind dla wskazanych domen."

#: option.c:507
msgid "Always perform DNS queries to all servers."
msgstr "Jednoczesne odpytywanie wszystkich serwerów nadrzędnych; klientowi przekazywana jest pierwsza odpowiedź."

#: option.c:508
msgid "Set tag if client includes matching option in request."
msgstr "Ustawienie znacznika jeżeli w żądaniu DHCP pojawi się wskazana opcja, ewentualnie o konkretnej wartości."

#: option.c:509
#, fuzzy
msgid "Set tag if client provides given name."
msgstr "Ustawienie znacznika jeżeli w żądaniu DHCP pojawi się wskazana opcja, ewentualnie o konkretnej wartości."

#: option.c:510
msgid "Use alternative ports for DHCP."
msgstr "Użycie alternatywnych portów dla usługi DHCP."

#: option.c:511
msgid "Specify NAPTR DNS record."
msgstr "Specyfikacja rekordu DNS NAPTR."

#: option.c:512
msgid "Specify lowest port available for DNS query transmission."
msgstr "Ustawienie dolnej granicy numerów portów do przesyłania zapytań DNS."

#: option.c:513
msgid "Specify highest port available for DNS query transmission."
msgstr "Ograniczenie najwyższego numeru portu dla transmisji zapytań DNS."

#: option.c:514
msgid "Use only fully qualified domain names for DHCP clients."
msgstr "Przechowywanie w serwerze DNS dnsmasq-a tylko w pełni kwalifikowanych nazw zgłaszanych przez klientów DHCP."

#: option.c:515
msgid "Generate hostnames based on MAC address for nameless clients."
msgstr "Generowanie nazw na podstawie MAC-adresów dla klientów bez nazwy."

#: option.c:516
msgid "Use these DHCP relays as full proxies."
msgstr "Traktowanie wskazanych serwerów pośredniczących DHCP jako działających w trybie \"pełnomocnika\" (full-proxy)."

#: option.c:517
msgid "Relay DHCP requests to a remote server"
msgstr "Przekazywanie żądań DHCP do zdalnego serwera"

#: option.c:518
msgid "Specify alias name for LOCAL DNS name."
msgstr "Wskazanie synonimu nazwy komputera lokalnego - znanego z /etc/hosts albo z DHCP."

#: option.c:519
msgid "Prompt to send to PXE clients."
msgstr "Zgłoszenie wysyłane klientom PXE."

#: option.c:520
msgid "Boot service for PXE menu."
msgstr "Składnik menu PXE (--> man)."

#: option.c:521
msgid "Check configuration syntax."
msgstr "Sprawdzenie składni."

#: option.c:522
msgid "Add requestor's MAC address to forwarded DNS queries."
msgstr "Przekazywanie MAC-adresu komputera pytającego w ruchu wychodzącym DNS."

#: option.c:523
msgid "Strip MAC information from queries."
msgstr ""

#: option.c:524
msgid "Add specified IP subnet to forwarded DNS queries."
msgstr "Zamieszczanie wskazanego adresu podsieci w przekazywanych zapytaniach DNS."

#: option.c:525
msgid "Strip ECS information from queries."
msgstr ""

#: option.c:526
msgid "Add client identification to forwarded DNS queries."
msgstr "Zamieszczanie identyfikacji pytającego w przekazywanych zapytaniach DNS."

#: option.c:527
msgid "Proxy DNSSEC validation results from upstream nameservers."
msgstr "Przekazywanie wyników weryfikacji DNSSEC z serwerów nadrzędnych."

#: option.c:528
msgid "Attempt to allocate sequential IP addresses to DHCP clients."
msgstr "Zmiana sposobu przydzielania adresów IP na sekwencyjny."

#: option.c:529
#, fuzzy
msgid "Ignore client identifier option sent by DHCP clients."
msgstr "Nie zwracanie uwagi na nazwę podawaną przez klienta w przypadku dopasowania wszystkich wymienionych znaczników."

#: option.c:530
msgid "Copy connection-track mark from queries to upstream connections."
msgstr "Zachowanie znacznika połączenia z odebranego zapytania DNS w ruchu zewnętrznym."

#: option.c:531
msgid "Allow DHCP clients to do their own DDNS updates."
msgstr "Zezwolenie klientom DHCP na uaktualnianie DDNS-ów."

#: option.c:532
msgid "Send router-advertisements for interfaces doing DHCPv6"
msgstr "Załączenie anonsowania (RA) na interfejsach serwujących DHCPv6"

#: option.c:533
msgid "Specify DUID_EN-type DHCPv6 server DUID"
msgstr "Określenie DHCPv6 DUID"

#: option.c:534
msgid "Specify host (A/AAAA and PTR) records"
msgstr "Określenie rekordów A/AAAA i PTR"

#: option.c:535
msgid "Specify host record in interface subnet"
msgstr ""

#: option.c:536
msgid "Specify certification authority authorization record"
msgstr ""

#: option.c:537
msgid "Specify arbitrary DNS resource record"
msgstr "Określenie rekordu TXT"

#: option.c:538
msgid "Bind to interfaces in use - check for new interfaces"
msgstr "Dynamiczne podpinanie do interfejsów sieciowych"

#: option.c:539
msgid "Export local names to global DNS"
msgstr "Eksportowanie lokalnych nazw hostów do globalnego DNS-a"

#: option.c:540
msgid "Domain to export to global DNS"
msgstr "Domena pod którą będą eksportowane lokalne nazwy"

#: option.c:541
msgid "Set TTL for authoritative replies"
msgstr "Określenie TTL dla odpowiedzi autorytatywnych"

#: option.c:542
#, fuzzy
msgid "Set authoritative zone information"
msgstr "Określenie danych strefy autorytatywnej (SOA)"

#: option.c:543
msgid "Secondary authoritative nameservers for forward domains"
msgstr "Pomocnicze serwery autorytatywne dla forwardowanych domen"

#: option.c:544
msgid "Peers which are allowed to do zone transfer"
msgstr "Wskazanie serwerów uprawnionych do transferu stref"

#: option.c:545
msgid "Specify ipsets to which matching domains should be added"
msgstr "Wyszczególnienie ipset-ów, do których będą dopisywane adresy IP leżące we wskazanych domenach"

#: option.c:546
#, fuzzy
msgid "Specify nftables sets to which matching domains should be added"
msgstr "Wyszczególnienie ipset-ów, do których będą dopisywane adresy IP leżące we wskazanych domenach"

#: option.c:547
msgid "Enable filtering of DNS queries with connection-track marks."
msgstr ""

#: option.c:548
msgid "Set allowed DNS patterns for a connection-track mark."
msgstr ""

#: option.c:549
msgid "Specify a domain and address range for synthesised names"
msgstr "Wskazanie domeny i zakresu adresów dla generowanych nazw"

#: option.c:550
msgid "Activate DNSSEC validation"
msgstr "Uaktywnienie walidacji DNSSEC"

#: option.c:551
msgid "Specify trust anchor key digest."
msgstr "Wskazanie punktu zaufania dla uwierzytelniania DNSSEC."

#: option.c:552
msgid "Disable upstream checking for DNSSEC debugging."
msgstr "Akceptowanie nieuwiarygodnionych odpowiedzi DNSSEC (ustawienie bitu CD w zapytaniach)."

#: option.c:553
msgid "Ensure answers without DNSSEC are in unsigned zones."
msgstr "Upewnianie się, że odpowiedzi bez DNSSEC pochodzą ze stref niepodpisanych."

#: option.c:554
msgid "Don't check DNSSEC signature timestamps until first cache-reload"
msgstr "Wyłączenie sprawdzania sygnatur czasowych DNSSEC do pierwszego przeładowania pamięci podręcznej."

#: option.c:555
msgid "Timestamp file to verify system clock for DNSSEC"
msgstr "Plik znacznika czasu do weryfikacji zegara systemowego dla potrzeb DNSSEC."

#: option.c:556
#, fuzzy
msgid "Set MTU, priority, resend-interval and router-lifetime"
msgstr "Ustawianie priorytetu, okresu rozsyłania oraz czasu życia rutera (RA)."

#: option.c:557
msgid "Do not log routine DHCP."
msgstr "Wyłączenie logowania zwyczajnego DHCP."

#: option.c:558
msgid "Do not log routine DHCPv6."
msgstr "Wyłączenie logowania zwyczajnego DHCPv6."

#: option.c:559
msgid "Do not log RA."
msgstr "Wyłączenie logowania RA."

#: option.c:560
msgid "Log debugging information."
msgstr ""

#: option.c:561
msgid "Accept queries only from directly-connected networks."
msgstr "Akceptowanie zapytań wyłącznie z sieci podpiętych bezpośrednio."

#: option.c:562
msgid "Detect and remove DNS forwarding loops."
msgstr "Wykrywanie i usuwanie pętli zapytań DNS."

#: option.c:563
msgid "Ignore DNS responses containing ipaddr."
msgstr "Ignorowanie odpowiedzi DNS zawierających ipaddr."

#: option.c:564
msgid "Set TTL in DNS responses with DHCP-derived addresses."
msgstr "Ustawienie TTL w odpowiedziach DNS dla adresów przydzielonych przez DHCP."

#: option.c:565
msgid "Delay DHCP replies for at least number of seconds."
msgstr ""

#: option.c:566
msgid "Enables DHCPv4 Rapid Commit option."
msgstr ""

#: option.c:567
msgid "Path to debug packet dump file"
msgstr ""

#: option.c:568
msgid "Mask which packets to dump"
msgstr ""

#: option.c:569
#, fuzzy
msgid "Call dhcp-script when lease expiry changes."
msgstr "Wywoływanie dhcp-script w reakcji na zmiany w tablicy ARP."

#: option.c:570
msgid "Send Cisco Umbrella identifiers including remote IP."
msgstr ""

#: option.c:571
#, fuzzy
msgid "Do not log routine TFTP."
msgstr "Wyłączenie logowania zwyczajnego DHCP."

#: option.c:572
msgid "Suppress round-robin ordering of DNS records."
msgstr ""

#: option.c:802
#, c-format
msgid ""
"Usage: dnsmasq [options]\n"
"\n"
msgstr ""
"Użycie: dnsmasq [opcje]\n"
"\n"

#: option.c:804
#, c-format
msgid "Use short options only on the command line.\n"
msgstr "W tym systemie w linii poleceń można używać wyłącznie jednoliterowych opcji.\n"

#: option.c:806
#, c-format
msgid "Valid options are:\n"
msgstr "Dostępne opcje:\n"

#: option.c:853 option.c:1055
msgid "bad address"
msgstr "zły adres"

#: option.c:882 option.c:886
msgid "bad port"
msgstr "nieprawidłowy numer portu"

#: option.c:899 option.c:1002 option.c:1048
msgid "interface binding not supported"
msgstr "nie ma możliwości dowiązywania do interfejsu"

#: option.c:955
msgid "Cannot resolve server name"
msgstr ""

#: option.c:991
msgid "cannot use IPv4 server address with IPv6 source address"
msgstr ""

#: option.c:997 option.c:1043
msgid "interface can only be specified once"
msgstr ""

#: option.c:1011 option.c:4785
msgid "bad interface name"
msgstr "nieprawidłowa nazwa interfejsu"

#: option.c:1037
msgid "cannot use IPv6 server address with IPv4 source address"
msgstr ""

#: option.c:1124
#, fuzzy
msgid "bad IPv4 prefix length"
msgstr "zła maska"

#: option.c:1155 option.c:1165 option.c:1240 option.c:1250 option.c:5360
msgid "error"
msgstr "błąd"

#: option.c:1207
#, fuzzy
msgid "bad IPv6 prefix length"
msgstr "zła maska"

#: option.c:1467
msgid "inappropriate vendor:"
msgstr ""

#: option.c:1474
msgid "inappropriate encap:"
msgstr ""

#: option.c:1500
msgid "unsupported encapsulation for IPv6 option"
msgstr "nieobsługiwany rodzaj enkapsulacji opcji IPv6"

#: option.c:1514
msgid "bad dhcp-option"
msgstr "błąd w dhcp-option"

#: option.c:1592
msgid "bad IP address"
msgstr "zły adres IP"

#: option.c:1595 option.c:1734 option.c:3928
msgid "bad IPv6 address"
msgstr "zły adres IPv6"

#: option.c:1688
msgid "bad IPv4 address"
msgstr "nieprawidłowy adres IPv4"

#: option.c:1761 option.c:1856
msgid "bad domain in dhcp-option"
msgstr "nieprawidłowa nazwa domeny w dhcp-option"

#: option.c:1900
msgid "dhcp-option too long"
msgstr "zbyt długa dhcp-option (>255 znaków)"

#: option.c:1907
msgid "illegal dhcp-match"
msgstr "niedopuszczalne dhcp-match"

#: option.c:1966
msgid "illegal repeated flag"
msgstr "wielokrotne użycie opcji niedozwolone (pojawiła się wcześniej w linii poleceń)"

#: option.c:1974
msgid "illegal repeated keyword"
msgstr "wielokrotne użycie opcji niedozwolone (pojawiła się wsześniej w pliku konfiguracyjnym)"

#: option.c:2056 option.c:5533
#, c-format
msgid "cannot access directory %s: %s"
msgstr "brak dostępu do katalogu %s: %s"

#: option.c:2102 tftp.c:573 dump.c:72
#, c-format
msgid "cannot access %s: %s"
msgstr "brak dostępu do %s: %s"

#: option.c:2219
msgid "setting log facility is not possible under Android"
msgstr "zmiana log-facility w systemie Android nie jest możliwa"

#: option.c:2228
msgid "bad log facility"
msgstr "nierozpoznany znacznik logów"

#: option.c:2281
msgid "bad MX preference"
msgstr "nieprawidłowa wartość preferencji MX"

#: option.c:2289
msgid "bad MX name"
msgstr "nieprawidłowa nazwa MX"

#: option.c:2304
msgid "bad MX target"
msgstr "nieprawidłowa wartość celu MX"

#: option.c:2324
msgid "recompile with HAVE_SCRIPT defined to enable lease-change scripts"
msgstr "żeby mieć możliwość używania skryptów wywoływanych przy zmianie dzierżawy, przekompiluj dnsmasq-a z włączoną flagą HAVE_SCRIPT"

#: option.c:2328
msgid "recompile with HAVE_LUASCRIPT defined to enable Lua scripts"
msgstr "używanie skryptów Lua, wymaga skompilowania dnsmasq-a z flagą HAVE_LUASCRIPT"

#: option.c:2447
#, fuzzy
msgid "invalid auth-zone"
msgstr "nieprawidłowy zakres adresów w --alias"

#: option.c:2589 option.c:2621
#, fuzzy
msgid "bad prefix length"
msgstr "zła maska"

#: option.c:2601 option.c:2642 option.c:2696
msgid "bad prefix"
msgstr "zła maska"

#: option.c:2716
#, fuzzy
msgid "prefix length too small"
msgstr "długość prefiksu musi wynosić co najmniej 64"

#: option.c:3010
#, fuzzy
msgid "Bad address in --address"
msgstr "adres jest w użyciu"

#: option.c:3110
msgid "recompile with HAVE_IPSET defined to enable ipset directives"
msgstr "chcąc korzystać z ipsets przekompiluj dnsmasq-a z HAVE_IPSET"

#: option.c:3117
#, fuzzy
msgid "recompile with HAVE_NFTSET defined to enable nftset directives"
msgstr "chcąc korzystać z ipsets przekompiluj dnsmasq-a z HAVE_IPSET"

#: option.c:3192 option.c:3210
#, fuzzy
msgid "recompile with HAVE_CONNTRACK defined to enable connmark-allowlist directives"
msgstr "chcąc korzystać z ipsets przekompiluj dnsmasq-a z HAVE_IPSET"

#: option.c:3496
msgid "bad port range"
msgstr "nieprawidłowy zakres numerów portów"

#: option.c:3522
msgid "bad bridge-interface"
msgstr "nieprawidłowa nazwa urządzenia w bridge-interface"

#: option.c:3566
msgid "bad shared-network"
msgstr ""

#: option.c:3620
msgid "only one tag allowed"
msgstr "można wskazać tylko jeden znacznik sieci"

#: option.c:3641 option.c:3657 option.c:3783 option.c:3791 option.c:3834
msgid "bad dhcp-range"
msgstr "nieprawidłowy zakres dhcp-range"

#: option.c:3675
msgid "inconsistent DHCP range"
msgstr "niespójny zakres adresów DHCP"

#: option.c:3741
msgid "prefix length must be exactly 64 for RA subnets"
msgstr "długość prefiksu musi wynosić dokładnie 64 dla podsieci RA"

#: option.c:3743
msgid "prefix length must be exactly 64 for subnet constructors"
msgstr "długość prefiksu musi wynosić dokładnie 64 dla konstruktorów podsieci"

#: option.c:3746
msgid "prefix length must be at least 64"
msgstr "długość prefiksu musi wynosić co najmniej 64"

#: option.c:3749
msgid "inconsistent DHCPv6 range"
msgstr "niespójny zakres adresów DHCPv6"

#: option.c:3768
msgid "prefix must be zero with \"constructor:\" argument"
msgstr "prefiks musi wynosić zero z argumentem \"constructor:\""

#: option.c:3893 option.c:3971
msgid "bad hex constant"
msgstr "zapis niezgodny z formatem szesnastkowym"

#: option.c:3946
#, fuzzy
msgid "bad IPv6 prefix"
msgstr "zła maska"

#: option.c:3994
#, c-format
msgid "duplicate dhcp-host IP address %s"
msgstr "powtórzony adres IP %s w specyfikacji dhcp-host"

#: option.c:4056
msgid "bad DHCP host name"
msgstr "niedopuszczalna nazwa komputera w dhcp-host"

#: option.c:4142
msgid "bad tag-if"
msgstr "nieprawidłowa składnia 'tag-if'"

#: option.c:4490 option.c:5046
msgid "invalid port number"
msgstr "nieprawidłowy numer portu"

#: option.c:4546
msgid "bad dhcp-proxy address"
msgstr "zły adres dhcp-proxy"

#: option.c:4627
msgid "Bad dhcp-relay"
msgstr "zły dhcp-relay"

#: option.c:4671
msgid "bad RA-params"
msgstr "nieprawidłowe argumenty RA"

#: option.c:4681
msgid "bad DUID"
msgstr "zły DUID"

#: option.c:4715
#, fuzzy
msgid "missing address in alias"
msgstr "niepoprawny adres"

#: option.c:4721
msgid "invalid alias range"
msgstr "nieprawidłowy zakres adresów w --alias"

#: option.c:4770
#, fuzzy
msgid "missing address in dynamic host"
msgstr "niepoprawny adres"

#: option.c:4785
#, fuzzy
msgid "bad dynamic host"
msgstr "zły katalog dynamiczny %s: %s"

#: option.c:4803 option.c:4819
msgid "bad CNAME"
msgstr "zła CNAME"

#: option.c:4827
msgid "duplicate CNAME"
msgstr "powtórzona CNAME"

#: option.c:4854
msgid "bad PTR record"
msgstr "nieprawidłowy zapis rekordu PTR"

#: option.c:4889
msgid "bad NAPTR record"
msgstr "nieprawidłowy zapis rekordu NAPTR"

#: option.c:4925
msgid "bad RR record"
msgstr "nieprawidłowy zapis rekordu RR"

#: option.c:4958
#, fuzzy
msgid "bad CAA record"
msgstr "nieprawidłowy zapis rekordu RR"

#: option.c:4987
msgid "bad TXT record"
msgstr "nieprawidłowy zapis rekordu TXT"

#: option.c:5030
msgid "bad SRV record"
msgstr "nieprawidłowy zapis rekordu SRV"

#: option.c:5037
msgid "bad SRV target"
msgstr "nieprawidłowa wartość celu SRV"

#: option.c:5056
msgid "invalid priority"
msgstr "nieprawidłowy priorytet"

#: option.c:5061
msgid "invalid weight"
msgstr "nieprawidłowa waga"

#: option.c:5084
msgid "Bad host-record"
msgstr "nieprawidłowy zapis host-record"

#: option.c:5123
msgid "Bad name in host-record"
msgstr "niedopuszczalna nazwa w host-record"

#: option.c:5165
msgid "bad value for dnssec-check-unsigned"
msgstr ""

#: option.c:5201
msgid "bad trust anchor"
msgstr "nieprawidłowa specyfikacja punktu zaufania"

#: option.c:5217
msgid "bad HEX in trust anchor"
msgstr "zły zapis szesnastkowy"

#: option.c:5228
msgid "unsupported option (check that dnsmasq was compiled with DHCP/TFTP/DNSSEC/DBus support)"
msgstr "nieobsługiwana opcja (sprawdź, czy obsługa DHCP/TFTP/DNSSEC/DBus została wkompilowana)"

#: option.c:5290
msgid "missing \""
msgstr "brakuje \""

#: option.c:5347
msgid "bad option"
msgstr "nieprawidłowa opcja"

#: option.c:5349
msgid "extraneous parameter"
msgstr "nadwyżkowy parametr"

#: option.c:5351
msgid "missing parameter"
msgstr "brak parametru"

#: option.c:5353
msgid "illegal option"
msgstr "niedopuszczalna opcja"

#: option.c:5363
#, c-format
msgid " in output from %s"
msgstr ""

#: option.c:5365
#, c-format
msgid " at line %d of %s"
msgstr " w linii %d pliku %s"

#: option.c:5380 option.c:5683 option.c:5694
#, c-format
msgid "read %s"
msgstr "przeczytałem %s"

#: option.c:5446
#, fuzzy, c-format
msgid "cannot execute %s: %s"
msgstr "błąd odczytu z pliku %s: %s"

#: option.c:5454 option.c:5615 tftp.c:790
#, c-format
msgid "cannot read %s: %s"
msgstr "błąd odczytu z pliku %s: %s"

#: option.c:5473
#, fuzzy, c-format
msgid "error executing %s: %s"
msgstr "nie udało się uruchomić %s: %s"

#: option.c:5476
#, c-format
msgid "%s returns non-zero error code"
msgstr ""

#: option.c:5775
msgid "junk found in command line"
msgstr "jakieś śmieci w linii poleceń"

#: option.c:5815
#, c-format
msgid "Dnsmasq version %s  %s\n"
msgstr "Dnsmasq, wersja %s  %s\n"

#: option.c:5816
#, c-format
msgid ""
"Compile time options: %s\n"
"\n"
msgstr ""
"Wkompilowane opcje %s\n"
"\n"

#: option.c:5817
#, c-format
msgid "This software comes with ABSOLUTELY NO WARRANTY.\n"
msgstr "Autor nie daje ŻADNYCH GWARANCJI egzekwowalnych prawnie.\n"

#: option.c:5818
#, c-format
msgid "Dnsmasq is free software, and you are welcome to redistribute it\n"
msgstr "Dnsmasq jest wolnym oprogramowaniem, możesz go rozprowadzać\n"

#: option.c:5819
#, c-format
msgid "under the terms of the GNU General Public License, version 2 or 3.\n"
msgstr "na warunkach określonych w GNU General Public Licence, w wersji 2 lub 3.\n"

#: option.c:5836
msgid "try --help"
msgstr "spróbuj: --help"

#: option.c:5838
msgid "try -w"
msgstr "spróbuj: -w"

#: option.c:5840
#, c-format
msgid "bad command line options: %s"
msgstr "nieprawidłowa opcja w linii poleceń %s"

#: option.c:5909
#, c-format
msgid "CNAME loop involving %s"
msgstr ""

#: option.c:5950
#, c-format
msgid "cannot get host-name: %s"
msgstr "nie można pobrać nazwy hosta: %s"

#: option.c:5978
msgid "only one resolv.conf file allowed in no-poll mode."
msgstr "w trybie no-poll można wskazać najwyżej jeden plik resolv.conf."

#: option.c:5988
msgid "must have exactly one resolv.conf to read domain from."
msgstr "musisz mieć dokładnie jeden plik resolv.conf do odczytu domen."

#: option.c:5991 network.c:1727 dhcp.c:892
#, c-format
msgid "failed to read %s: %s"
msgstr "nie udało się odczytać %s: %s"

#: option.c:6008
#, c-format
msgid "no search directive found in %s"
msgstr "brak wytycznych wyszukiwania w %s"

#: option.c:6029
msgid "there must be a default domain when --dhcp-fqdn is set"
msgstr "w przypadku używania --dhcp-fqdn trzeba wskazać domyślną domenę"

#: option.c:6038
msgid "syntax check OK"
msgstr "składnia sprawdzona, jest prawidłowa"

#: forward.c:107
#, c-format
msgid "failed to send packet: %s"
msgstr "wysyłanie pakietu nie powiodło się: %s"

#: forward.c:715
msgid "discarding DNS reply: subnet option mismatch"
msgstr "odrzucam odpowiedź DNS: nie zgadza się specyfikacja podsieci"

#: forward.c:780
#, c-format
msgid "nameserver %s refused to do a recursive query"
msgstr "serwer nazw %s odmawia wykonania zapytania rekurencyjnego"

#: forward.c:826
#, c-format
msgid "possible DNS-rebind attack detected: %s"
msgstr "prawdopodobnie wykryto atak DNS-rebind: %s"

#: forward.c:1239
#, c-format
msgid "reducing DNS packet size for nameserver %s to %d"
msgstr ""

#: forward.c:1565
#, fuzzy, c-format
msgid "ignoring query from non-local network %s (logged only once)"
msgstr "Ignorowanie zapytań z sieci pozalokalnych."

#: forward.c:2139
#, fuzzy, c-format
msgid "ignoring query from non-local network %s"
msgstr "Ignorowanie zapytań z sieci pozalokalnych."

#: forward.c:2501
#, fuzzy, c-format
msgid "failed to bind server socket to %s: %s"
msgstr "błąd przy przyznawaniu nazwy gniazdu serwera %s: %s"

#: forward.c:2867
#, c-format
msgid "Maximum number of concurrent DNS queries reached (max: %d)"
msgstr "Osiągnięto graniczną ilość jednocześnie obsługiwanych zapytań DNS (maks: %d)"

#: forward.c:2869
#, fuzzy, c-format
msgid "Maximum number of concurrent DNS queries to %s reached (max: %d)"
msgstr "Osiągnięto graniczną ilość jednocześnie obsługiwanych zapytań DNS (maks: %d)"

#: network.c:700
#, c-format
msgid "stopped listening on %s(#%d): %s port %d"
msgstr ""

#: network.c:911
#, c-format
msgid "failed to create listening socket for %s: %s"
msgstr "nie udało się otworzyć gniazda %s: %s"

#: network.c:1192
#, c-format
msgid "listening on %s(#%d): %s port %d"
msgstr ""

#: network.c:1219
#, fuzzy, c-format
msgid "listening on %s port %d"
msgstr "błąd wysyłania pliku %s do komputera %s"

#: network.c:1252
#, c-format
msgid "LOUD WARNING: listening on %s may accept requests via interfaces other than %s"
msgstr "UWAGA: nasłuchiwanie na %s może przyjmować żądania przychodzące przez interfejsy inne niż %s"

#: network.c:1259
msgid "LOUD WARNING: use --bind-dynamic rather than --bind-interfaces to avoid DNS amplification attacks via these interface(s)"
msgstr "UWAGA: zastosowanie --bind-dynamic zamiast --bind-interfaces daje ochronę przed atakami wzmocnienia DNS"

#: network.c:1268
#, fuzzy, c-format
msgid "warning: using interface %s instead"
msgstr "uwaga: %s niedostępny"

#: network.c:1277
#, c-format
msgid "warning: no addresses found for interface %s"
msgstr "uwaga: nie znaleziono adresu interfejsu %s"

#: network.c:1335
#, c-format
msgid "interface %s failed to join DHCPv6 multicast group: %s"
msgstr "interfejs %s nie pozwolił się przyłączyć do grupy rozgłoszeniowej DHCPv6: %s"

#: network.c:1340
msgid "try increasing /proc/sys/net/core/optmem_max"
msgstr "spróbuj podwyższyć /proc/sys/net/core/optmem_max"

#: network.c:1545
#, c-format
msgid "failed to bind server socket for %s: %s"
msgstr "błąd przy przyznawaniu nazwy gniazdu serwera %s: %s"

#: network.c:1622
#, c-format
msgid "ignoring nameserver %s - local interface"
msgstr "ignorowanie serwera nazw %s - interfejs lokalny"

#: network.c:1633
#, c-format
msgid "ignoring nameserver %s - cannot make/bind socket: %s"
msgstr "ignorowanie serwera nazw %s - nie można utworzyć/dowiązać gniazda: %s"

#: network.c:1643
msgid "more servers are defined but not logged"
msgstr ""

#: network.c:1654
msgid "(no DNSSEC)"
msgstr "(brak obsługi DNSSEC)"

#: network.c:1657
msgid "unqualified"
msgstr "niekwalifikowane(-a)"

#: network.c:1657
msgid "names"
msgstr "nazwy"

#: network.c:1659
msgid "default"
msgstr "domyślne"

#: network.c:1661
msgid "domain"
msgstr "domeny"

#: network.c:1663
#, fuzzy, c-format
msgid "using nameserver %s#%d for %s %s%s %s"
msgstr "używam serwera nazw %s#%d dla %s %s %s"

#: network.c:1667
#, c-format
msgid "NOT using nameserver %s#%d - query loop detected"
msgstr "NIE używam serwera nazw %s#%d - wykryto pętlę zapytań"

#: network.c:1670
#, c-format
msgid "using nameserver %s#%d(via %s)"
msgstr "używam serwera nazw %s#%d (przez %s)"

#: network.c:1672
#, c-format
msgid "using nameserver %s#%d"
msgstr "używam serwera nazw %s#%d"

#: network.c:1687
#, fuzzy, c-format
msgid "using only locally-known addresses for %s"
msgstr "używam adresów lokalnych tylko dla %s %s"

#: network.c:1690
#, fuzzy, c-format
msgid "using standard nameservers for %s"
msgstr "używam standardowych serwerów nazw dla %s %s"

#: network.c:1694
#, fuzzy, c-format
msgid "using %d more local addresses"
msgstr "używam o %d serwerów nazw więcej"

#: network.c:1696
#, c-format
msgid "using %d more nameservers"
msgstr "używam o %d serwerów nazw więcej"

#: dnsmasq.c:192
msgid "dhcp-hostsdir, dhcp-optsdir and hostsdir are not supported on this platform"
msgstr "dhcp-hostsdir, dhcp-optsdir i hostsdir nie znajdują zastosowania na tej platformie"

#: dnsmasq.c:207
msgid "no root trust anchor provided for DNSSEC"
msgstr "nie wskazano punktów zaufania dla DNSSEC"

#: dnsmasq.c:210
msgid "cannot reduce cache size from default when DNSSEC enabled"
msgstr "brak możliwości zmniejszenia pamięci podręcznej poniżej wielkości domyślnej w przypadku używania DNSSEC"

#: dnsmasq.c:212
msgid "DNSSEC not available: set HAVE_DNSSEC in src/config.h"
msgstr "obsługa DNSSEC niedostępna - ustaw HAVE_DNSSEC w src/config.h"

#: dnsmasq.c:218
msgid "TFTP server not available: set HAVE_TFTP in src/config.h"
msgstr "Serwer TFTP nie został wkompilowany -- ustaw HAVE_TFTP w src/config.h"

#: dnsmasq.c:225
msgid "cannot use --conntrack AND --query-port"
msgstr "--conntrack i --query-port wzajemnie się wykluczają"

#: dnsmasq.c:231
msgid "conntrack support not available: set HAVE_CONNTRACK in src/config.h"
msgstr "wsparcie przekazywania znaczników połączeń (conntrack) nie zostało wkompilowane - ustaw HAVE_CONNTRACK w src/config.h"

#: dnsmasq.c:236
msgid "asynchronous logging is not available under Solaris"
msgstr "zapis do logów w trybie asynchronicznym nie jest dostępny w Solarisie"

#: dnsmasq.c:241
msgid "asynchronous logging is not available under Android"
msgstr "zapis do logów w trybie asynchronicznym nie jest dostępny w Androidzie"

#: dnsmasq.c:246
msgid "authoritative DNS not available: set HAVE_AUTH in src/config.h"
msgstr "tryb autorytatywny DNS-a niedostępny - ustaw HAVE_AUTH w src/config.h"

#: dnsmasq.c:251
msgid "loop detection not available: set HAVE_LOOP in src/config.h"
msgstr "wykrywanie pętli zapytań nie zostało wkompilowane - ustaw HAVE_LOOP w src/config.h"

#: dnsmasq.c:256
#, fuzzy
msgid "Ubus not available: set HAVE_UBUS in src/config.h"
msgstr "Obsługa DBus nie została wkompilowana -- ustaw HAVE_DBUS w src/config.h"

#: dnsmasq.c:267
msgid "max_port cannot be smaller than min_port"
msgstr "max_port nie może być niższy niż min_port"

#: dnsmasq.c:271
msgid "port_limit must not be larger than available port range"
msgstr ""

#: dnsmasq.c:278
msgid "--auth-server required when an auth zone is defined."
msgstr ""

#: dnsmasq.c:283
msgid "zone serial must be configured in --auth-soa"
msgstr "za pomocą --auth-soa musi zostać ustawiony numer seryjny strefy"

#: dnsmasq.c:303
msgid "dhcp-range constructor not available on this platform"
msgstr "konstrukcja dhcp-range nie jest dostępna w tym systemie"

#: dnsmasq.c:377
msgid "cannot set --bind-interfaces and --bind-dynamic"
msgstr "--bind-interfaces i --bind-dynamic wzajemnie się wykluczają"

#: dnsmasq.c:380
#, c-format
msgid "failed to find list of interfaces: %s"
msgstr "błąd podczas tworzenia listy interfejsów sieciowych: %s"

#: dnsmasq.c:389
#, c-format
msgid "unknown interface %s"
msgstr "nieznany interfejs %s"

#: dnsmasq.c:396
#, c-format
msgid "failed to set SO_BINDTODEVICE on DHCP socket: %s"
msgstr "nie udało się ustawić SO_BINDTODEVICE gniazda DHCP: %s"

#: dnsmasq.c:440
#, fuzzy
msgid "Packet dumps not available: set HAVE_DUMP in src/config.h"
msgstr "Obsługa DBus nie została wkompilowana -- ustaw HAVE_DBUS w src/config.h"

#: dnsmasq.c:448 dnsmasq.c:1232
#, c-format
msgid "DBus error: %s"
msgstr "błąd DBus: %s"

#: dnsmasq.c:451
msgid "DBus not available: set HAVE_DBUS in src/config.h"
msgstr "Obsługa DBus nie została wkompilowana -- ustaw HAVE_DBUS w src/config.h"

#: dnsmasq.c:459 dnsmasq.c:1253
#, fuzzy, c-format
msgid "UBus error: %s"
msgstr "błąd DBus: %s"

#: dnsmasq.c:462
#, fuzzy
msgid "UBus not available: set HAVE_UBUS in src/config.h"
msgstr "Obsługa DBus nie została wkompilowana -- ustaw HAVE_DBUS w src/config.h"

#: dnsmasq.c:492
#, c-format
msgid "unknown user or group: %s"
msgstr "nieznany użytkownik lub grupa: %s"

#: dnsmasq.c:568
#, c-format
msgid "process is missing required capability %s"
msgstr ""

#: dnsmasq.c:600
#, c-format
msgid "cannot chdir to filesystem root: %s"
msgstr "nie potrafię wejść do głównego katalogu: %s"

#: dnsmasq.c:852
#, c-format
msgid "started, version %s DNS disabled"
msgstr "uruchomiony, wersja %s, DNS wyłączony"

#: dnsmasq.c:857
#, c-format
msgid "started, version %s cachesize %d"
msgstr "uruchomiony, wersja %s, %d miejsc w pamięci podręcznej"

#: dnsmasq.c:859
msgid "cache size greater than 10000 may cause performance issues, and is unlikely to be useful."
msgstr ""

#: dnsmasq.c:862
#, c-format
msgid "started, version %s cache disabled"
msgstr "uruchomiony, wersja %s, pamięć podręczna wyłączona"

#: dnsmasq.c:865
msgid "DNS service limited to local subnets"
msgstr "usługa DNS ograniczona do lokalnych podsieci"

#: dnsmasq.c:868
#, c-format
msgid "compile time options: %s"
msgstr "opcje kompilacji: %s"

#: dnsmasq.c:877
msgid "DBus support enabled: connected to system bus"
msgstr "obsługa DBus włączona, podłączono do serwera DBus"

#: dnsmasq.c:879
msgid "DBus support enabled: bus connection pending"
msgstr "obsługa DBus włączona, trwa podłączanie do serwera DBus"

#: dnsmasq.c:887
#, fuzzy
msgid "UBus support enabled: connected to system bus"
msgstr "obsługa DBus włączona, podłączono do serwera DBus"

#: dnsmasq.c:889
#, fuzzy
msgid "UBus support enabled: bus connection pending"
msgstr "obsługa DBus włączona, trwa podłączanie do serwera DBus"

#: dnsmasq.c:909
msgid "DNSSEC validation enabled but all unsigned answers are trusted"
msgstr ""

#: dnsmasq.c:911
msgid "DNSSEC validation enabled"
msgstr "walidacja DNSSEC włączona"

#: dnsmasq.c:915
#, fuzzy
msgid "DNSSEC signature timestamps not checked until receipt of SIGINT"
msgstr "sprawdzanie sygnatur czasowych DNSSEC wyłączone do czasu przeładowania pamięci podręcznej"

#: dnsmasq.c:918
msgid "DNSSEC signature timestamps not checked until system time valid"
msgstr "sprawdzanie sygnatur czasowych DNSSEC wyłączone do czasu zsynchronizowania się zegara systemowego"

#: dnsmasq.c:921
#, c-format
msgid "configured with trust anchor for %s keytag %u"
msgstr ""

#: dnsmasq.c:927
#, c-format
msgid "warning: failed to change owner of %s: %s"
msgstr "uwaga: nie udało się zmienić użytkownika pliku %s: %s"

#: dnsmasq.c:932
msgid "setting --bind-interfaces option because of OS limitations"
msgstr "ustawiam --bind-interfaces z powodu ograniczeń systemu operacyjnego"

#: dnsmasq.c:945
#, c-format
msgid "warning: interface %s does not currently exist"
msgstr "uwaga: interfejs %s nie jest włączony"

#: dnsmasq.c:950
msgid "warning: ignoring resolv-file flag because no-resolv is set"
msgstr "uwaga: ignoruję opcję resolv-file, ponieważ wybrano tryb no-resolv"

#: dnsmasq.c:953
msgid "warning: no upstream servers configured"
msgstr "uwaga: nie wskazano nadrzędnych serwerów DNS"

#: dnsmasq.c:957
#, c-format
msgid "asynchronous logging enabled, queue limit is %d messages"
msgstr "włączono asynchroniczny tryb zapisu do logów z kolejką na %d komunikatów"

#: dnsmasq.c:978
msgid "IPv6 router advertisement enabled"
msgstr "anonsowanie rutera IPv6 włączone"

#: dnsmasq.c:983
#, c-format
msgid "DHCP, sockets bound exclusively to interface %s"
msgstr "DHCP, gniazda dowiązane na wyłączność interfejsowi %s"

#: dnsmasq.c:1000
msgid "root is "
msgstr "z głównym katalogiem w "

#: dnsmasq.c:1000
msgid "enabled"
msgstr "włączony"

#: dnsmasq.c:1002
msgid "secure mode"
msgstr "w trybie bezpiecznym"

#: dnsmasq.c:1003
#, fuzzy
msgid "single port mode"
msgstr "nieprawidłowy numer portu"

#: dnsmasq.c:1006
#, c-format
msgid "warning: %s inaccessible"
msgstr "uwaga: %s niedostępny"

#: dnsmasq.c:1010
#, c-format
msgid "warning: TFTP directory %s inaccessible"
msgstr "uwaga: katalog TFTP %s nie jest dostępny"

#: dnsmasq.c:1036
#, c-format
msgid "restricting maximum simultaneous TFTP transfers to %d"
msgstr "ograniczam ilość jednoczesnych przesłań TFTP do %d"

#: dnsmasq.c:1095
#, fuzzy, c-format
msgid "error binding DHCP socket to device %s"
msgstr "Błąd wysyłania pakietu DHCP do %s: %s"

#: dnsmasq.c:1229
msgid "connected to system DBus"
msgstr "podłączono do DBus-a"

#: dnsmasq.c:1250
#, fuzzy
msgid "connected to system UBus"
msgstr "podłączono do DBus-a"

#: dnsmasq.c:1416
#, c-format
msgid "cannot fork into background: %s"
msgstr "nie potrafię przełączyć się do pracy w tle: %s"

#: dnsmasq.c:1420
#, c-format
msgid "failed to create helper: %s"
msgstr "nie udało się utworzyć procesu pomocniczego: %s"

#: dnsmasq.c:1424
#, c-format
msgid "setting capabilities failed: %s"
msgstr "nie powiodło się ustawianie ograniczeń (capabilities): %s"

#: dnsmasq.c:1428
#, c-format
msgid "failed to change user-id to %s: %s"
msgstr "nie udało się zmienić użytkownika procesu na %s: %s"

#: dnsmasq.c:1432
#, c-format
msgid "failed to change group-id to %s: %s"
msgstr "nie udało się zmienić grupy procesu na %s: %s"

#: dnsmasq.c:1436
#, c-format
msgid "failed to open pidfile %s: %s"
msgstr "nie udało się otworzyć pliku z PID-em %s: %s"

#: dnsmasq.c:1440
#, c-format
msgid "cannot open log %s: %s"
msgstr "nie udało się otworzyć logu %s: %s"

#: dnsmasq.c:1444
#, c-format
msgid "failed to load Lua script: %s"
msgstr "nie udało się wczytać skryptu Lua: %s"

#: dnsmasq.c:1448
#, c-format
msgid "TFTP directory %s inaccessible: %s"
msgstr "katalog TFTP %s nie jest dostępny: %s"

#: dnsmasq.c:1452
#, c-format
msgid "cannot create timestamp file %s: %s"
msgstr "nie potrafię utworzyć pliku znacznika czasu %s: %s"

#: dnsmasq.c:1536
#, c-format
msgid "script process killed by signal %d"
msgstr "skrypt został zabity sygnałem %d"

#: dnsmasq.c:1540
#, c-format
msgid "script process exited with status %d"
msgstr "skrypt zakończył się z kodem powrotu %d"

#: dnsmasq.c:1544
#, c-format
msgid "failed to execute %s: %s"
msgstr "nie udało się uruchomić %s: %s"

#: dnsmasq.c:1584
msgid "now checking DNSSEC signature timestamps"
msgstr "trwa sprawdzanie sygnatur czasowych podpisów DNSSEC"

#: dnsmasq.c:1619 dnssec.c:160 dnssec.c:204
#, c-format
msgid "failed to update mtime on %s: %s"
msgstr "nie udało się uaktualnić znacznika czasu pliku %s: %s"

#: dnsmasq.c:1631
msgid "exiting on receipt of SIGTERM"
msgstr "zakończyłem działanie z powodu odebrania SIGTERM"

#: dnsmasq.c:1659
#, c-format
msgid "failed to access %s: %s"
msgstr "brak dostępu do %s: %s"

#: dnsmasq.c:1690
#, c-format
msgid "reading %s"
msgstr "czytanie %s"

#: dnsmasq.c:1706
#, c-format
msgid "no servers found in %s, will retry"
msgstr "w %s nie znalazłem serwerów, spróbuję ponownie później"

#: dhcp.c:51
#, c-format
msgid "cannot create DHCP socket: %s"
msgstr "nie udało się utworzyć gniazda dla DHCP: %s"

#: dhcp.c:66
#, c-format
msgid "failed to set options on DHCP socket: %s"
msgstr "błąd podczas ustawiania opcji gniazda DHCP: %s"

#: dhcp.c:87
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCP socket: %s"
msgstr "nie udało się ustawić SO_REUSE{ADDR|PORT} gniazda DHCP: %s"

#: dhcp.c:99
#, c-format
msgid "failed to bind DHCP server socket: %s"
msgstr "błąd przy przyznawaniu nazwy gniazdu serwera DHCP: %s"

#: dhcp.c:125
#, c-format
msgid "cannot create ICMP raw socket: %s."
msgstr "nie udało się utworzyć surowego gniazda ICMP: %s."

#: dhcp.c:254 dhcp6.c:186
#, c-format
msgid "unknown interface %s in bridge-interface"
msgstr "nieznany interfejs %s w bridge-u"

#: dhcp.c:295
#, c-format
msgid "DHCP packet received on %s which has no address"
msgstr "żądanie DHCP odebrano na interfejsie %s, który nie ma adresu"

#: dhcp.c:429
#, c-format
msgid "ARP-cache injection failed: %s"
msgstr "uzupełnienie pamięci podręcznej ARP nie powiodło się: %s"

#: dhcp.c:490
#, c-format
msgid "Error sending DHCP packet to %s: %s"
msgstr "Błąd wysyłania pakietu DHCP do %s: %s"

#: dhcp.c:547
#, c-format
msgid "DHCP range %s -- %s is not consistent with netmask %s"
msgstr "zakres adresów DHCP %s -- %s jest niespójny z maską sieci %s"

#: dhcp.c:930
#, c-format
msgid "bad line at %s line %d"
msgstr "zła zawartość pliku %s, w linii %d"

#: dhcp.c:973
#, c-format
msgid "ignoring %s line %d, duplicate name or IP address"
msgstr "w %s pomijam linię %d -- powtórzona nazwa lub adres IP"

#: dhcp.c:1034
#, c-format
msgid "read %s - %d addresses"
msgstr "wczytałem %s - %d adresów"

#: dhcp.c:1136
#, fuzzy, c-format
msgid "Cannot broadcast DHCP relay via interface %s"
msgstr "Nie mogę rozesłać do serwerów DHCPv6 nie mając prawidłowego interfejsu"

#: dhcp.c:1160
#, c-format
msgid "broadcast via %s"
msgstr ""

#: dhcp.c:1163 rfc3315.c:2219
#, fuzzy, c-format
msgid "DHCP relay at %s -> %s"
msgstr "przekazywanie DHCP %s -> %s"

#: lease.c:64
#, c-format
msgid "ignoring invalid line in lease database: %s %s %s %s ..."
msgstr ""

#: lease.c:101
#, c-format
msgid "ignoring invalid line in lease database, bad address: %s"
msgstr ""

#: lease.c:108
msgid "too many stored leases"
msgstr "zbyt duża ilość zapisanych dzierżaw"

#: lease.c:176
#, c-format
msgid "cannot open or create lease file %s: %s"
msgstr "nie potrafię otworzyć albo utworzyć pliku dzierżaw %s: %s"

#: lease.c:185
#, fuzzy
msgid "failed to parse lease database cleanly"
msgstr "nie udało się odczytać %s: %s"

#: lease.c:188
#, fuzzy, c-format
msgid "failed to read lease file %s: %s"
msgstr "nie udało się odczytać %s: %s"

#: lease.c:204
#, c-format
msgid "cannot run lease-init script %s: %s"
msgstr "nie potrafię uruchomić skryptu %s: %s"

#: lease.c:210
#, c-format
msgid "lease-init script returned exit code %s"
msgstr "skrypt zakończył się z kodem powrotu %s"

#: lease.c:381
#, fuzzy, c-format
msgid "failed to write %s: %s (retry in %u s)"
msgstr "błąd zapisu do %s: %s (spróbuję ponownie za %us)"

#: lease.c:955
#, c-format
msgid "Ignoring domain %s for DHCP host name %s"
msgstr "Nie uwzględniam części domenowej (%s) dla komputera %s"

#: rfc2131.c:378
msgid "with subnet selector"
msgstr "z wyborem podsieci"

#: rfc2131.c:383
msgid "via"
msgstr "przez"

#: rfc2131.c:389
#, c-format
msgid "no address range available for DHCP request %s %s"
msgstr "nie zdefiniowano zakresu adresów odpowiedniego dla żądania %s %s"

#: rfc2131.c:403
#, c-format
msgid "%u available DHCP subnet: %s/%s"
msgstr "%u dostępna podsieć DHCP: %s/%s"

#: rfc2131.c:409 rfc3315.c:320
#, c-format
msgid "%u available DHCP range: %s -- %s"
msgstr "%u dostępny zakres adresów DHCP: %s -- %s"

#: rfc2131.c:521
#, c-format
msgid "%u vendor class: %s"
msgstr "%u klasa dostawcy: %s"

#: rfc2131.c:523
#, c-format
msgid "%u user class: %s"
msgstr "%u klasa użytkownika: %s"

#: rfc2131.c:557
msgid "disabled"
msgstr "wyłączony(a)"

#: rfc2131.c:598 rfc2131.c:1087 rfc2131.c:1536 rfc3315.c:633 rfc3315.c:816
#: rfc3315.c:1122
msgid "ignored"
msgstr "ignoruję"

#: rfc2131.c:613 rfc2131.c:1340 rfc3315.c:868
msgid "address in use"
msgstr "adres jest w użyciu"

#: rfc2131.c:627 rfc2131.c:1141
msgid "no address available"
msgstr "brak dostępnego adresu"

#: rfc2131.c:634 rfc2131.c:1302
msgid "wrong network"
msgstr "nieprawidłowa sieć"

#: rfc2131.c:649
msgid "no address configured"
msgstr "brak skonfigurowanego adresu"

#: rfc2131.c:655 rfc2131.c:1353
msgid "no leases left"
msgstr "brak wolnych dzierżaw"

#: rfc2131.c:756 rfc3315.c:500
#, c-format
msgid "%u client provides name: %s"
msgstr "klient %u przedstawia się jako %s"

#: rfc2131.c:885
msgid "PXE BIS not supported"
msgstr "PXE BIS nie jest obsługiwane"

#: rfc2131.c:1054 rfc3315.c:1223
#, c-format
msgid "disabling DHCP static address %s for %s"
msgstr "wyłączam statyczne przypisanie adresu %s dla %s"

#: rfc2131.c:1075
msgid "unknown lease"
msgstr "nieznana dzierżawa"

#: rfc2131.c:1110
#, c-format
msgid "not using configured address %s because it is leased to %s"
msgstr "nie proponuję zakładanego w konfiguracji adresu %s, bo jest on już wydzierżawiony komputerowi %s"

#: rfc2131.c:1120
#, c-format
msgid "not using configured address %s because it is in use by the server or relay"
msgstr "nie proponuję zakładanego w konfiguracji adresu %s, bo używa go któryś z serwerów"

#: rfc2131.c:1123
#, c-format
msgid "not using configured address %s because it was previously declined"
msgstr "nie proponuję zakładanego w konfiguracji adresu %s, bo już poprzednio został odrzucony"

#: rfc2131.c:1139 rfc2131.c:1346
msgid "no unique-id"
msgstr "brak unikalnego id"

#: rfc2131.c:1238
msgid "wrong server-ID"
msgstr "nieprawidłowy identyfikator serwera (server-ID)"

#: rfc2131.c:1257
msgid "wrong address"
msgstr "błędny adres"

#: rfc2131.c:1275 rfc3315.c:976
msgid "lease not found"
msgstr "dzierżawa nieznaleziona"

#: rfc2131.c:1310
msgid "address not available"
msgstr "adres niedostępny"

#: rfc2131.c:1321
msgid "static lease available"
msgstr "dostępna statyczna dzierżawa"

#: rfc2131.c:1325
msgid "address reserved"
msgstr "adres zarezerwowany"

#: rfc2131.c:1334
#, c-format
msgid "abandoning lease to %s of %s"
msgstr "porzucam przypisanie do %s nazwy %s"

#: rfc2131.c:1870
#, c-format
msgid "%u bootfile name: %s"
msgstr "%u nazwa pliku bootowania: %s"

#: rfc2131.c:1879
#, c-format
msgid "%u server name: %s"
msgstr "%u nazwa serwera: %s"

#: rfc2131.c:1889
#, c-format
msgid "%u next server: %s"
msgstr "%u następny serwer: %s"

#: rfc2131.c:1893
#, c-format
msgid "%u broadcast response"
msgstr "%u odpowiedź rozgłoszeniowa"

#: rfc2131.c:1956
#, c-format
msgid "cannot send DHCP/BOOTP option %d: no space left in packet"
msgstr "nie mam możliwości wysłania opcji %d DHCP/BOOTP: niedostateczna ilość miejsca w pakiecie"

#: rfc2131.c:2267
msgid "PXE menu too large"
msgstr "menu PXE zbyt duże"

#: rfc2131.c:2430 rfc3315.c:1517
#, c-format
msgid "%u requested options: %s"
msgstr "%u zażądano: %s"

#: rfc2131.c:2747
#, c-format
msgid "cannot send RFC3925 option: too many options for enterprise number %d"
msgstr "nie mogę wysłać opcji RFC3925: za długi łańcuch opcji przy numerze %d"

#: rfc2131.c:2810
#, c-format
msgid "%u reply delay: %d"
msgstr ""

#: netlink.c:86
#, c-format
msgid "cannot create netlink socket: %s"
msgstr "nie potrafię utworzyć połączenia netlink %s"

#: netlink.c:379
#, c-format
msgid "netlink returns error: %s"
msgstr "wystąpił błąd w połączeniu netlink %s"

#: dbus.c:491
#, c-format
msgid "Enabling --%s option from D-Bus"
msgstr "opcja --%s została właśnie aktywowana za pomocą D-Bus"

#: dbus.c:496
#, c-format
msgid "Disabling --%s option from D-Bus"
msgstr "opcja --%s została właśnie dezaktywowana za pomocą D-Bus"

#: dbus.c:857
msgid "setting upstream servers from DBus"
msgstr "ustawiam adresy serwerów nadrzędnych na podstawie informacji odebranych z DBus"

#: dbus.c:907
msgid "could not register a DBus message handler"
msgstr "nie można zarejestrować uchwytu DBus"

#: bpf.c:261
#, c-format
msgid "cannot create DHCP BPF socket: %s"
msgstr "nie potrafię utworzyć gniazda DHCP BPF: %s"

#: bpf.c:289
#, c-format
msgid "DHCP request for unsupported hardware type (%d) received on %s"
msgstr "żądanie DHCP od urządzenia nieobsługiwanego typu (%d) odebrano na %s"

#: bpf.c:374
#, c-format
msgid "cannot create PF_ROUTE socket: %s"
msgstr "nie udało się utworzyć gniazda PF_ROUTE: %s"

#: bpf.c:395
msgid "Unknown protocol version from route socket"
msgstr "Nieznana wersja protokołu."

#: helper.c:150
msgid "lease() function missing in Lua script"
msgstr "w skrypcie Lua brak funkcji lease()"

#: tftp.c:353
msgid "unable to get free port for TFTP"
msgstr "brak wolnego portu dla usługi TFTP"

#: tftp.c:369
#, c-format
msgid "unsupported request from %s"
msgstr "nieobsługiwane żądanie od komputera %s"

#: tftp.c:520
#, c-format
msgid "file %s not found for %s"
msgstr "plik %s nie został znaleziony dla %s"

#: tftp.c:609
#, c-format
msgid "ignoring packet from %s (TID mismatch)"
msgstr ""

#: tftp.c:662
#, c-format
msgid "failed sending %s to %s"
msgstr "błąd wysyłania pliku %s do komputera %s"

#: tftp.c:662
#, c-format
msgid "sent %s to %s"
msgstr "plik %s przesłano do %s"

#: tftp.c:712
#, c-format
msgid "error %d %s received from %s"
msgstr "błąd %d %s odebrano od %s"

#: log.c:203
#, c-format
msgid "overflow: %d log entries lost"
msgstr "przepełnienie: stracono %d wpisów do logów"

#: log.c:281
#, c-format
msgid "log failed: %s"
msgstr "nie udało się zapisać komunikatów do %s"

#: log.c:490
msgid "FAILED to start up"
msgstr "BŁĄD: nie udało się uruchomić dnsmasq-a"

#: conntrack.c:63
#, c-format
msgid "Conntrack connection mark retrieval failed: %s"
msgstr "Nie udało się odcztać znacznika połączenia (conntrack): %s"

#: dhcp6.c:51
#, c-format
msgid "cannot create DHCPv6 socket: %s"
msgstr "nie udało się utworzyć gniazda dla DHCPv6: %s"

#: dhcp6.c:72
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCPv6 socket: %s"
msgstr "nie udało się ustawić SO_REUSE{ADDR|PORT} gniazda DHCPv6: %s"

#: dhcp6.c:84
#, c-format
msgid "failed to bind DHCPv6 server socket: %s"
msgstr "dowiązywanie gniazda serwera DHCPv6 zakończone niepowodzeniem: %s"

#: rfc3315.c:173
#, c-format
msgid "no address range available for DHCPv6 request from relay at %s"
msgstr "nie zdefiniowano zakresu adresów odpowiedniego dla żądania DHCPv6 przekazanego przez %s"

#: rfc3315.c:182
#, c-format
msgid "no address range available for DHCPv6 request via %s"
msgstr "nie zdefiniowano zakresu adresów odpowiedniego dla żądania DHCPv6 od %s"

#: rfc3315.c:317
#, c-format
msgid "%u available DHCPv6 subnet: %s/%d"
msgstr "%u dostępna podsieć DHCPv6: %s/%d"

#: rfc3315.c:400
#, c-format
msgid "%u vendor class: %u"
msgstr "%u klasa dostawcy: %u"

#: rfc3315.c:448
#, c-format
msgid "%u client MAC address: %s"
msgstr "adres MAC klienta %u: %s"

#: rfc3315.c:763 rfc3315.c:860
msgid "address unavailable"
msgstr "adres niedostępny"

#: rfc3315.c:775 rfc3315.c:904 rfc3315.c:1273
msgid "success"
msgstr "udane"

#: rfc3315.c:790 rfc3315.c:799 rfc3315.c:912 rfc3315.c:914 rfc3315.c:1048
msgid "no addresses available"
msgstr "brak wolnych adresów"

#: rfc3315.c:891
msgid "not on link"
msgstr "poza zasięgiem"

#: rfc3315.c:980 rfc3315.c:1181 rfc3315.c:1262
msgid "no binding found"
msgstr "brak powiązania"

#: rfc3315.c:1017
msgid "deprecated"
msgstr "przestarzały"

#: rfc3315.c:1024
msgid "address invalid"
msgstr "niepoprawny adres"

#: rfc3315.c:1082 rfc3315.c:1084
msgid "confirm failed"
msgstr "brak potwierdzenia"

#: rfc3315.c:1099
msgid "all addresses still on link"
msgstr "wszystkie adresy ciągle w użyciu"

#: rfc3315.c:1190
msgid "release received"
msgstr "adres został zwolniony"

#: rfc3315.c:2200
#, fuzzy, c-format
msgid "Cannot multicast DHCP relay via interface %s"
msgstr "Nie mogę rozesłać do serwerów DHCPv6 nie mając prawidłowego interfejsu"

#: rfc3315.c:2216
#, c-format
msgid "multicast via %s"
msgstr ""

#: dhcp-common.c:187
#, c-format
msgid "Ignoring duplicate dhcp-option %d"
msgstr "Pomijam powtórzoną dhcp-option %d"

#: dhcp-common.c:264
#, c-format
msgid "%u tags: %s"
msgstr "%u cechy: %s"

#: dhcp-common.c:484
#, c-format
msgid "%s has more than one address in hostsfile, using %s for DHCP"
msgstr "do komputera o nazwie %s pasuje więcej niż jeden adres, w odpowiedzi DHCP wysyłam %s"

#: dhcp-common.c:518
#, c-format
msgid "duplicate IP address %s (%s) in dhcp-config directive"
msgstr "powtórzenie adresu IP %s (%s) w opcji dhcp-config"

#: dhcp-common.c:738
#, c-format
msgid "Known DHCP options:\n"
msgstr "Znane opcje DHCP:\n"

#: dhcp-common.c:749
#, c-format
msgid "Known DHCPv6 options:\n"
msgstr "Rozpoznawane opcje DHCPv6:\n"

#: dhcp-common.c:946
msgid ", prefix deprecated"
msgstr ", przestarzały prefiks"

#: dhcp-common.c:949
#, c-format
msgid ", lease time "
msgstr ", czas dzierżawy "

#: dhcp-common.c:991
#, c-format
msgid "%s stateless on %s%.0s%.0s%s"
msgstr "%s bezstanowy na %s%.0s%.0s%s"

#: dhcp-common.c:993
#, c-format
msgid "%s, static leases only on %.0s%s%s%.0s"
msgstr "%s, wyłącznie statyczne dzierżawy na %.0s%s%s%.0s"

#: dhcp-common.c:995
#, c-format
msgid "%s, proxy on subnet %.0s%s%.0s%.0s"
msgstr "%s, wykryto pośrednika na podsieci %.0s%s%.0s%.0s"

#: dhcp-common.c:996
#, c-format
msgid "%s, IP range %s -- %s%s%.0s"
msgstr "%s, zakres IP %s -- %s%s%.0s"

#: dhcp-common.c:1009
#, c-format
msgid "DHCPv4-derived IPv6 names on %s%s"
msgstr "pochodzące z DHCPv4 nazwy IPv6 na %s%s"

#: dhcp-common.c:1012
#, c-format
msgid "router advertisement on %s%s"
msgstr "anonsowanie rutera na %s%s"

#: dhcp-common.c:1043
#, fuzzy, c-format
msgid "DHCP relay from %s via %s"
msgstr "przekazywanie DHCP z %s do %s"

#: dhcp-common.c:1045
#, c-format
msgid "DHCP relay from %s to %s via %s"
msgstr "przekazywanie DHCP z %s do %s za pomocą %s"

#: dhcp-common.c:1048
#, c-format
msgid "DHCP relay from %s to %s"
msgstr "przekazywanie DHCP z %s do %s"

#: radv.c:110
#, c-format
msgid "cannot create ICMPv6 socket: %s"
msgstr "nie udało się utworzyć gniazda dla ICMPv6: %s"

#: auth.c:462
#, c-format
msgid "ignoring zone transfer request from %s"
msgstr "ignoruję żądanie transferu strefy od %s"

#: ipset.c:99
#, c-format
msgid "failed to create IPset control socket: %s"
msgstr "nie powiodło się otwieranie gniazda sterującego IPset: %s"

#: ipset.c:211
#, fuzzy, c-format
msgid "failed to update ipset %s: %s"
msgstr "nie udało się uaktualnić znacznika czasu pliku %s: %s"

#: pattern.c:29
#, c-format
msgid "[pattern.c:%d] Assertion failure: %s"
msgstr ""

#: pattern.c:142
#, c-format
msgid "Invalid DNS name: Invalid character %c."
msgstr ""

#: pattern.c:151
msgid "Invalid DNS name: Empty label."
msgstr ""

#: pattern.c:156
msgid "Invalid DNS name: Label starts with hyphen."
msgstr ""

#: pattern.c:170
msgid "Invalid DNS name: Label ends with hyphen."
msgstr ""

#: pattern.c:176
#, c-format
msgid "Invalid DNS name: Label is too long (%zu)."
msgstr ""

#: pattern.c:184
#, c-format
msgid "Invalid DNS name: Not enough labels (%zu)."
msgstr ""

#: pattern.c:189
msgid "Invalid DNS name: Final label is fully numeric."
msgstr ""

#: pattern.c:199
msgid "Invalid DNS name: \"local\" pseudo-TLD."
msgstr ""

#: pattern.c:204
#, c-format
msgid "DNS name has invalid length (%zu)."
msgstr ""

#: pattern.c:258
#, c-format
msgid "Invalid DNS name pattern: Invalid character %c."
msgstr ""

#: pattern.c:267
msgid "Invalid DNS name pattern: Empty label."
msgstr ""

#: pattern.c:272
msgid "Invalid DNS name pattern: Label starts with hyphen."
msgstr ""

#: pattern.c:285
msgid "Invalid DNS name pattern: Wildcard character used more than twice per label."
msgstr ""

#: pattern.c:295
msgid "Invalid DNS name pattern: Label ends with hyphen."
msgstr ""

#: pattern.c:301
#, c-format
msgid "Invalid DNS name pattern: Label is too long (%zu)."
msgstr ""

#: pattern.c:309
#, c-format
msgid "Invalid DNS name pattern: Not enough labels (%zu)."
msgstr ""

#: pattern.c:314
msgid "Invalid DNS name pattern: Wildcard within final two labels."
msgstr ""

#: pattern.c:319
msgid "Invalid DNS name pattern: Final label is fully numeric."
msgstr ""

#: pattern.c:329
msgid "Invalid DNS name pattern: \"local\" pseudo-TLD."
msgstr ""

#: pattern.c:334
#, c-format
msgid "DNS name pattern has invalid length after removing wildcards (%zu)."
msgstr ""

#: dnssec.c:206
#, fuzzy
msgid "system time considered valid, now checking DNSSEC signature timestamps."
msgstr "trwa sprawdzanie sygnatur czasowych podpisów DNSSEC"

#: dnssec.c:1018
#, c-format
msgid "Insecure DS reply received for %s, check domain configuration and upstream DNS server DNSSEC support"
msgstr ""

#: blockdata.c:55
#, fuzzy, c-format
msgid "pool memory in use %zu, max %zu, allocated %zu"
msgstr "DNSSEC: zużycie pamięci %u, maks. %u, przydzielona %u"

#: tables.c:61
#, c-format
msgid "failed to access pf devices: %s"
msgstr "brak dostępu do /dev/pf (filtra pakietów): %s"

#: tables.c:74
#, c-format
msgid "warning: no opened pf devices %s"
msgstr "uwaga: brak otwartych filtrów pakietów %s"

#: tables.c:82
#, c-format
msgid "error: cannot use table name %s"
msgstr "błąd: nie potrafię użyć nazwy tablicy %s"

#: tables.c:90
#, c-format
msgid "error: cannot strlcpy table name %s"
msgstr "błąd: nie potrafię strlcpy nazwy tablicy %s"

#: tables.c:101
#, fuzzy, c-format
msgid "IPset: error: %s"
msgstr "błąd DBus: %s"

#: tables.c:108
msgid "info: table created"
msgstr "info: tablica utworzona"

#: tables.c:133
#, c-format
msgid "warning: DIOCR%sADDRS: %s"
msgstr "uwaga: DIOCR%sADDRS: %s"

#: tables.c:137
#, c-format
msgid "%d addresses %s"
msgstr "%d adresów %s"

#: inotify.c:62
#, c-format
msgid "cannot access path %s: %s"
msgstr "brak dostępu do katalogu %s: %s"

#: inotify.c:95
#, c-format
msgid "failed to create inotify: %s"
msgstr "nie udało się uruchomić powiadamiania inotify: %s"

#: inotify.c:111
#, c-format
msgid "too many symlinks following %s"
msgstr "zbyt wiele odniesień począwszy od %s"

#: inotify.c:127
#, c-format
msgid "directory %s for resolv-file is missing, cannot poll"
msgstr "katalog %s z resolv-file nie istnieje - nie ma czego odpytywać"

#: inotify.c:131 inotify.c:200
#, c-format
msgid "failed to create inotify for %s: %s"
msgstr "nie udało się utworzyć powiadamiania dla %s: %s"

#: inotify.c:178 inotify.c:185
#, c-format
msgid "bad dynamic directory %s: %s"
msgstr "zły katalog dynamiczny %s: %s"

#: inotify.c:186
#, fuzzy
msgid "not a directory"
msgstr "brak dostępu do katalogu %s: %s"

#: inotify.c:299
#, c-format
msgid "inotify: %s removed"
msgstr ""

#: inotify.c:301
#, fuzzy, c-format
msgid "inotify: %s new or modified"
msgstr "inotify: pojawił się lub uległ zmianie plik %s"

#: inotify.c:309
#, c-format
msgid "inotify: flushed %u names read from %s"
msgstr ""

#: dump.c:68
#, fuzzy, c-format
msgid "cannot create %s: %s"
msgstr "błąd odczytu z pliku %s: %s"

#: dump.c:74
#, fuzzy, c-format
msgid "bad header in %s"
msgstr "adres jest w użyciu"

#: dump.c:287
#, fuzzy
msgid "failed to write packet dump"
msgstr "wysyłanie pakietu nie powiodło się: %s"

#: dump.c:289
#, c-format
msgid "%u dumping packet %u mask 0x%04x"
msgstr ""

#: dump.c:291
#, c-format
msgid "dumping packet %u mask 0x%04x"
msgstr ""

#: ubus.c:79
#, c-format
msgid "UBus subscription callback: %s subscriber(s)"
msgstr ""

#: ubus.c:99
#, fuzzy, c-format
msgid "Cannot reconnect to UBus: %s"
msgstr "nie udało się otworzyć logu %s: %s"

#: ubus.c:135
msgid "Cannot set UBus listeners: no connection"
msgstr ""

#: ubus.c:155
msgid "Cannot poll UBus listeners: no connection"
msgstr ""

#: ubus.c:168
msgid "Disconnecting from UBus"
msgstr ""

#: ubus.c:179 ubus.c:326
#, c-format
msgid "UBus command failed: %d (%s)"
msgstr ""

#: hash-questions.c:40
msgid "Failed to create SHA-256 hash object"
msgstr ""

#: nftset.c:35
#, fuzzy
msgid "failed to create nftset context"
msgstr "nie powiodło się otwieranie gniazda sterującego IPset: %s"

#, fuzzy
#~ msgid "bad IPv4 prefix"
#~ msgstr "zła maska"

#, fuzzy
#~ msgid "Cannot add object to UBus: %s"
#~ msgstr "nie udało się otworzyć logu %s: %s"

#, fuzzy
#~ msgid "Failed to send UBus event: %s"
#~ msgstr "wysyłanie pakietu nie powiodło się: %s"

#~ msgid "Specify DHCPv6 prefix class"
#~ msgstr "Określenie prefiksu klasy DHCPv6"

#~ msgid "cannot run scripts under uClinux"
#~ msgstr "w uClinuksie nie ma możliwości uruchamiania skryptów"

#~ msgid "cannot match tags in --dhcp-host"
#~ msgstr "--dhcp-host nie dopuszcza dopasowywania na podstawie znaczników"

#~ msgid "attempt to set an IPv6 server address via DBus - no IPv6 support"
#~ msgstr "próba ustawienia adresu IPv6 serwera przez DBus, ale brak obsługi IPv6"

#~ msgid "unknown prefix-class %d"
#~ msgstr "nieznana klasa sieci %d"

#~ msgid "bad TTL"
#~ msgstr "zły TTL"

#~ msgid "error: fill_addr missused"
#~ msgstr "błąd: niepoprawnie użyty fill_addr"

#~ msgid "warning: pfr_add_tables: %s(%d)"
#~ msgstr "uwaga: pfr_add_tables: %s(%d)"

#, fuzzy
#~ msgid "cannot cannonicalise resolv-file %s: %s"
#~ msgstr "nie potrafię otworzyć albo utworzyć pliku dzierżaw %s: %s"

#~ msgid "Always send frequent router-advertisements"
#~ msgstr "Rozsyłanie wielokrotne anonsów rutera (RA)"

#~ msgid "no interface with address %s"
#~ msgstr "brak interfejsu z adresem %s"

#~ msgid "duplicate IP address %s in dhcp-config directive."
#~ msgstr "powtórzony adres IP (%s) w parametrze dhcp-config"

#, fuzzy
#~ msgid "Specify path to Lua script (no default)."
#~ msgstr "Określenie ścieżki do pliku PID (domyślnie: %s)."

#~ msgid "only one dhcp-hostsfile allowed"
#~ msgstr "można wskazać tylko jeden plik dhcp-hostsfile"

#~ msgid "only one dhcp-optsfile allowed"
#~ msgstr "można wskazać tylko jeden plik dhcp-optsfile"

#~ msgid "files nested too deep in %s"
#~ msgstr "zbyt duże zagłębienie plików w %s"

#~ msgid "TXT record string too long"
#~ msgstr "zbyt długi rekord TXT"

#~ msgid "failed to set IPV6 options on listening socket: %s"
#~ msgstr "błąd ustawiania opcji IPV6 na nasłuchującym gnieździe: %s"

#~ msgid "failed to bind listening socket for %s: %s"
#~ msgstr "błąd przy przyznawaniu nazwy gniazdu %s: %s"
