# Italian translations for dnsmasq package.
# This file is put in the public domain.
# <PERSON> <<EMAIL>>, 2006.
#
msgid ""
msgstr ""
"Project-Id-Version: dnsmasq 2.32\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2009-06-18 12:24+0100\n"
"PO-Revision-Date: 2017-07-17 18:30+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Italian <<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=ASCII\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: cache.c:652
msgid "Internal error in cache."
msgstr ""

#: cache.c:1179
#, c-format
msgid "failed to load names from %s: %s"
msgstr ""

#: cache.c:1201 dhcp.c:943
#, c-format
msgid "bad address at %s line %d"
msgstr ""

#: cache.c:1254 dhcp.c:959
#, c-format
msgid "bad name at %s line %d"
msgstr ""

#: cache.c:1265
#, c-format
msgid "read %s - %d names"
msgstr ""

#: cache.c:1381
msgid "cleared cache"
msgstr ""

#: cache.c:1445
#, c-format
msgid "No IPv4 address found for %s"
msgstr ""

#: cache.c:1491
#, c-format
msgid "%s is a CNAME, not giving it to the DHCP lease of %s"
msgstr ""

#: cache.c:1515
#, c-format
msgid "not giving name %s to the DHCP lease of %s because the name exists in %s with address %s"
msgstr ""

#: cache.c:1760
#, c-format
msgid "time %lu"
msgstr ""

#: cache.c:1761
#, c-format
msgid "cache size %d, %d/%d cache insertions re-used unexpired cache entries."
msgstr ""

#: cache.c:1763
#, c-format
msgid "queries forwarded %u, queries answered locally %u"
msgstr ""

#: cache.c:1766
#, c-format
msgid "queries answered from stale cache %u"
msgstr ""

#: cache.c:1768
#, c-format
msgid "queries for authoritative zones %u"
msgstr ""

#: cache.c:1796
#, c-format
msgid "server %s#%d: queries sent %u, retried %u, failed %u, nxdomain replies %u, avg. latency %ums"
msgstr ""

#: util.c:51
#, c-format
msgid "failed to seed the random number generator: %s"
msgstr ""

#: util.c:246
msgid "failed to allocate memory"
msgstr ""

#: util.c:305 option.c:696
msgid "could not get memory"
msgstr ""

#: util.c:326
#, c-format
msgid "cannot create pipe: %s"
msgstr ""

#: util.c:334
#, c-format
msgid "failed to allocate %d bytes"
msgstr ""

#: util.c:344
#, c-format
msgid "failed to reallocate %d bytes"
msgstr ""

#: util.c:465
#, c-format
msgid "cannot read monotonic clock: %s"
msgstr ""

#: util.c:579
#, c-format
msgid "infinite"
msgstr ""

#: util.c:867
#, c-format
msgid "failed to find kernel version: %s"
msgstr ""

#: option.c:393
msgid "Specify local address(es) to listen on."
msgstr ""

#: option.c:394
msgid "Return ipaddr for all hosts in specified domains."
msgstr ""

#: option.c:395
msgid "Fake reverse lookups for RFC1918 private address ranges."
msgstr ""

#: option.c:396
msgid "Treat ipaddr as NXDOMAIN (defeats Verisign wildcard)."
msgstr ""

#: option.c:397
#, c-format
msgid "Specify the size of the cache in entries (defaults to %s)."
msgstr ""

#: option.c:398
#, c-format
msgid "Specify configuration file (defaults to %s)."
msgstr ""

#: option.c:399
msgid "Do NOT fork into the background: run in debug mode."
msgstr ""

#: option.c:400
msgid "Do NOT forward queries with no domain part."
msgstr ""

#: option.c:401
msgid "Return self-pointing MX records for local hosts."
msgstr ""

#: option.c:402
msgid "Expand simple names in /etc/hosts with domain-suffix."
msgstr ""

#: option.c:403
msgid "Don't forward spurious DNS requests from Windows hosts."
msgstr ""

#: option.c:404
msgid "Don't include IPv4 addresses in DNS answers."
msgstr ""

#: option.c:405
msgid "Don't include IPv6 addresses in DNS answers."
msgstr ""

#: option.c:406
msgid "Enable DHCP in the range given with lease duration."
msgstr ""

#: option.c:407
#, c-format
msgid "Change to this group after startup (defaults to %s)."
msgstr ""

#: option.c:408
msgid "Set address or hostname for a specified machine."
msgstr ""

#: option.c:409
msgid "Read DHCP host specs from file."
msgstr ""

#: option.c:410
msgid "Read DHCP option specs from file."
msgstr ""

#: option.c:411
msgid "Read DHCP host specs from a directory."
msgstr ""

#: option.c:412
msgid "Read DHCP options from a directory."
msgstr ""

#: option.c:413
msgid "Evaluate conditional tag expression."
msgstr ""

#: option.c:414
#, c-format
msgid "Do NOT load %s file."
msgstr ""

#: option.c:415
#, c-format
msgid "Specify a hosts file to be read in addition to %s."
msgstr ""

#: option.c:416
msgid "Read hosts files from a directory."
msgstr ""

#: option.c:417
msgid "Specify interface(s) to listen on."
msgstr ""

#: option.c:418
msgid "Specify interface(s) NOT to listen on."
msgstr ""

#: option.c:419
msgid "Map DHCP user class to tag."
msgstr ""

#: option.c:420
msgid "Map RFC3046 circuit-id to tag."
msgstr ""

#: option.c:421
msgid "Map RFC3046 remote-id to tag."
msgstr ""

#: option.c:422
msgid "Map RFC3993 subscriber-id to tag."
msgstr ""

#: option.c:423
msgid "Specify vendor class to match for PXE requests."
msgstr ""

#: option.c:424
msgid "Don't do DHCP for hosts with tag set."
msgstr ""

#: option.c:425
msgid "Force broadcast replies for hosts with tag set."
msgstr ""

#: option.c:426
msgid "Do NOT fork into the background, do NOT run in debug mode."
msgstr ""

#: option.c:427
msgid "Assume we are the only DHCP server on the local network."
msgstr ""

#: option.c:428
#, c-format
msgid "Specify where to store DHCP leases (defaults to %s)."
msgstr ""

#: option.c:429
msgid "Return MX records for local hosts."
msgstr ""

#: option.c:430
msgid "Specify an MX record."
msgstr ""

#: option.c:431
msgid "Specify BOOTP options to DHCP server."
msgstr ""

#: option.c:432
#, c-format
msgid "Do NOT poll %s file, reload only on SIGHUP."
msgstr ""

#: option.c:433
msgid "Do NOT cache failed search results."
msgstr ""

#: option.c:434
msgid "Use expired cache data for faster reply."
msgstr ""

#: option.c:435
#, c-format
msgid "Use nameservers strictly in the order given in %s."
msgstr ""

#: option.c:436
msgid "Specify options to be sent to DHCP clients."
msgstr ""

#: option.c:437
msgid "DHCP option sent even if the client does not request it."
msgstr ""

#: option.c:438
msgid "Specify port to listen for DNS requests on (defaults to 53)."
msgstr ""

#: option.c:439
#, c-format
msgid "Maximum supported UDP packet size for EDNS.0 (defaults to %s)."
msgstr ""

#: option.c:440
msgid "Log DNS queries."
msgstr ""

#: option.c:441
msgid "Force the originating port for upstream DNS queries."
msgstr ""

#: option.c:442
msgid "Set maximum number of random originating ports for a query."
msgstr ""

#: option.c:443
msgid "Do NOT read resolv.conf."
msgstr ""

#: option.c:444
#, c-format
msgid "Specify path to resolv.conf (defaults to %s)."
msgstr ""

#: option.c:445
msgid "Specify path to file with server= options"
msgstr ""

#: option.c:446
msgid "Specify address(es) of upstream servers with optional domains."
msgstr ""

#: option.c:447
msgid "Specify address of upstream servers for reverse address queries"
msgstr ""

#: option.c:448
msgid "Never forward queries to specified domains."
msgstr ""

#: option.c:449
msgid "Specify the domain to be assigned in DHCP leases."
msgstr ""

#: option.c:450
msgid "Specify default target in an MX record."
msgstr ""

#: option.c:451
msgid "Specify time-to-live in seconds for replies from /etc/hosts."
msgstr ""

#: option.c:452
msgid "Specify time-to-live in seconds for negative caching."
msgstr ""

#: option.c:453
msgid "Specify time-to-live in seconds for maximum TTL to send to clients."
msgstr ""

#: option.c:454
msgid "Specify time-to-live ceiling for cache."
msgstr ""

#: option.c:455
msgid "Specify time-to-live floor for cache."
msgstr ""

#: option.c:456
msgid "Retry DNS queries after this many milliseconds."
msgstr ""

#: option.c:457
#, c-format
msgid "Change to this user after startup. (defaults to %s)."
msgstr ""

#: option.c:458
msgid "Map DHCP vendor class to tag."
msgstr ""

#: option.c:459
msgid "Display dnsmasq version and copyright information."
msgstr ""

#: option.c:460
msgid "Translate IPv4 addresses from upstream servers."
msgstr ""

#: option.c:461
msgid "Specify a SRV record."
msgstr ""

#: option.c:462
msgid "Display this message. Use --help dhcp or --help dhcp6 for known DHCP options."
msgstr ""

#: option.c:463
#, c-format
msgid "Specify path of PID file (defaults to %s)."
msgstr ""

#: option.c:464
#, c-format
msgid "Specify maximum number of DHCP leases (defaults to %s)."
msgstr ""

#: option.c:465
msgid "Answer DNS queries based on the interface a query was sent to."
msgstr ""

#: option.c:466
msgid "Specify TXT DNS record."
msgstr ""

#: option.c:467
msgid "Specify PTR DNS record."
msgstr ""

#: option.c:468
msgid "Give DNS name to IPv4 address of interface."
msgstr ""

#: option.c:469
msgid "Bind only to interfaces in use."
msgstr ""

#: option.c:470
#, c-format
msgid "Read DHCP static host information from %s."
msgstr ""

#: option.c:471
msgid "Enable the DBus interface for setting upstream servers, etc."
msgstr ""

#: option.c:472
msgid "Enable the UBus interface."
msgstr ""

#: option.c:473
msgid "Do not provide DHCP on this interface, only provide DNS."
msgstr ""

#: option.c:474
msgid "Enable dynamic address allocation for bootp."
msgstr ""

#: option.c:475
msgid "Map MAC address (with wildcards) to option set."
msgstr ""

#: option.c:476
msgid "Treat DHCP requests on aliases as arriving from interface."
msgstr ""

#: option.c:477
msgid "Specify extra networks sharing a broadcast domain for DHCP"
msgstr ""

#: option.c:478
msgid "Disable ICMP echo address checking in the DHCP server."
msgstr ""

#: option.c:479
msgid "Shell script to run on DHCP lease creation and destruction."
msgstr ""

#: option.c:480
msgid "Lua script to run on DHCP lease creation and destruction."
msgstr ""

#: option.c:481
msgid "Run lease-change scripts as this user."
msgstr ""

#: option.c:482
msgid "Call dhcp-script with changes to local ARP table."
msgstr ""

#: option.c:483
msgid "Read configuration from all the files in this directory."
msgstr ""

#: option.c:484
msgid "Execute file and read configuration from stdin."
msgstr ""

#: option.c:485
msgid "Log to this syslog facility or file. (defaults to DAEMON)"
msgstr ""

#: option.c:486
msgid "Do not use leasefile."
msgstr ""

#: option.c:487
#, c-format
msgid "Maximum number of concurrent DNS queries. (defaults to %s)"
msgstr ""

#: option.c:488
#, c-format
msgid "Clear DNS cache when reloading %s."
msgstr ""

#: option.c:489
msgid "Ignore hostnames provided by DHCP clients."
msgstr ""

#: option.c:490
msgid "Do NOT reuse filename and server fields for extra DHCP options."
msgstr ""

#: option.c:491
msgid "Enable integrated read-only TFTP server."
msgstr ""

#: option.c:492
msgid "Export files by TFTP only from the specified subtree."
msgstr ""

#: option.c:493
msgid "Add client IP or hardware address to tftp-root."
msgstr ""

#: option.c:494
msgid "Allow access only to files owned by the user running dnsmasq."
msgstr ""

#: option.c:495
msgid "Do not terminate the service if TFTP directories are inaccessible."
msgstr ""

#: option.c:496
#, c-format
msgid "Maximum number of concurrent TFTP transfers (defaults to %s)."
msgstr ""

#: option.c:497
msgid "Maximum MTU to use for TFTP transfers."
msgstr ""

#: option.c:498
msgid "Disable the TFTP blocksize extension."
msgstr ""

#: option.c:499
msgid "Convert TFTP filenames to lowercase"
msgstr ""

#: option.c:500
msgid "Ephemeral port range for use by TFTP transfers."
msgstr ""

#: option.c:501
msgid "Use only one port for TFTP server."
msgstr ""

#: option.c:502
msgid "Extra logging for DHCP."
msgstr ""

#: option.c:503
msgid "Enable async. logging; optionally set queue length."
msgstr ""

#: option.c:504
msgid "Stop DNS rebinding. Filter private IP ranges when resolving."
msgstr ""

#: option.c:505
msgid "Allow rebinding of *********/8, for RBL servers."
msgstr ""

#: option.c:506
msgid "Inhibit DNS-rebind protection on this domain."
msgstr ""

#: option.c:507
msgid "Always perform DNS queries to all servers."
msgstr ""

#: option.c:508
msgid "Set tag if client includes matching option in request."
msgstr ""

#: option.c:509
msgid "Set tag if client provides given name."
msgstr ""

#: option.c:510
msgid "Use alternative ports for DHCP."
msgstr ""

#: option.c:511
msgid "Specify NAPTR DNS record."
msgstr ""

#: option.c:512
msgid "Specify lowest port available for DNS query transmission."
msgstr ""

#: option.c:513
msgid "Specify highest port available for DNS query transmission."
msgstr ""

#: option.c:514
msgid "Use only fully qualified domain names for DHCP clients."
msgstr ""

#: option.c:515
msgid "Generate hostnames based on MAC address for nameless clients."
msgstr ""

#: option.c:516
msgid "Use these DHCP relays as full proxies."
msgstr ""

#: option.c:517
msgid "Relay DHCP requests to a remote server"
msgstr ""

#: option.c:518
msgid "Specify alias name for LOCAL DNS name."
msgstr ""

#: option.c:519
msgid "Prompt to send to PXE clients."
msgstr ""

#: option.c:520
msgid "Boot service for PXE menu."
msgstr ""

#: option.c:521
msgid "Check configuration syntax."
msgstr ""

#: option.c:522
msgid "Add requestor's MAC address to forwarded DNS queries."
msgstr ""

#: option.c:523
msgid "Strip MAC information from queries."
msgstr ""

#: option.c:524
msgid "Add specified IP subnet to forwarded DNS queries."
msgstr ""

#: option.c:525
msgid "Strip ECS information from queries."
msgstr ""

#: option.c:526
msgid "Add client identification to forwarded DNS queries."
msgstr ""

#: option.c:527
msgid "Proxy DNSSEC validation results from upstream nameservers."
msgstr ""

#: option.c:528
msgid "Attempt to allocate sequential IP addresses to DHCP clients."
msgstr ""

#: option.c:529
msgid "Ignore client identifier option sent by DHCP clients."
msgstr ""

#: option.c:530
msgid "Copy connection-track mark from queries to upstream connections."
msgstr ""

#: option.c:531
msgid "Allow DHCP clients to do their own DDNS updates."
msgstr ""

#: option.c:532
msgid "Send router-advertisements for interfaces doing DHCPv6"
msgstr ""

#: option.c:533
msgid "Specify DUID_EN-type DHCPv6 server DUID"
msgstr ""

#: option.c:534
msgid "Specify host (A/AAAA and PTR) records"
msgstr ""

#: option.c:535
msgid "Specify host record in interface subnet"
msgstr ""

#: option.c:536
msgid "Specify certification authority authorization record"
msgstr ""

#: option.c:537
msgid "Specify arbitrary DNS resource record"
msgstr ""

#: option.c:538
msgid "Bind to interfaces in use - check for new interfaces"
msgstr ""

#: option.c:539
msgid "Export local names to global DNS"
msgstr ""

#: option.c:540
msgid "Domain to export to global DNS"
msgstr ""

#: option.c:541
msgid "Set TTL for authoritative replies"
msgstr ""

#: option.c:542
msgid "Set authoritative zone information"
msgstr ""

#: option.c:543
msgid "Secondary authoritative nameservers for forward domains"
msgstr ""

#: option.c:544
msgid "Peers which are allowed to do zone transfer"
msgstr ""

#: option.c:545
msgid "Specify ipsets to which matching domains should be added"
msgstr ""

#: option.c:546
msgid "Specify nftables sets to which matching domains should be added"
msgstr ""

#: option.c:547
msgid "Enable filtering of DNS queries with connection-track marks."
msgstr ""

#: option.c:548
msgid "Set allowed DNS patterns for a connection-track mark."
msgstr ""

#: option.c:549
msgid "Specify a domain and address range for synthesised names"
msgstr ""

#: option.c:550
msgid "Activate DNSSEC validation"
msgstr ""

#: option.c:551
msgid "Specify trust anchor key digest."
msgstr ""

#: option.c:552
msgid "Disable upstream checking for DNSSEC debugging."
msgstr ""

#: option.c:553
msgid "Ensure answers without DNSSEC are in unsigned zones."
msgstr ""

#: option.c:554
msgid "Don't check DNSSEC signature timestamps until first cache-reload"
msgstr ""

#: option.c:555
msgid "Timestamp file to verify system clock for DNSSEC"
msgstr ""

#: option.c:556
msgid "Set MTU, priority, resend-interval and router-lifetime"
msgstr ""

#: option.c:557
msgid "Do not log routine DHCP."
msgstr ""

#: option.c:558
msgid "Do not log routine DHCPv6."
msgstr ""

#: option.c:559
msgid "Do not log RA."
msgstr ""

#: option.c:560
msgid "Log debugging information."
msgstr ""

#: option.c:561
msgid "Accept queries only from directly-connected networks."
msgstr ""

#: option.c:562
msgid "Detect and remove DNS forwarding loops."
msgstr ""

#: option.c:563
msgid "Ignore DNS responses containing ipaddr."
msgstr ""

#: option.c:564
msgid "Set TTL in DNS responses with DHCP-derived addresses."
msgstr ""

#: option.c:565
msgid "Delay DHCP replies for at least number of seconds."
msgstr ""

#: option.c:566
msgid "Enables DHCPv4 Rapid Commit option."
msgstr ""

#: option.c:567
msgid "Path to debug packet dump file"
msgstr ""

#: option.c:568
msgid "Mask which packets to dump"
msgstr ""

#: option.c:569
msgid "Call dhcp-script when lease expiry changes."
msgstr ""

#: option.c:570
msgid "Send Cisco Umbrella identifiers including remote IP."
msgstr ""

#: option.c:571
msgid "Do not log routine TFTP."
msgstr ""

#: option.c:572
msgid "Suppress round-robin ordering of DNS records."
msgstr ""

#: option.c:802
#, c-format
msgid ""
"Usage: dnsmasq [options]\n"
"\n"
msgstr ""

#: option.c:804
#, c-format
msgid "Use short options only on the command line.\n"
msgstr ""

#: option.c:806
#, c-format
msgid "Valid options are:\n"
msgstr ""

#: option.c:853 option.c:1055
msgid "bad address"
msgstr ""

#: option.c:882 option.c:886
msgid "bad port"
msgstr ""

#: option.c:899 option.c:1002 option.c:1048
msgid "interface binding not supported"
msgstr ""

#: option.c:955
msgid "Cannot resolve server name"
msgstr ""

#: option.c:991
msgid "cannot use IPv4 server address with IPv6 source address"
msgstr ""

#: option.c:997 option.c:1043
msgid "interface can only be specified once"
msgstr ""

#: option.c:1011 option.c:4785
msgid "bad interface name"
msgstr ""

#: option.c:1037
msgid "cannot use IPv6 server address with IPv4 source address"
msgstr ""

#: option.c:1124
msgid "bad IPv4 prefix length"
msgstr ""

#: option.c:1155 option.c:1165 option.c:1240 option.c:1250 option.c:5360
msgid "error"
msgstr ""

#: option.c:1207
msgid "bad IPv6 prefix length"
msgstr ""

#: option.c:1467
msgid "inappropriate vendor:"
msgstr ""

#: option.c:1474
msgid "inappropriate encap:"
msgstr ""

#: option.c:1500
msgid "unsupported encapsulation for IPv6 option"
msgstr ""

#: option.c:1514
msgid "bad dhcp-option"
msgstr ""

#: option.c:1592
msgid "bad IP address"
msgstr ""

#: option.c:1595 option.c:1734 option.c:3928
msgid "bad IPv6 address"
msgstr ""

#: option.c:1688
msgid "bad IPv4 address"
msgstr ""

#: option.c:1761 option.c:1856
msgid "bad domain in dhcp-option"
msgstr ""

#: option.c:1900
msgid "dhcp-option too long"
msgstr ""

#: option.c:1907
msgid "illegal dhcp-match"
msgstr ""

#: option.c:1966
msgid "illegal repeated flag"
msgstr ""

#: option.c:1974
msgid "illegal repeated keyword"
msgstr ""

#: option.c:2056 option.c:5533
#, c-format
msgid "cannot access directory %s: %s"
msgstr ""

#: option.c:2102 tftp.c:573 dump.c:72
#, c-format
msgid "cannot access %s: %s"
msgstr ""

#: option.c:2219
msgid "setting log facility is not possible under Android"
msgstr ""

#: option.c:2228
msgid "bad log facility"
msgstr ""

#: option.c:2281
msgid "bad MX preference"
msgstr ""

#: option.c:2289
msgid "bad MX name"
msgstr ""

#: option.c:2304
msgid "bad MX target"
msgstr ""

#: option.c:2324
msgid "recompile with HAVE_SCRIPT defined to enable lease-change scripts"
msgstr ""

#: option.c:2328
msgid "recompile with HAVE_LUASCRIPT defined to enable Lua scripts"
msgstr ""

#: option.c:2447
msgid "invalid auth-zone"
msgstr ""

#: option.c:2589 option.c:2621
msgid "bad prefix length"
msgstr ""

#: option.c:2601 option.c:2642 option.c:2696
msgid "bad prefix"
msgstr ""

#: option.c:2716
msgid "prefix length too small"
msgstr ""

#: option.c:3010
msgid "Bad address in --address"
msgstr ""

#: option.c:3110
msgid "recompile with HAVE_IPSET defined to enable ipset directives"
msgstr ""

#: option.c:3117
msgid "recompile with HAVE_NFTSET defined to enable nftset directives"
msgstr ""

#: option.c:3192 option.c:3210
msgid "recompile with HAVE_CONNTRACK defined to enable connmark-allowlist directives"
msgstr ""

#: option.c:3496
msgid "bad port range"
msgstr ""

#: option.c:3522
msgid "bad bridge-interface"
msgstr ""

#: option.c:3566
msgid "bad shared-network"
msgstr ""

#: option.c:3620
msgid "only one tag allowed"
msgstr ""

#: option.c:3641 option.c:3657 option.c:3783 option.c:3791 option.c:3834
msgid "bad dhcp-range"
msgstr ""

#: option.c:3675
msgid "inconsistent DHCP range"
msgstr ""

#: option.c:3741
msgid "prefix length must be exactly 64 for RA subnets"
msgstr ""

#: option.c:3743
msgid "prefix length must be exactly 64 for subnet constructors"
msgstr ""

#: option.c:3746
msgid "prefix length must be at least 64"
msgstr ""

#: option.c:3749
msgid "inconsistent DHCPv6 range"
msgstr ""

#: option.c:3768
msgid "prefix must be zero with \"constructor:\" argument"
msgstr ""

#: option.c:3893 option.c:3971
msgid "bad hex constant"
msgstr ""

#: option.c:3946
msgid "bad IPv6 prefix"
msgstr ""

#: option.c:3994
#, c-format
msgid "duplicate dhcp-host IP address %s"
msgstr ""

#: option.c:4056
msgid "bad DHCP host name"
msgstr ""

#: option.c:4142
msgid "bad tag-if"
msgstr ""

#: option.c:4490 option.c:5046
msgid "invalid port number"
msgstr ""

#: option.c:4546
msgid "bad dhcp-proxy address"
msgstr ""

#: option.c:4627
msgid "Bad dhcp-relay"
msgstr ""

#: option.c:4671
msgid "bad RA-params"
msgstr ""

#: option.c:4681
msgid "bad DUID"
msgstr ""

#: option.c:4715
msgid "missing address in alias"
msgstr ""

#: option.c:4721
msgid "invalid alias range"
msgstr ""

#: option.c:4770
msgid "missing address in dynamic host"
msgstr ""

#: option.c:4785
msgid "bad dynamic host"
msgstr ""

#: option.c:4803 option.c:4819
msgid "bad CNAME"
msgstr ""

#: option.c:4827
msgid "duplicate CNAME"
msgstr ""

#: option.c:4854
msgid "bad PTR record"
msgstr ""

#: option.c:4889
msgid "bad NAPTR record"
msgstr ""

#: option.c:4925
msgid "bad RR record"
msgstr ""

#: option.c:4958
msgid "bad CAA record"
msgstr ""

#: option.c:4987
msgid "bad TXT record"
msgstr ""

#: option.c:5030
msgid "bad SRV record"
msgstr ""

#: option.c:5037
msgid "bad SRV target"
msgstr ""

#: option.c:5056
msgid "invalid priority"
msgstr ""

#: option.c:5061
msgid "invalid weight"
msgstr ""

#: option.c:5084
msgid "Bad host-record"
msgstr ""

#: option.c:5123
msgid "Bad name in host-record"
msgstr ""

#: option.c:5165
msgid "bad value for dnssec-check-unsigned"
msgstr ""

#: option.c:5201
msgid "bad trust anchor"
msgstr ""

#: option.c:5217
msgid "bad HEX in trust anchor"
msgstr ""

#: option.c:5228
msgid "unsupported option (check that dnsmasq was compiled with DHCP/TFTP/DNSSEC/DBus support)"
msgstr ""

#: option.c:5290
msgid "missing \""
msgstr ""

#: option.c:5347
msgid "bad option"
msgstr ""

#: option.c:5349
msgid "extraneous parameter"
msgstr ""

#: option.c:5351
msgid "missing parameter"
msgstr ""

#: option.c:5353
msgid "illegal option"
msgstr ""

#: option.c:5363
#, c-format
msgid " in output from %s"
msgstr ""

#: option.c:5365
#, c-format
msgid " at line %d of %s"
msgstr ""

#: option.c:5380 option.c:5683 option.c:5694
#, c-format
msgid "read %s"
msgstr ""

#: option.c:5446
#, c-format
msgid "cannot execute %s: %s"
msgstr ""

#: option.c:5454 option.c:5615 tftp.c:790
#, c-format
msgid "cannot read %s: %s"
msgstr ""

#: option.c:5473
#, c-format
msgid "error executing %s: %s"
msgstr ""

#: option.c:5476
#, c-format
msgid "%s returns non-zero error code"
msgstr ""

#: option.c:5775
msgid "junk found in command line"
msgstr ""

#: option.c:5815
#, c-format
msgid "Dnsmasq version %s  %s\n"
msgstr ""

#: option.c:5816
#, c-format
msgid ""
"Compile time options: %s\n"
"\n"
msgstr ""

#: option.c:5817
#, c-format
msgid "This software comes with ABSOLUTELY NO WARRANTY.\n"
msgstr ""

#: option.c:5818
#, c-format
msgid "Dnsmasq is free software, and you are welcome to redistribute it\n"
msgstr ""

#: option.c:5819
#, c-format
msgid "under the terms of the GNU General Public License, version 2 or 3.\n"
msgstr ""

#: option.c:5836
msgid "try --help"
msgstr ""

#: option.c:5838
msgid "try -w"
msgstr ""

#: option.c:5840
#, c-format
msgid "bad command line options: %s"
msgstr ""

#: option.c:5909
#, c-format
msgid "CNAME loop involving %s"
msgstr ""

#: option.c:5950
#, c-format
msgid "cannot get host-name: %s"
msgstr ""

#: option.c:5978
msgid "only one resolv.conf file allowed in no-poll mode."
msgstr ""

#: option.c:5988
msgid "must have exactly one resolv.conf to read domain from."
msgstr ""

#: option.c:5991 network.c:1727 dhcp.c:892
#, c-format
msgid "failed to read %s: %s"
msgstr ""

#: option.c:6008
#, c-format
msgid "no search directive found in %s"
msgstr ""

#: option.c:6029
msgid "there must be a default domain when --dhcp-fqdn is set"
msgstr ""

#: option.c:6038
msgid "syntax check OK"
msgstr ""

#: forward.c:107
#, c-format
msgid "failed to send packet: %s"
msgstr ""

#: forward.c:715
msgid "discarding DNS reply: subnet option mismatch"
msgstr ""

#: forward.c:780
#, c-format
msgid "nameserver %s refused to do a recursive query"
msgstr ""

#: forward.c:826
#, c-format
msgid "possible DNS-rebind attack detected: %s"
msgstr ""

#: forward.c:1239
#, c-format
msgid "reducing DNS packet size for nameserver %s to %d"
msgstr ""

#: forward.c:1565
#, c-format
msgid "ignoring query from non-local network %s (logged only once)"
msgstr ""

#: forward.c:2139
#, c-format
msgid "ignoring query from non-local network %s"
msgstr ""

#: forward.c:2501
#, c-format
msgid "failed to bind server socket to %s: %s"
msgstr ""

#: forward.c:2867
#, c-format
msgid "Maximum number of concurrent DNS queries reached (max: %d)"
msgstr ""

#: forward.c:2869
#, c-format
msgid "Maximum number of concurrent DNS queries to %s reached (max: %d)"
msgstr ""

#: network.c:700
#, c-format
msgid "stopped listening on %s(#%d): %s port %d"
msgstr ""

#: network.c:911
#, c-format
msgid "failed to create listening socket for %s: %s"
msgstr ""

#: network.c:1192
#, c-format
msgid "listening on %s(#%d): %s port %d"
msgstr ""

#: network.c:1219
#, c-format
msgid "listening on %s port %d"
msgstr ""

#: network.c:1252
#, c-format
msgid "LOUD WARNING: listening on %s may accept requests via interfaces other than %s"
msgstr ""

#: network.c:1259
msgid "LOUD WARNING: use --bind-dynamic rather than --bind-interfaces to avoid DNS amplification attacks via these interface(s)"
msgstr ""

#: network.c:1268
#, c-format
msgid "warning: using interface %s instead"
msgstr ""

#: network.c:1277
#, c-format
msgid "warning: no addresses found for interface %s"
msgstr ""

#: network.c:1335
#, c-format
msgid "interface %s failed to join DHCPv6 multicast group: %s"
msgstr ""

#: network.c:1340
msgid "try increasing /proc/sys/net/core/optmem_max"
msgstr ""

#: network.c:1545
#, c-format
msgid "failed to bind server socket for %s: %s"
msgstr ""

#: network.c:1622
#, c-format
msgid "ignoring nameserver %s - local interface"
msgstr ""

#: network.c:1633
#, c-format
msgid "ignoring nameserver %s - cannot make/bind socket: %s"
msgstr ""

#: network.c:1643
msgid "more servers are defined but not logged"
msgstr ""

#: network.c:1654
msgid "(no DNSSEC)"
msgstr ""

#: network.c:1657
msgid "unqualified"
msgstr ""

#: network.c:1657
msgid "names"
msgstr ""

#: network.c:1659
msgid "default"
msgstr ""

#: network.c:1661
msgid "domain"
msgstr ""

#: network.c:1663
#, c-format
msgid "using nameserver %s#%d for %s %s%s %s"
msgstr ""

#: network.c:1667
#, c-format
msgid "NOT using nameserver %s#%d - query loop detected"
msgstr ""

#: network.c:1670
#, c-format
msgid "using nameserver %s#%d(via %s)"
msgstr ""

#: network.c:1672
#, c-format
msgid "using nameserver %s#%d"
msgstr ""

#: network.c:1687
#, c-format
msgid "using only locally-known addresses for %s"
msgstr ""

#: network.c:1690
#, c-format
msgid "using standard nameservers for %s"
msgstr ""

#: network.c:1694
#, c-format
msgid "using %d more local addresses"
msgstr ""

#: network.c:1696
#, c-format
msgid "using %d more nameservers"
msgstr ""

#: dnsmasq.c:192
msgid "dhcp-hostsdir, dhcp-optsdir and hostsdir are not supported on this platform"
msgstr ""

#: dnsmasq.c:207
msgid "no root trust anchor provided for DNSSEC"
msgstr ""

#: dnsmasq.c:210
msgid "cannot reduce cache size from default when DNSSEC enabled"
msgstr ""

#: dnsmasq.c:212
msgid "DNSSEC not available: set HAVE_DNSSEC in src/config.h"
msgstr ""

#: dnsmasq.c:218
msgid "TFTP server not available: set HAVE_TFTP in src/config.h"
msgstr ""

#: dnsmasq.c:225
msgid "cannot use --conntrack AND --query-port"
msgstr ""

#: dnsmasq.c:231
msgid "conntrack support not available: set HAVE_CONNTRACK in src/config.h"
msgstr ""

#: dnsmasq.c:236
msgid "asynchronous logging is not available under Solaris"
msgstr ""

#: dnsmasq.c:241
msgid "asynchronous logging is not available under Android"
msgstr ""

#: dnsmasq.c:246
msgid "authoritative DNS not available: set HAVE_AUTH in src/config.h"
msgstr ""

#: dnsmasq.c:251
msgid "loop detection not available: set HAVE_LOOP in src/config.h"
msgstr ""

#: dnsmasq.c:256
msgid "Ubus not available: set HAVE_UBUS in src/config.h"
msgstr ""

#: dnsmasq.c:267
msgid "max_port cannot be smaller than min_port"
msgstr ""

#: dnsmasq.c:271
msgid "port_limit must not be larger than available port range"
msgstr ""

#: dnsmasq.c:278
msgid "--auth-server required when an auth zone is defined."
msgstr ""

#: dnsmasq.c:283
msgid "zone serial must be configured in --auth-soa"
msgstr ""

#: dnsmasq.c:303
msgid "dhcp-range constructor not available on this platform"
msgstr ""

#: dnsmasq.c:377
msgid "cannot set --bind-interfaces and --bind-dynamic"
msgstr ""

#: dnsmasq.c:380
#, c-format
msgid "failed to find list of interfaces: %s"
msgstr ""

#: dnsmasq.c:389
#, c-format
msgid "unknown interface %s"
msgstr ""

#: dnsmasq.c:396
#, c-format
msgid "failed to set SO_BINDTODEVICE on DHCP socket: %s"
msgstr ""

#: dnsmasq.c:440
msgid "Packet dumps not available: set HAVE_DUMP in src/config.h"
msgstr ""

#: dnsmasq.c:448 dnsmasq.c:1232
#, c-format
msgid "DBus error: %s"
msgstr ""

#: dnsmasq.c:451
msgid "DBus not available: set HAVE_DBUS in src/config.h"
msgstr ""

#: dnsmasq.c:459 dnsmasq.c:1253
#, c-format
msgid "UBus error: %s"
msgstr ""

#: dnsmasq.c:462
msgid "UBus not available: set HAVE_UBUS in src/config.h"
msgstr ""

#: dnsmasq.c:492
#, c-format
msgid "unknown user or group: %s"
msgstr ""

#: dnsmasq.c:568
#, c-format
msgid "process is missing required capability %s"
msgstr ""

#: dnsmasq.c:600
#, c-format
msgid "cannot chdir to filesystem root: %s"
msgstr ""

#: dnsmasq.c:852
#, c-format
msgid "started, version %s DNS disabled"
msgstr ""

#: dnsmasq.c:857
#, c-format
msgid "started, version %s cachesize %d"
msgstr ""

#: dnsmasq.c:859
msgid "cache size greater than 10000 may cause performance issues, and is unlikely to be useful."
msgstr ""

#: dnsmasq.c:862
#, c-format
msgid "started, version %s cache disabled"
msgstr ""

#: dnsmasq.c:865
msgid "DNS service limited to local subnets"
msgstr ""

#: dnsmasq.c:868
#, c-format
msgid "compile time options: %s"
msgstr ""

#: dnsmasq.c:877
msgid "DBus support enabled: connected to system bus"
msgstr ""

#: dnsmasq.c:879
msgid "DBus support enabled: bus connection pending"
msgstr ""

#: dnsmasq.c:887
msgid "UBus support enabled: connected to system bus"
msgstr ""

#: dnsmasq.c:889
msgid "UBus support enabled: bus connection pending"
msgstr ""

#: dnsmasq.c:909
msgid "DNSSEC validation enabled but all unsigned answers are trusted"
msgstr ""

#: dnsmasq.c:911
msgid "DNSSEC validation enabled"
msgstr ""

#: dnsmasq.c:915
msgid "DNSSEC signature timestamps not checked until receipt of SIGINT"
msgstr ""

#: dnsmasq.c:918
msgid "DNSSEC signature timestamps not checked until system time valid"
msgstr ""

#: dnsmasq.c:921
#, c-format
msgid "configured with trust anchor for %s keytag %u"
msgstr ""

#: dnsmasq.c:927
#, c-format
msgid "warning: failed to change owner of %s: %s"
msgstr ""

#: dnsmasq.c:932
msgid "setting --bind-interfaces option because of OS limitations"
msgstr ""

#: dnsmasq.c:945
#, c-format
msgid "warning: interface %s does not currently exist"
msgstr ""

#: dnsmasq.c:950
msgid "warning: ignoring resolv-file flag because no-resolv is set"
msgstr ""

#: dnsmasq.c:953
msgid "warning: no upstream servers configured"
msgstr ""

#: dnsmasq.c:957
#, c-format
msgid "asynchronous logging enabled, queue limit is %d messages"
msgstr ""

#: dnsmasq.c:978
msgid "IPv6 router advertisement enabled"
msgstr ""

#: dnsmasq.c:983
#, c-format
msgid "DHCP, sockets bound exclusively to interface %s"
msgstr ""

#: dnsmasq.c:1000
msgid "root is "
msgstr ""

#: dnsmasq.c:1000
msgid "enabled"
msgstr ""

#: dnsmasq.c:1002
msgid "secure mode"
msgstr ""

#: dnsmasq.c:1003
msgid "single port mode"
msgstr ""

#: dnsmasq.c:1006
#, c-format
msgid "warning: %s inaccessible"
msgstr ""

#: dnsmasq.c:1010
#, c-format
msgid "warning: TFTP directory %s inaccessible"
msgstr ""

#: dnsmasq.c:1036
#, c-format
msgid "restricting maximum simultaneous TFTP transfers to %d"
msgstr ""

#: dnsmasq.c:1095
#, c-format
msgid "error binding DHCP socket to device %s"
msgstr ""

#: dnsmasq.c:1229
msgid "connected to system DBus"
msgstr ""

#: dnsmasq.c:1250
msgid "connected to system UBus"
msgstr ""

#: dnsmasq.c:1416
#, c-format
msgid "cannot fork into background: %s"
msgstr ""

#: dnsmasq.c:1420
#, c-format
msgid "failed to create helper: %s"
msgstr ""

#: dnsmasq.c:1424
#, c-format
msgid "setting capabilities failed: %s"
msgstr ""

#: dnsmasq.c:1428
#, c-format
msgid "failed to change user-id to %s: %s"
msgstr ""

#: dnsmasq.c:1432
#, c-format
msgid "failed to change group-id to %s: %s"
msgstr ""

#: dnsmasq.c:1436
#, c-format
msgid "failed to open pidfile %s: %s"
msgstr ""

#: dnsmasq.c:1440
#, c-format
msgid "cannot open log %s: %s"
msgstr ""

#: dnsmasq.c:1444
#, c-format
msgid "failed to load Lua script: %s"
msgstr ""

#: dnsmasq.c:1448
#, c-format
msgid "TFTP directory %s inaccessible: %s"
msgstr ""

#: dnsmasq.c:1452
#, c-format
msgid "cannot create timestamp file %s: %s"
msgstr ""

#: dnsmasq.c:1536
#, c-format
msgid "script process killed by signal %d"
msgstr ""

#: dnsmasq.c:1540
#, c-format
msgid "script process exited with status %d"
msgstr ""

#: dnsmasq.c:1544
#, c-format
msgid "failed to execute %s: %s"
msgstr ""

#: dnsmasq.c:1584
msgid "now checking DNSSEC signature timestamps"
msgstr ""

#: dnsmasq.c:1619 dnssec.c:160 dnssec.c:204
#, c-format
msgid "failed to update mtime on %s: %s"
msgstr ""

#: dnsmasq.c:1631
msgid "exiting on receipt of SIGTERM"
msgstr ""

#: dnsmasq.c:1659
#, c-format
msgid "failed to access %s: %s"
msgstr ""

#: dnsmasq.c:1690
#, c-format
msgid "reading %s"
msgstr ""

#: dnsmasq.c:1706
#, c-format
msgid "no servers found in %s, will retry"
msgstr ""

#: dhcp.c:51
#, c-format
msgid "cannot create DHCP socket: %s"
msgstr ""

#: dhcp.c:66
#, c-format
msgid "failed to set options on DHCP socket: %s"
msgstr ""

#: dhcp.c:87
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCP socket: %s"
msgstr ""

#: dhcp.c:99
#, c-format
msgid "failed to bind DHCP server socket: %s"
msgstr ""

#: dhcp.c:125
#, c-format
msgid "cannot create ICMP raw socket: %s."
msgstr ""

#: dhcp.c:254 dhcp6.c:186
#, c-format
msgid "unknown interface %s in bridge-interface"
msgstr ""

#: dhcp.c:295
#, c-format
msgid "DHCP packet received on %s which has no address"
msgstr ""

#: dhcp.c:429
#, c-format
msgid "ARP-cache injection failed: %s"
msgstr ""

#: dhcp.c:490
#, c-format
msgid "Error sending DHCP packet to %s: %s"
msgstr ""

#: dhcp.c:547
#, c-format
msgid "DHCP range %s -- %s is not consistent with netmask %s"
msgstr ""

#: dhcp.c:930
#, c-format
msgid "bad line at %s line %d"
msgstr ""

#: dhcp.c:973
#, c-format
msgid "ignoring %s line %d, duplicate name or IP address"
msgstr ""

#: dhcp.c:1034
#, c-format
msgid "read %s - %d addresses"
msgstr ""

#: dhcp.c:1136
#, c-format
msgid "Cannot broadcast DHCP relay via interface %s"
msgstr ""

#: dhcp.c:1160
#, c-format
msgid "broadcast via %s"
msgstr ""

#: dhcp.c:1163 rfc3315.c:2219
#, c-format
msgid "DHCP relay at %s -> %s"
msgstr ""

#: lease.c:64
#, c-format
msgid "ignoring invalid line in lease database: %s %s %s %s ..."
msgstr ""

#: lease.c:101
#, c-format
msgid "ignoring invalid line in lease database, bad address: %s"
msgstr ""

#: lease.c:108
msgid "too many stored leases"
msgstr ""

#: lease.c:176
#, c-format
msgid "cannot open or create lease file %s: %s"
msgstr ""

#: lease.c:185
msgid "failed to parse lease database cleanly"
msgstr ""

#: lease.c:188
#, c-format
msgid "failed to read lease file %s: %s"
msgstr ""

#: lease.c:204
#, c-format
msgid "cannot run lease-init script %s: %s"
msgstr ""

#: lease.c:210
#, c-format
msgid "lease-init script returned exit code %s"
msgstr ""

#: lease.c:381
#, c-format
msgid "failed to write %s: %s (retry in %u s)"
msgstr ""

#: lease.c:955
#, c-format
msgid "Ignoring domain %s for DHCP host name %s"
msgstr ""

#: rfc2131.c:378
msgid "with subnet selector"
msgstr ""

#: rfc2131.c:383
msgid "via"
msgstr ""

#: rfc2131.c:389
#, c-format
msgid "no address range available for DHCP request %s %s"
msgstr ""

#: rfc2131.c:403
#, c-format
msgid "%u available DHCP subnet: %s/%s"
msgstr ""

#: rfc2131.c:409 rfc3315.c:320
#, c-format
msgid "%u available DHCP range: %s -- %s"
msgstr ""

#: rfc2131.c:521
#, c-format
msgid "%u vendor class: %s"
msgstr ""

#: rfc2131.c:523
#, c-format
msgid "%u user class: %s"
msgstr ""

#: rfc2131.c:557
msgid "disabled"
msgstr ""

#: rfc2131.c:598 rfc2131.c:1087 rfc2131.c:1536 rfc3315.c:633 rfc3315.c:816
#: rfc3315.c:1122
msgid "ignored"
msgstr ""

#: rfc2131.c:613 rfc2131.c:1340 rfc3315.c:868
msgid "address in use"
msgstr ""

#: rfc2131.c:627 rfc2131.c:1141
msgid "no address available"
msgstr ""

#: rfc2131.c:634 rfc2131.c:1302
msgid "wrong network"
msgstr ""

#: rfc2131.c:649
msgid "no address configured"
msgstr ""

#: rfc2131.c:655 rfc2131.c:1353
msgid "no leases left"
msgstr ""

#: rfc2131.c:756 rfc3315.c:500
#, c-format
msgid "%u client provides name: %s"
msgstr ""

#: rfc2131.c:885
msgid "PXE BIS not supported"
msgstr ""

#: rfc2131.c:1054 rfc3315.c:1223
#, c-format
msgid "disabling DHCP static address %s for %s"
msgstr ""

#: rfc2131.c:1075
msgid "unknown lease"
msgstr ""

#: rfc2131.c:1110
#, c-format
msgid "not using configured address %s because it is leased to %s"
msgstr ""

#: rfc2131.c:1120
#, c-format
msgid "not using configured address %s because it is in use by the server or relay"
msgstr ""

#: rfc2131.c:1123
#, c-format
msgid "not using configured address %s because it was previously declined"
msgstr ""

#: rfc2131.c:1139 rfc2131.c:1346
msgid "no unique-id"
msgstr ""

#: rfc2131.c:1238
msgid "wrong server-ID"
msgstr ""

#: rfc2131.c:1257
msgid "wrong address"
msgstr ""

#: rfc2131.c:1275 rfc3315.c:976
msgid "lease not found"
msgstr ""

#: rfc2131.c:1310
msgid "address not available"
msgstr ""

#: rfc2131.c:1321
msgid "static lease available"
msgstr ""

#: rfc2131.c:1325
msgid "address reserved"
msgstr ""

#: rfc2131.c:1334
#, c-format
msgid "abandoning lease to %s of %s"
msgstr ""

#: rfc2131.c:1870
#, c-format
msgid "%u bootfile name: %s"
msgstr ""

#: rfc2131.c:1879
#, c-format
msgid "%u server name: %s"
msgstr ""

#: rfc2131.c:1889
#, c-format
msgid "%u next server: %s"
msgstr ""

#: rfc2131.c:1893
#, c-format
msgid "%u broadcast response"
msgstr ""

#: rfc2131.c:1956
#, c-format
msgid "cannot send DHCP/BOOTP option %d: no space left in packet"
msgstr ""

#: rfc2131.c:2267
msgid "PXE menu too large"
msgstr ""

#: rfc2131.c:2430 rfc3315.c:1517
#, c-format
msgid "%u requested options: %s"
msgstr ""

#: rfc2131.c:2747
#, c-format
msgid "cannot send RFC3925 option: too many options for enterprise number %d"
msgstr ""

#: rfc2131.c:2810
#, c-format
msgid "%u reply delay: %d"
msgstr ""

#: netlink.c:86
#, c-format
msgid "cannot create netlink socket: %s"
msgstr ""

#: netlink.c:379
#, c-format
msgid "netlink returns error: %s"
msgstr ""

#: dbus.c:491
#, c-format
msgid "Enabling --%s option from D-Bus"
msgstr ""

#: dbus.c:496
#, c-format
msgid "Disabling --%s option from D-Bus"
msgstr ""

#: dbus.c:857
msgid "setting upstream servers from DBus"
msgstr ""

#: dbus.c:907
msgid "could not register a DBus message handler"
msgstr ""

#: bpf.c:261
#, c-format
msgid "cannot create DHCP BPF socket: %s"
msgstr ""

#: bpf.c:289
#, c-format
msgid "DHCP request for unsupported hardware type (%d) received on %s"
msgstr ""

#: bpf.c:374
#, c-format
msgid "cannot create PF_ROUTE socket: %s"
msgstr ""

#: bpf.c:395
msgid "Unknown protocol version from route socket"
msgstr ""

#: helper.c:150
msgid "lease() function missing in Lua script"
msgstr ""

#: tftp.c:353
msgid "unable to get free port for TFTP"
msgstr ""

#: tftp.c:369
#, c-format
msgid "unsupported request from %s"
msgstr ""

#: tftp.c:520
#, c-format
msgid "file %s not found for %s"
msgstr ""

#: tftp.c:609
#, c-format
msgid "ignoring packet from %s (TID mismatch)"
msgstr ""

#: tftp.c:662
#, c-format
msgid "failed sending %s to %s"
msgstr ""

#: tftp.c:662
#, c-format
msgid "sent %s to %s"
msgstr ""

#: tftp.c:712
#, c-format
msgid "error %d %s received from %s"
msgstr ""

#: log.c:203
#, c-format
msgid "overflow: %d log entries lost"
msgstr ""

#: log.c:281
#, c-format
msgid "log failed: %s"
msgstr ""

#: log.c:490
msgid "FAILED to start up"
msgstr ""

#: conntrack.c:63
#, c-format
msgid "Conntrack connection mark retrieval failed: %s"
msgstr ""

#: dhcp6.c:51
#, c-format
msgid "cannot create DHCPv6 socket: %s"
msgstr ""

#: dhcp6.c:72
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCPv6 socket: %s"
msgstr ""

#: dhcp6.c:84
#, c-format
msgid "failed to bind DHCPv6 server socket: %s"
msgstr ""

#: rfc3315.c:173
#, c-format
msgid "no address range available for DHCPv6 request from relay at %s"
msgstr ""

#: rfc3315.c:182
#, c-format
msgid "no address range available for DHCPv6 request via %s"
msgstr ""

#: rfc3315.c:317
#, c-format
msgid "%u available DHCPv6 subnet: %s/%d"
msgstr ""

#: rfc3315.c:400
#, c-format
msgid "%u vendor class: %u"
msgstr ""

#: rfc3315.c:448
#, c-format
msgid "%u client MAC address: %s"
msgstr ""

#: rfc3315.c:763 rfc3315.c:860
msgid "address unavailable"
msgstr ""

#: rfc3315.c:775 rfc3315.c:904 rfc3315.c:1273
msgid "success"
msgstr ""

#: rfc3315.c:790 rfc3315.c:799 rfc3315.c:912 rfc3315.c:914 rfc3315.c:1048
msgid "no addresses available"
msgstr ""

#: rfc3315.c:891
msgid "not on link"
msgstr ""

#: rfc3315.c:980 rfc3315.c:1181 rfc3315.c:1262
msgid "no binding found"
msgstr ""

#: rfc3315.c:1017
msgid "deprecated"
msgstr ""

#: rfc3315.c:1024
msgid "address invalid"
msgstr ""

#: rfc3315.c:1082 rfc3315.c:1084
msgid "confirm failed"
msgstr ""

#: rfc3315.c:1099
msgid "all addresses still on link"
msgstr ""

#: rfc3315.c:1190
msgid "release received"
msgstr ""

#: rfc3315.c:2200
#, c-format
msgid "Cannot multicast DHCP relay via interface %s"
msgstr ""

#: rfc3315.c:2216
#, c-format
msgid "multicast via %s"
msgstr ""

#: dhcp-common.c:187
#, c-format
msgid "Ignoring duplicate dhcp-option %d"
msgstr ""

#: dhcp-common.c:264
#, c-format
msgid "%u tags: %s"
msgstr ""

#: dhcp-common.c:484
#, c-format
msgid "%s has more than one address in hostsfile, using %s for DHCP"
msgstr ""

#: dhcp-common.c:518
#, c-format
msgid "duplicate IP address %s (%s) in dhcp-config directive"
msgstr ""

#: dhcp-common.c:738
#, c-format
msgid "Known DHCP options:\n"
msgstr ""

#: dhcp-common.c:749
#, c-format
msgid "Known DHCPv6 options:\n"
msgstr ""

#: dhcp-common.c:946
msgid ", prefix deprecated"
msgstr ""

#: dhcp-common.c:949
#, c-format
msgid ", lease time "
msgstr ""

#: dhcp-common.c:991
#, c-format
msgid "%s stateless on %s%.0s%.0s%s"
msgstr ""

#: dhcp-common.c:993
#, c-format
msgid "%s, static leases only on %.0s%s%s%.0s"
msgstr ""

#: dhcp-common.c:995
#, c-format
msgid "%s, proxy on subnet %.0s%s%.0s%.0s"
msgstr ""

#: dhcp-common.c:996
#, c-format
msgid "%s, IP range %s -- %s%s%.0s"
msgstr ""

#: dhcp-common.c:1009
#, c-format
msgid "DHCPv4-derived IPv6 names on %s%s"
msgstr ""

#: dhcp-common.c:1012
#, c-format
msgid "router advertisement on %s%s"
msgstr ""

#: dhcp-common.c:1043
#, c-format
msgid "DHCP relay from %s via %s"
msgstr ""

#: dhcp-common.c:1045
#, c-format
msgid "DHCP relay from %s to %s via %s"
msgstr ""

#: dhcp-common.c:1048
#, c-format
msgid "DHCP relay from %s to %s"
msgstr ""

#: radv.c:110
#, c-format
msgid "cannot create ICMPv6 socket: %s"
msgstr ""

#: auth.c:462
#, c-format
msgid "ignoring zone transfer request from %s"
msgstr ""

#: ipset.c:99
#, c-format
msgid "failed to create IPset control socket: %s"
msgstr ""

#: ipset.c:211
#, c-format
msgid "failed to update ipset %s: %s"
msgstr ""

#: pattern.c:29
#, c-format
msgid "[pattern.c:%d] Assertion failure: %s"
msgstr ""

#: pattern.c:142
#, c-format
msgid "Invalid DNS name: Invalid character %c."
msgstr ""

#: pattern.c:151
msgid "Invalid DNS name: Empty label."
msgstr ""

#: pattern.c:156
msgid "Invalid DNS name: Label starts with hyphen."
msgstr ""

#: pattern.c:170
msgid "Invalid DNS name: Label ends with hyphen."
msgstr ""

#: pattern.c:176
#, c-format
msgid "Invalid DNS name: Label is too long (%zu)."
msgstr ""

#: pattern.c:184
#, c-format
msgid "Invalid DNS name: Not enough labels (%zu)."
msgstr ""

#: pattern.c:189
msgid "Invalid DNS name: Final label is fully numeric."
msgstr ""

#: pattern.c:199
msgid "Invalid DNS name: \"local\" pseudo-TLD."
msgstr ""

#: pattern.c:204
#, c-format
msgid "DNS name has invalid length (%zu)."
msgstr ""

#: pattern.c:258
#, c-format
msgid "Invalid DNS name pattern: Invalid character %c."
msgstr ""

#: pattern.c:267
msgid "Invalid DNS name pattern: Empty label."
msgstr ""

#: pattern.c:272
msgid "Invalid DNS name pattern: Label starts with hyphen."
msgstr ""

#: pattern.c:285
msgid "Invalid DNS name pattern: Wildcard character used more than twice per label."
msgstr ""

#: pattern.c:295
msgid "Invalid DNS name pattern: Label ends with hyphen."
msgstr ""

#: pattern.c:301
#, c-format
msgid "Invalid DNS name pattern: Label is too long (%zu)."
msgstr ""

#: pattern.c:309
#, c-format
msgid "Invalid DNS name pattern: Not enough labels (%zu)."
msgstr ""

#: pattern.c:314
msgid "Invalid DNS name pattern: Wildcard within final two labels."
msgstr ""

#: pattern.c:319
msgid "Invalid DNS name pattern: Final label is fully numeric."
msgstr ""

#: pattern.c:329
msgid "Invalid DNS name pattern: \"local\" pseudo-TLD."
msgstr ""

#: pattern.c:334
#, c-format
msgid "DNS name pattern has invalid length after removing wildcards (%zu)."
msgstr ""

#: dnssec.c:206
msgid "system time considered valid, now checking DNSSEC signature timestamps."
msgstr ""

#: dnssec.c:1018
#, c-format
msgid "Insecure DS reply received for %s, check domain configuration and upstream DNS server DNSSEC support"
msgstr ""

#: blockdata.c:55
#, c-format
msgid "pool memory in use %zu, max %zu, allocated %zu"
msgstr ""

#: tables.c:61
#, c-format
msgid "failed to access pf devices: %s"
msgstr ""

#: tables.c:74
#, c-format
msgid "warning: no opened pf devices %s"
msgstr ""

#: tables.c:82
#, c-format
msgid "error: cannot use table name %s"
msgstr ""

#: tables.c:90
#, c-format
msgid "error: cannot strlcpy table name %s"
msgstr ""

#: tables.c:101
#, c-format
msgid "IPset: error: %s"
msgstr ""

#: tables.c:108
msgid "info: table created"
msgstr ""

#: tables.c:133
#, c-format
msgid "warning: DIOCR%sADDRS: %s"
msgstr ""

#: tables.c:137
#, c-format
msgid "%d addresses %s"
msgstr ""

#: inotify.c:62
#, c-format
msgid "cannot access path %s: %s"
msgstr ""

#: inotify.c:95
#, c-format
msgid "failed to create inotify: %s"
msgstr ""

#: inotify.c:111
#, c-format
msgid "too many symlinks following %s"
msgstr ""

#: inotify.c:127
#, c-format
msgid "directory %s for resolv-file is missing, cannot poll"
msgstr ""

#: inotify.c:131 inotify.c:200
#, c-format
msgid "failed to create inotify for %s: %s"
msgstr ""

#: inotify.c:178 inotify.c:185
#, c-format
msgid "bad dynamic directory %s: %s"
msgstr ""

#: inotify.c:186
msgid "not a directory"
msgstr ""

#: inotify.c:299
#, c-format
msgid "inotify: %s removed"
msgstr ""

#: inotify.c:301
#, c-format
msgid "inotify: %s new or modified"
msgstr ""

#: inotify.c:309
#, c-format
msgid "inotify: flushed %u names read from %s"
msgstr ""

#: dump.c:68
#, c-format
msgid "cannot create %s: %s"
msgstr ""

#: dump.c:74
#, c-format
msgid "bad header in %s"
msgstr ""

#: dump.c:287
msgid "failed to write packet dump"
msgstr ""

#: dump.c:289
#, c-format
msgid "%u dumping packet %u mask 0x%04x"
msgstr ""

#: dump.c:291
#, c-format
msgid "dumping packet %u mask 0x%04x"
msgstr ""

#: ubus.c:79
#, c-format
msgid "UBus subscription callback: %s subscriber(s)"
msgstr ""

#: ubus.c:99
#, c-format
msgid "Cannot reconnect to UBus: %s"
msgstr ""

#: ubus.c:135
msgid "Cannot set UBus listeners: no connection"
msgstr ""

#: ubus.c:155
msgid "Cannot poll UBus listeners: no connection"
msgstr ""

#: ubus.c:168
msgid "Disconnecting from UBus"
msgstr ""

#: ubus.c:179 ubus.c:326
#, c-format
msgid "UBus command failed: %d (%s)"
msgstr ""

#: hash-questions.c:40
msgid "Failed to create SHA-256 hash object"
msgstr ""

#: nftset.c:35
msgid "failed to create nftset context"
msgstr ""
