Current dnsmasq Version: 2.90
Files with CP changes: 
Makefile, cache.c, config.h, dhcp.c, dns-protocol.h, dnsmasq.c, dnsmasq.h,
edns0.c, option.c, util.c, forward.c
==========
09-20-2024 - Upgrade to stable v2.90
07-15-2021 - PV: Upgrade to stable v2.85 for CVE-2021-3448
01-20-2021 - KZ: Upgrade to stable v2.83 for CVE-2020-25681 through CVE-25687 (seven total)
10-02-2017 - SF: Upgrade to stable v2.78
08-12-2015 - AC: Upgrade to stable version v2.75
05-06-2015 - sdb: import fix for CVE-2015-3294
01-22-2015 - rk: Upgrade to stable version v2.72
08-20-2014 - rk: Upgrade to stable version v2.71
05-05-2014 - sdb: Upgrade to stable version v2.70 - five commits: this and four 'DNSMASQ: *'
08-28-2013 - SDB: allow non-standard port in /etc/resolv.conf bug#21210 - 9642:6a1c9815cce3
06-17-2013 - SF: Fixing opendns support - 8896:01709c19ccd1
06-12-2013 - AC: Re-add forced interface mode for ippt - 8809:1f86188266b1
04-19-2013 - SF: Upgraded to stable version 2.66 - 8295:28a81678c1fb
03-05-2013 - SF: cloned from git repo:
	Revision: HEAD-c796107 (index 50b17aa..854992b 100644)
==========
Website: http://www.thekelleys.org.uk/dnsmasq/doc.html
Repo: git://thekelleys.org.uk/dnsmasq.git 
