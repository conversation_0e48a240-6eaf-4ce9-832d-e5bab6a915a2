#!/usr/bin/make -f
# -*- makefile -*-

# Uncomment this to turn on verbose mode.
export DH_VERBOSE=1

# Make sure lintian does not complain about missing hardenings.
export DEB_BUILD_MAINT_OPTIONS = hardening=+all

include /usr/share/dpkg/architecture.mk

PREFIX = /usr
# Upstream does not handle CPPFLAGS, so we add it to CFLAGS here.
CFLAGS += $(CPPFLAGS)
COPTS =

ifeq (,$(filter nodbus,$(DEB_BUILD_OPTIONS)))
	COPTS += -DHAVE_DBUS
endif

ifeq (,$(filter noidn, $(DEB_BUILD_OPTIONS)))
	COPTS += -DHAVE_LIBIDN2
endif

ifeq (,$(filter nonftset, $(DEB_BUILD_OPTIONS)))
	COPTS += -DHAVE_NFTSET
endif

ifeq (,$(filter noconntrack,$(DEB_BUILD_OPTIONS)))
ifeq ($(DEB_HOST_ARCH_OS),linux)
	COPTS += -DHAVE_CONNTRACK
endif
endif

ifneq (,$(filter noipset,$(DEB_BUILD_OPTIONS)))
	COPTS += -DNO_IPSET
endif

ifneq (,$(filter nodhcp6,$(DEB_BUILD_OPTIONS)))
	COPTS += -DNO_DHCP6
endif

ifneq (,$(filter noipv6,$(DEB_BUILD_OPTIONS)))
	COPTS += -DNO_IPV6
endif

ifneq (,$(filter notftp,$(DEB_BUILD_OPTIONS)))
	COPTS += -DNO_TFTP
endif

ifneq (,$(filter nodhcp,$(DEB_BUILD_OPTIONS)))
	COPTS += -DNO_DHCP
endif

ifneq (,$(filter noscript,$(DEB_BUILD_OPTIONS)))
	COPTS += -DNO_SCRIPT
endif

ifneq (,$(filter nortc,$(DEB_BUILD_OPTIONS)))
	COPTS += -DHAVE_BROKEN_RTC
endif

ifeq (,$(filter nodnssec,$(DEB_BUILD_OPTIONS)))
	COPTS += -DHAVE_DNSSEC
endif


%:
	# Ubuntu and derivates do not support runit, see
	# https://bugs.debian.org/960401 for details.
	if dpkg-vendor --derives-from Ubuntu; then \
		dh $@; \
	else \
		dh $@ --with runit; \
	fi

# Upstream builds and installs in one go, so do we.
override_dh_auto_build:

override_dh_auto_install:
	dh_auto_build -p dnsmasq-base --no-parallel -- install-i18n \
		BUILDDIR=debian/auto-build/dnsmasq-base \
		DESTDIR=$(CURDIR)/debian/dnsmasq-base \
		PREFIX=$(PREFIX) CFLAGS="$(CFLAGS)" LDFLAGS="$(LDFLAGS)" \
		COPTS="$(COPTS)"
	dh_auto_build -p dnsmasq-base-lua --no-parallel -- install-i18n \
		BUILDDIR=debian/auto-build/dnsmasq-base-lua \
		DESTDIR=$(CURDIR)/debian/dnsmasq-base-lua \
		PREFIX=$(PREFIX) CFLAGS="$(CFLAGS)" LDFLAGS="$(LDFLAGS)" \
		LUA=lua5.4 COPTS="$(COPTS) -DHAVE_LUASCRIPT"
	dh_auto_build -p dnsmasq-utils -D contrib/lease-tools

override_dh_auto_clean:
	dh_auto_clean -p dnsmasq-base -- \
		BUILDDIR=debian/auto-build/dnsmasq-base
	dh_auto_clean -p dnsmasq-base-lua -- \
		BUILDDIR=debian/auto-build/dnsmasq-base-lua
	rm -rf debian/auto-build
	dh_auto_clean -p dnsmasq-utils -D contrib/lease-tools

override_dh_install:
	dh_install -p dnsmasq-utils --sourcedir=contrib/lease-tools
	dh_install --remaining-packages

# If 'nodoc' is absent from DEB_BUILD_OPTIONS, Correct name or location of
# some doc files.
# We would prefer do this via dh-exec if it would support dh_installdocs.
ifeq (,$(findstring nodoc,$(DEB_BUILD_OPTIONS)))
execute_after_dh_installdocs:
	for d in $(CURDIR)/debian/dnsmasq-base*/usr/share/doc/dnsmasq-base*; do \
		cd $$d; \
		mv readme README.Debian; \
		mv CHANGELOG.archive changelog.archive; \
		mkdir examples; \
		mv dnsmasq.conf.example examples/; \
	done
endif

# If 'nodoc' is present in DEB_BUILD_OPTIONS, drop the man pages already
# installed by the upstream build script. Then, let dh_installman do what
# else needs doing.
override_dh_installman:
ifneq (,$(findstring nodoc,$(DEB_BUILD_OPTIONS)))
	rm -rf debian/dnsmasq-base*/usr/share/man
endif
	dh_installman -p dnsmasq-utils --sourcedir=contrib/lease-tools
	dh_installman --remaining-packages
