#!/bin/sh
set -e

# Create the dnsmasq user in dnsmasq-base, so that <PERSON><PERSON> doesn't complain.

if [ "$1" = "configure" ]; then
  # Create the user to run as.
  if [ -z "`id -u dnsmasq 2> /dev/null`" ]; then
    useradd --system \
            --gid nogroup \
            --comment dnsmasq \
            --home-dir /var/lib/misc --no-create-home \
            --shell /usr/sbin/nologin \
            dnsmasq
  fi

  # Make the directory where we keep the pid file - this
  # has to be owned by "dnsmasq" so that the file can be unlinked.
  # This is only actually used by the dnsmasq binary package, not
  # dnsmasq-base, but it's much easier to create it here so that
  # we don't have synchronisation issues with the creation of the
  # dnsmasq user.
  if [ ! -d /run/dnsmasq ]; then
    mkdir /run/dnsmasq
    chown dnsmasq:nogroup /run/dnsmasq
  fi
fi

#DEBHELPER#

