#!/bin/sh

. /usr/share/dnsmasq/init-system-common

case "$1" in
	checkconfig)
		checkconfig
		;;
	start-resolvconf)
		start_resolvconf
		;;
	stop-resolvconf)
		stop_resolvconf
		;;
	exec)
		# /run may be volatile, so we need to ensure that
		# /run/dnsmasq exists here as well as in postinst
		if [ ! -d /run/dnsmasq ]; then
			mkdir /run/dnsmasq || { [ -d /run/dnsmasq ] || exit 2 ; }
			chown dnsmasq:nogroup /run/dnsmasq || exit 2
		fi
		exec ${DAEMON} -x /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid \
			${MAILHOSTNAME:+ -m ${MAILHOSTNAME}} \
			${MAILTARGET:+ -t ${MAILTARGET}} \
			${DNSMASQ_USER:+ -u ${DNSMASQ_USER}} \
			${DNSMASQ_INTERFACES:+ ${DNSMASQ_INTERFACES}} \
			${DHCP_LEASE:+ -l ${DHCP_LEASE}} \
			${DOMAIN_SUFFIX:+ -s ${DOMAIN_SUFFIX}} \
			${RESOLV_CONF:+ -r ${RESOLV_CONF}} \
			${CACHESIZE:+ -c ${CACHESIZE}} \
			${CONFIG_DIR:+ -7 ${CONFIG_DIR}} \
			${DNSMASQ_OPTS:+ ${DNSMASQ_OPTS}}
		;;
esac
