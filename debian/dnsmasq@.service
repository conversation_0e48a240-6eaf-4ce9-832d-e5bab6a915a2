[Unit]
Description=dnsmasq (%i) - A lightweight DHCP and caching DNS server
Requires=network.target
Wants=nss-lookup.target
Before=nss-lookup.target
After=network.target

[Service]
Type=forking
PIDFile=/run/dnsmasq/dnsmasq.%i.pid

# Test the config file and refuse starting if it is not valid.
ExecStartPre=/usr/share/dnsmasq/systemd-helper checkconfig "%i"

# We run dnsmasq via the /usr/share/dnsmasq/systemd-helper script which acts
# as a wrapper picking up extra configuration files and then execs dnsmasq
# itself, when called with the "exec" function.
ExecStart=/usr/share/dnsmasq/systemd-helper exec "%i"

# The *-resolvconf functions configure (and deconfigure)
# resolvconf to work with the dnsmasq DNS server. They're called like
# this to get correct error handling (ie don't start-resolvconf if the
# dnsmasq daemon fails to start).
ExecStartPost=/usr/share/dnsmasq/systemd-helper start-resolvconf "%i"
ExecStop=/usr/share/dnsmasq/systemd-helper stop-resolvconf "%i"


ExecReload=/bin/kill -HUP $MAINPID

[Install]
WantedBy=multi-user.target
