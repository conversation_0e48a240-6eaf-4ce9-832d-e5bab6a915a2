#!/bin/sh
### BEGIN INIT INFO
# Provides:       dnsmasq
# Required-Start: $network $remote_fs $syslog
# Required-Stop:  $network $remote_fs $syslog
# Default-Start:  2 3 4 5
# Default-Stop:   0 1 6
# Description:    DHCP and DNS server
### END INIT INFO

# Don't exit on error status
set +e

# The following test ensures the dnsmasq service is not started, when the
# package 'dnsmasq' is removed but not purged, even if the dnsmasq-base
# package is still in place.
if [ -r /usr/share/dnsmasq/init-system-common ]; then
    # 'dnsmasq' is installed: source initial code used also with systemd.
    . /usr/share/dnsmasq/init-system-common
else
    # 'dnsmasq' is removed but not purged, or damaged: do nothing.
    exit 0
fi

# Double-check 'dnsmasq-base' or 'dnsmasq-base-lua' is installed.
test -x ${DAEMON} || exit 0

# Source the SysV init-functions which should always be available.
. /lib/lsb/init-functions || exit 0

start()
{
    # Return
    #   0 if daemon has been started
    #   1 if daemon was already running
    #   2 if daemon could not be started

    # /run may be volatile, so we need to ensure that
    # /run/dnsmasq exists here as well as in postinst
    if [ ! -d /run/dnsmasq ]; then
        mkdir /run/dnsmasq || { [ -d /run/dnsmasq ] || return 2 ; }
        chown dnsmasq:nogroup /run/dnsmasq || return 2
    fi
    [ -x /sbin/restorecon ] && /sbin/restorecon /run/dnsmasq

    start-stop-daemon --start --quiet --pidfile /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid --exec ${DAEMON} --test > /dev/null || return 1
    start-stop-daemon --start --quiet --pidfile /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid --exec ${DAEMON} -- \
        -x /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid \
        ${MAILHOSTNAME:+ -m ${MAILHOSTNAME}} \
        ${MAILTARGET:+ -t ${MAILTARGET}} \
        ${DNSMASQ_USER:+ -u ${DNSMASQ_USER}} \
        ${DNSMASQ_INTERFACES:+ ${DNSMASQ_INTERFACES}} \
        ${DHCP_LEASE:+ -l ${DHCP_LEASE}} \
        ${DOMAIN_SUFFIX:+ -s ${DOMAIN_SUFFIX}} \
        ${RESOLV_CONF:+ -r ${RESOLV_CONF}} \
        ${CACHESIZE:+ -c ${CACHESIZE}} \
        ${CONFIG_DIR:+ -7 ${CONFIG_DIR}} \
        ${DNSMASQ_OPTS:+ ${DNSMASQ_OPTS}} \
        || return 2
}

stop()
{
    # Return
    #   0 if daemon has been stopped
    #   1 if daemon was already stopped
    #   2 if daemon could not be stopped
    #   other if a failure occurred
    start-stop-daemon --stop --quiet --retry=TERM/30/KILL/5 --pidfile /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid --name ${NAME}
}

status()
{
    # Return
    #   0 if daemon is running
    #   1 if daemon is dead and pid file exists
    #   3 if daemon is not running
    #   4 if daemon status is unknown
    start-stop-daemon --start --quiet --pidfile /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid --exec ${DAEMON} --test > /dev/null
    case "${?}" in
      0) [ -e "/run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid" ] && return 1 ; return 3 ;;
      1) return 0 ;;
      *) return 4 ;;
    esac
}

case "${1}" in
  start)
    log_daemon_msg "Starting ${DESC}" "${NAME}${INSTANCE:+.${INSTANCE}}"
    start
    case "${?}" in
      0)
        log_end_msg 0
        start_resolvconf
        exit 0
        ;;
      1)
        log_success_msg "(already running)"
        exit 0
        ;;
      *)
        log_end_msg 1
        exit 1
        ;;
    esac
    ;;
  stop)
    stop_resolvconf
    log_daemon_msg "Stopping ${DESC}" "${NAME}${INSTANCE:+.${INSTANCE}}"
    stop
    RETVAL="${?}"
    case "${RETVAL}" in
      0) log_end_msg 0 ; exit 0 ;;
      1) log_warning_msg "(not running)" ; exit 0 ;;
      *) log_end_msg 1; exit 1 ;;
    esac
    ;;
  restart|force-reload)
    checkconfig
    if [ ${?} -ne 0 ]; then
        NAME="configuration syntax check"
        RETVAL="2"
    else
        stop_resolvconf
        stop
        RETVAL="${?}"
    fi
    log_daemon_msg "Restarting ${DESC}" "${NAME}${INSTANCE:+.${INSTANCE}}"
    case "${RETVAL}" in
      0|1)
        sleep 2
        start
        case "${?}" in
          0)
            log_end_msg 0
            start_resolvconf
            exit 0
            ;;
          *)
            log_end_msg 1
            exit 1
            ;;
        esac
        ;;
      *)
        log_end_msg 1
        exit 1
        ;;
    esac
    ;;
  status)
    log_daemon_msg "Checking ${DESC}" "${NAME}${INSTANCE:+.${INSTANCE}}"
    status
    case "${?}" in
      0) log_success_msg "(running)" ; exit 0 ;;
      1) log_success_msg "(dead, pid file exists)" ; exit 1 ;;
      3) log_success_msg "(not running)" ; exit 3 ;;
      *) log_success_msg "(unknown)" ; exit 4 ;;
    esac
    ;;
  dump-stats)
    kill -s USR1 `cat /run/dnsmasq/${NAME}${INSTANCE:+.${INSTANCE}}.pid`
    ;;
  *)
    echo "Usage: /etc/init.d/${NAME} {start|stop|restart|force-reload|dump-stats|status}" >&2
    exit 3
    ;;
esac

exit 0
