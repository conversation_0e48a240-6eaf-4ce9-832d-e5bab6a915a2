#!/bin/sh

in=`cat`

search()
{
    grep "^\#[[:space:]]*define[[:space:]]*$1" config.h >/dev/null 2>&1 || \
    echo $in | grep $1 >/dev/null 2>&1
}

while [ "$#" -gt 0 ]; do
    search=$1
    pkg=$2
    op=$3
    lib=$4
    shift 4
if search "$search"; then

# Nasty, nasty, in --copy, arg 2 (if non-empty) is another config to search for, used with NO_GMP
    if [ $op = "--copy" ]; then
	if [ -z "$pkg" ]; then
	    pkg="$lib"
	elif search "$pkg"; then
	    pkg=""
	else 
	    pkg="$lib"
	fi
    elif search "${search}_STATIC"; then
	pkg=`$pkg  --static $op $lib`
    else
	pkg=`$pkg $op $lib`
    fi
    
    if search "${search}_STATIC"; then
	if [ $op = "--libs" ] || [ $op = "--copy" ]; then
	    echo "-Wl,-Bstatic $pkg -Wl,-Bdynamic"
	else
	    echo "$pkg" 
	fi
    else
	echo "$pkg"
    fi
fi

done
