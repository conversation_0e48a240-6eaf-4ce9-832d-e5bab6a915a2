version 2.90
	Fix reversion in --rev-server introduced in 2.88 which
	caused breakage if the prefix length is not exactly divisible
	by 8 (IPv4) or 4 (IPv6).

	Fix possible SEGV when there server(s) for a particular
	domain are configured, but no server which is not qualified
	for a particular domain. Thanks to <PERSON> for
	spotting this bug.

	Set the default maximum DNS UDP packet sice to 1232. This
	has been the recommended value since 2020 because it's the
	largest value that avoid fragmentation, and fragmentation
	is just not reliable on the modern internet, especially
	for IPv6. It's still possible to override this with
	--edns-packet-max for special circumstances.

	Add --no-dhcpv4-interface and --no-dhcpv6-interface for
	better control over which inetrfaces are providing DHCP service.

	Fix issue with stale caching: After replying with stale data,
	dnsmasq sends the query upstream to refresh the cache asynchronously
	and sometimes sends the wrong packet: packet length can be wrong,
	and if an EDE marking stale data is added to the answer that can
	end up in the query also. This bug only seems to cause problems
	when the usptream server is a DOH/DOT proxy. Thanks to <PERSON>
	for the bug report.

	Add configurable caching for arbitrary RR-types.

	Add --filter-rr option, to filter arbitrary RR-types.
	--filter-rr=ANY has a special meaning: it filters the
	answers to queries for the ANY RR-type.
	
	Add limits on the resources used to do DNSSEC validation.
	DNSSEC introduces a potential CPU DoS, because a crafted domain
	can force a validator to a large number of cryptographic
	operations whilst attempting to do validation. When using TCP
	transport a DNSKEY RRset contain thousands of members and any
	RRset can have thousands of signatures. The potential number
	of signature validations to follow the RFC for validation
	for one RRset is the cross product of the keys and signatures,
	so millions. In practice, the actual numbers are much lower,
	so attacks can be mitigated by limiting the amount of
	cryptographic "work" to a much lower amount. The actual
	limits are number a signature validation fails per RRset(20),
	number of signature validations and hash computations
	per query(200), number of sub-queries  to fetch  DS and DNSKEY
	RRsets per query(40), and the number of iterations in a
	NSEC3 record(150). These values are sensible, but there is, as yet,
	no standardisation on the values for a "conforming" domain, so a
	new option --dnssec-limit is provided should they need to be altered.
	The algorithm to validate DS records has also been altered to reduce
	the maximum work from cross product of the number of DS records and
	number of DNSKEYs to the cross product of the number of DS records
	and supported DS digest types. As the number of DS digest types
	is in single figures, this reduces the exposure.

	Credit is due to Elias Heftrig, Haya Schulmann, Niklas Vogel,
	and Michael Waidner from the German National Research Center for
	Applied Cybersecurity ATHENE for finding this vulnerability.

	CVE 2023-50387 and CVE 2023-50868 apply.
	Note that the is a security vulnerablity only when DNSSEC validation
	is enabled.
	
	
version 2.89
        Fix bug introduced in 2.88 (commit fe91134b) which can result
	in corruption of the DNS cache internal data structures and
	logging of "cache internal error". This has only been seen
	in one place in the wild, and it took considerable effort
	to even generate a test case to reproduce it, but there's
	no way to be sure it won't strike, and the effect is to break
	the cache badly. Installations with DNSSEC enabled are more
	likely to see the problem, but not running DNSSEC does not
	guarantee that it won't happen. Thanks to Timo van Roermund
	for reporting the bug and for his great efforts in chasing
	it down.


version 2.88
	Fix bug in --dynamic-host when an interface has /16 IPv4
  	address. Thanks to Mark Dietzer for spotting this.

	Add --fast-dns-retry option. This gives dnsmasq the ability
	to originate retries for upstream DNS queries itself, rather
	than relying on the downstream client. This is most useful
	when doing DNSSEC over unreliable upstream networks. It comes
	with some cost in memory usage and network bandwidth.

	Add --use-stale-cache option. When set, if a DNS name exists
	in the cache, but its time-to-live has expired, dnsmasq will
	return the data anyway. (It attempts to refresh the
	data with an upstream query after returning the stale data.)
	This can improve speed and reliability. It comes
	at the expense of sometimes returning out-of-date data and
	less efficient cache utilisation, since old data cannot be
	flushed when its TTL expires, so the cache becomes
	strictly least-recently-used.

	Add --port-limit option which allows tuning for robustness in
	the face of some upstream network errors. Thanks to
	Prashant Kumar Singh, Ravi Nagayach and Mike Danilov,
	all of Amazon Web Services, for their efforts in developing this
	and the stale-cache and fast-retry options.

	Make --hostsdir (but NOT --dhcp-hostsdir and --dhcp-optsdir)
	handle removal of whole files or entries within files.
	Thanks to Dominik Derigs for the initial patches for this.

	Fix bug, introduced in 2.87, which could result in DNS
	servers being removed from the configuration when reloading
	server configuration from DBus, or re-reading /etc/resolv.conf
	Only servers from the same source should be replaced, but some
	servers from other sources (i.e., hard coded or another dynamic source)
	could mysteriously disappear. Thanks to all reporting this,
	but especially Christopher J. Madsen who reduced the problem
	to an easily reproducible case which saved much labour in
	finding it.

	Add --no-round-robin option.

	Allow domain names as well as IP addresses when specifying
	upstream DNS servers. There are some gotchas associated with this
	(it will mysteriously fail to work if the dnsmasq instance
	being started is in the path from the system resolver to the DNS),
	and a seemingly sensible configuration like
	--server=domain.name@******* is unactionable if domain.name
	only resolves to an IPv6 address). There are, however,
	cases where is can be useful. Thanks to Dominik Derigs for
	the patch.

	Handle DS records for unsupported crypto algorithms correctly.
	Such a DS, as long as it is validated, should allow answers
	in the domain it attests to be returned as unvalidated, and not
	as a validation error.

	Optimise reading large numbers of --server options. When re-reading
	upstream servers from /etc/resolv.conf or other sources that
	can change dnsmasq tries to avoid memory fragmentation by re-using
	existing records that are being re-read unchanged. This involves
	seaching all the server records for each new one installed.
	During startup this search is pointless, and can cause long
	start times with thousands of --server options because the work
	needed is O(n^2). Handle this case more intelligently.
	Thanks to Ye Zhou for spotting the problem and an initial patch.
	
	If we detect that a DNS reply from upstream is malformed don't
	return it to the requestor; send a SEVFAIL rcode instead.

	
version 2.87
        Allow arbitrary prefix lengths in --rev-server and
	--domain=....,local

	Replace --address=/#/..... functionality which got
	missed in the 2.86 domain search rewrite.

	Add --nftset option, like --ipset but for the newer nftables.
	Thanks to Chen Zhenge for the patch.
	
	Add --filter-A and --filter-AAAA options, to remove IPv4 or IPv6
	addresses from DNS answers.

	Fix crash doing netbooting when --port is set to zero
	to disable the DNS server. Thanks to Drexl Johannes
	for the bug report.

	Generalise --dhcp-relay. Sending via broadcast/multicast is
	now supported for both IPv4 and IPv6 and the configuration
	syntax made easier (but backwards compatible).
	
	Add snooping of IPv6 prefix-delegations to the DHCP-relay system.

	Finesse parsing of --dhcp-remoteid and --dhcp-subscrid. To be treated
	as hex, the pattern must consist of only hex digits AND contain
	at least one ':'. Thanks to Bengt-Erik Sandstrom who tripped
	over a pattern consisting of a decimal number which was interpreted
	surprisingly.

	Include client address in TFTP file-not-found error reports.
	Thanks to Stefan Rink for the initial patch, which has been
	re-worked by me (srk). All bugs mine.

	Note in manpage the change in behaviour of -address. This behaviour
	actually changed in v2.86, but was undocumented there. From 2.86 on,
	(eg) --address=/example.com/******* ONLY applies to A queries. All other
	types of query will be sent upstream. Pre 2.86, that would catch the
	whole example.com domain and queries for other types would get
	a local NODATA answer. The pre-2.86 behaviour is still available,
	by configuring --address=/example.com/******* --local=/example.com/

        Fix problem with binding DHCP sockets to an individual interface.
	Despite the fact that the system call tales the interface _name_ as
	a parameter, it actually, binds the socket to interface _index_.
	Deleting the interface and creating a new one with the same name
	leaves the socket bound to the old index. (Creating new sockets
	always allocates a fresh index, they are not reused). We now
	take this behaviour into account and keep up with changing indexes.

	Add --conf-script configuration option.

	Enhance --domain to accept, for instance,
	--domain=net2.thekelleys.org.uk,eth2 so that hosts get a domain
	which relects the interface they are attached to in a way which
	doesn't require hard-coding addresses. Thanks to Sten Spans for
	the idea.

	Fix write-after-free error in DHCPv6 server code.
	CVE-2022-0934 refers.
	
	Add the ability to specify destination port in
	DHCP-relay mode. This change also removes a previous bug
	where --dhcp-alternate-port would affect the port used
	to relay _to_ as well as the port being listened on.
	The new feature allows configuration to provide bug-for-bug
	compatibility, if required. Thanks to Damian Kaczkowski 
	for the feature suggestion.

	Bound the value of UDP packet size in the EDNS0 header of
	forwarded queries to the configured or default value of
	edns-packet-max. There's no point letting a client set a larger
	value if we're unable to return the answer. Thanks to Bertie
	Taylor for pointing out the problem and supplying the patch.
	
	Fix problem with the configuration
	
	--server=/some.domain/# --address=/#/<ip> --server=<server_ip>

	This would return <ip> for queries in some.domain, rather than
	forwarding the query via the default server.

	Tweak DHCPv6 relay code so that packets relayed towards a server
	have source address on the server-facing network, not the
	client facing network. Thanks to Luis Thomas for spotting this
	and initial patch.


version 2.86
	Handle DHCPREBIND requests in the DHCPv6 server code.
	Thanks to Aichun Li for spotting this omission, and the initial
	patch.

	Fix bug which caused dnsmasq to lose track of processes forked
	to handle TCP DNS connections under heavy load. The code
	checked that at least one free process table slot was
	available before listening on TCP sockets, but didn't take
	into account that more than one TCP connection could
	arrive, so that check was not sufficient to ensure that
	there would be slots for all new processes. It compounded
	this error by silently failing to store the process when
	it did run out of slots. Even when this bug is triggered,
	all the right things happen, and answers are still returned.
	Only under very exceptional circumstances, does the bug
	manifest itself: see
	https://lists.thekelleys.org.uk/pipermail/dnsmasq-discuss/2021q2/014976.html
	Thanks to Tijs Van Buggenhout for finding the conditions under
	which the bug manifests itself, and then working out
	exactly what was going on.

	Major rewrite of the DNS server and domain handling code.
	This should be largely transparent, but it drastically
	improves performance and reduces memory foot-print when
	configuring large numbers domains of the form
	local=/adserver.com/
	or
	local=/adserver.com/#
	Lookup times now grow as log-to-base-2 of the number of domains,
	rather than greater than linearly, as before.
	The change makes multiple addresses associated with a domain work
	address=/example.com/*******
	address=/example.com/*******
	It also handles multiple upstream servers for a domain better; using
	the same try/retry algorithms as non domain-specific servers. This
	also applies to DNSSEC-generated queries.
	Finally, some of the oldest and gnarliest code in dnsmasq has had
	a significant clean-up. It's far from perfect, but it _is_ better.

	Revise resource handling for number of concurrent DNS queries. This
	used to have a global limit, but that has a problem when using
	different servers for different upstream domains. Queries which are
	routed by domain to an upstream server which is not responding will
	build up and trigger the limit, which breaks DNS service for
	all other domains which could be handled by other servers. The
	change is to make the limit per server-group, where a server group
	is the set of servers configured for a particular domain. In the
	common case, where only default servers are declared, there is
	no effective change.

	Improve efficiency of DNSSEC. The sharing point for DNSSEC RR data
	used to be when it entered the cache, having been validated. After
	that queries requiring the KEY or DS records would share the cached
	values. There is a common case in dual-stack hosts that queries for
	A and AAAA records for the same domain are made simultaneously.
	If required keys were not in the cache, this would result in two
	requests being sent upstream for the same key data (and all the
	subsequent chain-of-trust queries.) Now we combine these requests
	and elide the duplicates, resulting in fewer queries upstream
	and better performance. To keep a better handle on what's
	going on, the "extra" logging mode has been modified to associate
	queries and answers  for DNSSEC queries in the same way as ordinary
	queries. The requesting address and port have been removed from
	DNSSEC logging lines, since this is no longer strictly defined.

	Connection track mark based DNS query filtering. Thanks to
	Etan Kissling for implementing this It extends query filtering
	support beyond what is currently possible
	with the `--ipset` configuration option, by adding support for:
	1) Specifying allowlists on a per-client basis, based on their
	   associated Linux connection track mark.
	2) Dynamic configuration of allowlists via Ubus.
	3) Reporting when a DNS query resolves or is rejected via Ubus.
	4) DNS name patterns containing wildcards.
	Disallowed queries are not forwarded; they are rejected
	with a REFUSED error code.

	Allow smaller than 64 prefix lengths in synth-domain, with caveats.
	--synth-domain=1234:4567::/56,example.com is now valid.

	Make domains generated by --synth-domain appear in replies
	when in authoritative mode.

	Ensure CAP_NET_ADMIN capability is available when
	conntrack is configured. Thanks to Yick Xie for spotting
	the lack of this.

	When --dhcp-hostsfile --dhcp-optsfile and --addn-hosts are
	given a directory as argument, define the order in which
	files within that directory are read (alphabetical order
	of filename). Thanks to Ed Wildgoose for the initial patch
	and motivation for this.

	Allow adding IP address to nftables set in addition to
	ipset.

	
version 2.85
        Fix problem with DNS retries in 2.83/2.84.
        The new logic in 2.83/2.84 which merges distinct requests
	for the same domain causes problems with clients which do
	retries as distinct requests (differing IDs and/or source ports.)
	The retries just get piggy-backed on the first, failed, request.
        The logic is now changed so that distinct requests for repeated
        queries still get merged into a single ID/source port, but
	they now always trigger a re-try upstream.
        Thanks to Nicholas Mu for his analysis.

	Tweak sort order of tags in get-version. v2.84 sorts
	before v2.83, but v2.83 sorts before v2.83rc1 and 2.83rc1
	sorts before v2.83test1. This fixes the problem which lead
	to 2.84 announcing itself as 2.84rc2.

 	Avoid treating a --dhcp-host which has an IPv6 address
	as eligible for use with DHCPv4 on the grounds that it has
	no address, and vice-versa. Thanks to Viktor Papp for
	spotting the problem. (This bug was fixed was back in 2.67, and
	then regressed in 2.81).

	Add --dynamic-host option: A and AAAA records which take their
	network part from the network of a local interface. Useful
	for routers with dynamically prefixes. Thanks
	to Fred F for the suggestion.

	Teach --bogus-nxdomain and --ignore-address to take an IPv4 subnet.

	Use random source ports where possible if source
	addresses/interfaces in use.
	CVE-2021-3448 applies. Thanks to Petr Menšík for spotting this.
	It's possible to specify the source address or interface to be
	used when contacting upstream name servers: server=*******@*******
	or server=*******@*******#66 or server=*******@eth0, and all of
	these have, until now, used a single socket, bound to a fixed
	port. This was originally done to allow an error (non-existent
	interface, or non-local address) to be detected at start-up. This
	means that any upstream servers specified in such a way don't use
	random source ports, and are more susceptible to cache-poisoning
	attacks.
	We now use random ports where possible, even when the
	source is specified, so server=*******@******* or
	server=*******@eth0 will use random source
	ports. server=*******@*******#66 or any use of --query-port will
	use the explicitly configured port, and should only be done with
	understanding of the security implications.
	Note that this change changes non-existing interface, or non-local
	source address errors from fatal to run-time. The error will be
	logged and communication with the server not possible.

	Change the method of allocation of random source ports for DNS.
	Previously, without min-port or max-port configured, dnsmasq would
	default to the compiled in defaults for those, which are 1024 and
	65535. Now, when neither are configured, it defaults instead to
	the kernel's ephemeral port range, which is typically
	32768 to 60999 on Linux systems. This change eliminates the
	possibility that dnsmasq may be using a registered port > 1024
	when a long-running daemon starts up and wishes to claim it.
	This change does likely slightly reduce the number of random ports
	and therefore the protection from reply spoofing. The older
	behaviour can be restored using the min-port and max-port config
	switches should that be a concern.

	Scale the size of the DNS random-port pool based on the
	value of the --dns-forward-max configuration.

	Tweak TFTP code to check sender of all received packets, as
	specified in RFC 1350 para 4.

	Support some wildcard matching of input tags to --tag-if.
	Thanks to Geoff Back for the idea and the patch.

	
version 2.84
	Fix a problem, introduced in 2.83, which could see DNS replies
	being sent via the wrong socket. On machines running both
	IPv4 and IPv6 this could result in sporadic messages of
	the form "failed to send packet: Network is unreachable" and
	the lost of the query. Since the error is sporadic and of
	low probability, the client retry would normally succeed.

	Change HAVE_NETTLEHASH compile-time to HAVE_CRYPTOHASH.


version 2.83
	Use the values of --min-port and --max-port in outgoing
	TCP connections to upstream DNS servers.

	Fix a remote buffer overflow problem in the DNSSEC code. Any
	dnsmasq with DNSSEC compiled in and enabled is vulnerable to this,
	referenced by CVE-2020-25681, CVE-2020-25682, CVE-2020-25683
	CVE-2020-25687.

	Be sure to only accept UDP DNS query replies at the address
	from which the query was originated. This keeps as much entropy
	in the {query-ID, random-port} tuple as possible, to help defeat
	cache poisoning attacks. Refer: CVE-2020-25684.

	Use the SHA-256 hash function to verify that DNS answers
	received are for the questions originally asked. This replaces
	the slightly insecure SHA-1 (when compiled with DNSSEC) or
	the very insecure CRC32 (otherwise). Refer: CVE-2020-25685.

	Handle multiple identical near simultaneous DNS queries better.
	Previously, such queries would all be forwarded
	independently. This is, in theory, inefficient but in practise
	not a problem, _except_ that is means that an answer for any
	of the forwarded queries will be accepted and cached.
	An attacker can send a query multiple times, and for each repeat,
	another {port, ID} becomes capable of accepting the answer he is
	sending in the blind, to random IDs and ports. The chance of a
	successful attack is therefore multiplied by the number of repeats
	of the query. The new behaviour detects repeated queries and
	merely stores the clients sending repeats so that when the
	first query completes, the answer can be sent to all the
	clients who asked. Refer: CVE-2020-25686.
	

version 2.82
	Improve behaviour in the face of network interfaces which come
	and go and change index. Thanks to Petr Mensik for the patch.

	Convert hard startup failure on NETLINK_NO_ENOBUFS under qemu-user
	to a warning.

	Allow IPv6 addresses ofthe form [::ffff:*******] in --dhcp-option.

	Fix crash under heavy TCP connection load introduced in 2.81.
	Thanks to Frank for good work chasing this down.

	Change default lease time for DHCPv6 to one day.

	Alter calculation of preferred and valid times in router
	advertisements, so that these do not have a floor applied
	of the lease time in the dhcp-range if this is not explicitly
	specified and is merely the default.
	Thanks to Martin-Éric Racine for suggestions on this.

	
version 2.81
	Improve cache behaviour for TCP connections. For ease of
	implementation, dnsmasq has always forked a new process to handle
	each incoming TCP connection. A side-effect of this is that
	any DNS queries answered from TCP connections are not cached:
	when TCP connections were rare, this was not a problem.
	With the coming of DNSSEC, it is now the case that some
	DNSSEC queries have answers which spill to TCP, and if,
	for instance, this applies to the keys for the root, then
	those never get cached, and performance is very bad.
	This fix passes cache entries back from the TCP child process to
	the main server process, and fixes the problem.

	Remove the NO_FORK compile-time option, and support for uclinux.
	In an era where everything has an MMU, this looks like
	an anachronism, and it adds to (Ok, multiplies!) the
	combinatorial explosion of compile-time options. Thanks to
	Kevin Darbyshire-Bryant for the patch.

	Fix line-counting when reading /etc/hosts and friends; for
	correct error messages. Thanks to Christian Rosentreter
	for reporting this.

	Fix bug in DNS non-terminal code, added in 2.80, which could
	sometimes cause a NODATA rather than an NXDOMAIN reply.
	Thanks to Norman Rasmussen, Sven Mueller and Maciej Żenczykowski
	for spotting and diagnosing the bug and providing patches.

	Support TCP-fastopen (RFC-7413) on both incoming and
	outgoing TCP connections, if supported and enabled in the OS.

	Improve kernel-capability manipulation code under Linux. Dnsmasq
	now fails early if a required capability is not available, and
	tries not to request capabilities not required by its
	configuration.

	Add --shared-network config. This enables allocation of addresses
	by the DHCP server in subnets where the server (or relay) does not
	have an interface on the network in that subnet. Many thanks to
	kamp.de for sponsoring this feature.
	
	Fix broken contrib/lease_tools/dhcp_lease_time.c. A packet
	validation check got borked in commit 2b38e382 and release 2.80.
	Thanks to Tomasz Szajner for spotting this.

	Fix compilation against nettle version 3.5 and later.

	Fix spurious DNSSEC validation failures when the auth section
	of a reply contains unsigned RRs from a signed zone, 
	with the exception that NSEC and NSEC3 RRs must always be signed.
        Thanks to Tore Anderson for spotting and diagnosing the bug.

	Add --dhcp-ignore-clid. This disables reading of DHCP client
	identifier option (option 61), so clients are only identified by
	MAC addresses.

	Fix a bug which stopped --dhcp-name-match from working when a hostname
	is supplied in --dhcp-host. Thanks to James Feeney for spotting this.

	Fix bug which caused very rarely caused zero-length DHCPv6 packets.
	Thanks to Dereck Higgins for spotting this.

	Add --tftp-single-port option.

	Enhance --conf-dir to load files in a deterministic order. Thanks to
	Evgenii Seliavka for the suggestion and initial patch.

	In the router advert code, handle case where we have two
	different interfaces on the same IPv6 net, and we are doing
	RA/DHCP service on only one of them. Thanks to NIIBE Yutaka
	for spotting this case and making the initial patch.

	Support prefixed ranges of ipv6 addresses in dhcp-host.
	This eases problems chain-netbooting, where each link in the
	chain requests an address using a different UID. With a single
	address, only one gets the "static" address, but with this
	fix, enough addresses can be reserved for all the stages of the
	boot. Many thanks to Harald Jensås for his work on this idea and
	earlier patches.

	Add filtering by tag of --dhcp-host directives. Based on a patch
	by Harald Jensås.

	Allow empty server spec in --rev-server, to match --server.
	
	Remove DSA signature verification from DNSSEC, as specified in
	RFC 8624. Thanks to Loganaden Velvindron for the original patch.

	Add --script-on-renewal option.

	
version 2.80
	Add support for RFC 4039 DHCP rapid commit. Thanks to Ashram Method
	for the initial patch and motivation.

	Alter the default for dnssec-check-unsigned. Versions of
	dnsmasq prior to 2.80 defaulted to not checking unsigned
	replies, and used --dnssec-check-unsigned to switch
        this on. Such configurations will continue to work as before,
        but those which used the default of no checking will need to be
        altered to explicitly select no checking. The new default is
        because switching off checking for unsigned replies is
	inherently dangerous. Not only does it open the possiblity of forged
        replies, but it allows everything to appear to be working even
        when the upstream namesevers do not support DNSSEC, and in this
        case no DNSSEC validation at all is occuring.

        Fix DHCP broken-ness when --no-ping AND --dhcp-sequential-ip
	are set. Thanks to Daniel Miess for help with this.

	Add a facilty to store DNS packets sent/recieved in a
	pcap-format file for later debugging. The file location
	is given by the --dumpfile option, and a bitmap controlling
	which packets should be dumped is given by the --dumpmask
	option.

	Handle the case of both standard and constructed dhcp-ranges on the
	same interface better. We don't now contruct a dhcp-range if there's
	already one specified. This allows the specified interface to
	have different parameters and avoids advertising the same
	prefix twice. Thanks to Luis Marsano for spotting this case.

	Allow zone transfer in authoritative mode if auth-peer is specified,
	even if auth-sec-servers is not. Thanks to Raphaël Halimi for
	the suggestion.

	Fix bug which sometimes caused dnsmasq to wrongly return answers
	without DNSSEC RRs to queries with the do-bit set, but only when
	DNSSEC validation was not enabled.
	Thanks to Petr Menšík for spotting this.

	Fix missing fatal errors with some malformed options
	(server, local, address, rebind-domain-ok, ipset, alias).
	Thanks to Eugene Lozovoy for spotting the problem.

	Fix crash on startup with a --synth-domain which has no prefix.
	Introduced in 2.79. Thanks to Andreas Engel for the bug report.

	Fix missing EDNS0 section in some replies generated by local
	DNS configuration which confused systemd-resolvd. Thanks to
	Steve Dodd for characterising the problem.

	Add --dhcp-name-match config option. 

	Add --caa-record config option.

	Implement --address=/example.com/# as (more efficient) syntactic
	sugar for --address=/example.com/0.0.0.0 and
	--address=/example.com/::
	Returning null addresses is a useful technique for ad-blocking.
	Thanks to Peter Russell for the suggestion.
	
	Change anti cache-snooping behaviour with queries with the
	recursion-desired bit unset. Instead to returning SERVFAIL, we
	now always forward, and never answer from the cache. This
	allows "dig +trace" command to work. 
	
	Include in the example config file a formulation which
	stops DHCP clients from claiming the DNS name "wpad".
	This is a fix for the CERT Vulnerability VU#598349.

	
version 2.79
	Fix parsing of CNAME arguments, which are confused by extra spaces.
	Thanks to Diego Aguirre for spotting the bug.

	Where available, use IP_UNICAST_IF or IPV6_UNICAST_IF to bind
	upstream servers to an interface, rather than SO_BINDTODEVICE.
	Thanks to Beniamino Galvani for the patch.

	Always return a SERVFAIL answer to DNS queries without the
	recursion desired bit set, UNLESS acting as an authoritative
	DNS server. This avoids a potential route to cache snooping.

	Add support for Ed25519 signatures in DNSSEC validation.

	No longer support RSA/MD5 signatures in DNSSEC validation,
	since these are not secure. This behaviour is mandated in
	RFC-6944.

	Fix incorrect error exit code from dhcp_release6 utility.
	Thanks Gaudenz Steinlin for the bug report.

	Use SIGINT (instead of overloading SIGHUP) to turn on DNSSEC
	time validation when --dnssec-no-timecheck is in use.
	Note that this is an incompatible change from earlier releases.

	Allow more than one --bridge-interface option to refer to an
	interface, so that we can use
	--bridge-interface=int1,alias1
	--bridge-interface=int1,alias2
	as an alternative to
	--bridge-interface=int1,alias1,alias2
	Thanks to Neil Jerram for work on this.

	Fix for DNSSEC with wildcard-derived NSEC records.
	It's OK for NSEC records to be expanded from wildcards,
	but in that case, the proof of non-existence is only valid
	starting at the wildcard name, *.<domain> NOT the name expanded
	from the wildcard. Without this check it's possible for an
	attacker to craft an NSEC which wrongly proves non-existence.
	Thanks to Ralph Dolmans for finding this, and co-ordinating 
	the vulnerability tracking and fix release.
	CVE-2017-15107 applies.

	Remove special handling of A-for-A DNS queries. These
	are no longer a significant problem in the global DNS.
	http://cs.northwestern.edu/~ychen/Papers/DNS_ToN15.pdf
	Thanks to Mattias Hellström for the initial patch.

	Fix failure to delete dynamically created dhcp options
	from files in -dhcp-optsdir directories. Thanks to
	Lindgren Fredrik for the bug report.

	Add to --synth-domain the ability to create names using
	sequential numbers, as well as encodings of IP addresses.
	For instance,
	--synth-domain=thekelleys.org.uk,************,************,internal-*
	creates 21 domain names of the form
	internal-4.thekelleys.org.uk over the address range given, with
	internal-0.thekelleys.org.uk being ************ and
	internal-20.thekelleys.org.uk being ************
	Thanks to Andy Hawkins for the suggestion.

	Tidy up Crypto code, removing workarounds for ancient
	versions of libnettle. We now require libnettle 3.


version 2.78
        Fix logic of appending ".<layer>" to PXE basename. Thanks to Chris
	Novakovic for the patch.

	Revert ping-check of address in DHCPDISCOVER if there
	already exists a lease for the address. Under some
	circumstances, and netbooted windows installation can reply
	to pings before if has a DHCP lease and block allocation
	of the address it already used during netboot. Thanks to
	Jan Psota for spotting this.

	Fix DHCP relaying, broken in 2.76 and 2.77 by commit
	ff325644c7afae2588583f935f4ea9b9694eb52e. Thanks to
	John Fitzgibbon for the diagnosis and patch.

        Try other servers if first returns REFUSED when
	--strict-order active. Thanks to Hans Dedecker
	for the patch

	Fix regression in 2.77, ironically added as a security
	improvement, which resulted in a crash when a DNS
	query exceeded 512 bytes (or the EDNS0 packet size,
	if different.) Thanks to Christian Kujau, Arne Woerner
	Juan Manuel Fernandez and Kevin Darbyshire-Bryant for
	chasing this one down.  CVE-2017-13704 applies.

	Fix heap overflow in DNS code. This is a potentially serious
	security hole. It allows an attacker who can make DNS
	requests to dnsmasq, and who controls the contents of
	a domain, which is thereby queried, to overflow
	(by 2 bytes) a heap buffer and either crash, or
	even take control of, dnsmasq.
	CVE-2017-14491 applies.
	Credit to Felix Wilhelm, Fermin J. Serna, Gabriel Campana
	Kevin Hamacher and Ron Bowes of the Google Security Team for
	finding this.

	Fix heap overflow in IPv6 router advertisement code.
	This is a potentially serious security hole, as a
	crafted RA request can overflow a buffer and crash or
	control dnsmasq. Attacker must be on the local network.
	CVE-2017-14492 applies.
        Credit to Felix Wilhelm, Fermin J. Serna, Gabriel Campana
	and Kevin Hamacher of the Google Security Team for
	finding this.

	Fix stack overflow in DHCPv6 code. An attacker who can send
	a DHCPv6 request to dnsmasq can overflow the stack frame and
	crash or control dnsmasq.
	CVE-2017-14493 applies.
	Credit to Felix Wilhelm, Fermin J. Serna, Gabriel Campana
	Kevin Hamacher and Ron Bowes of the Google Security Team for
	finding this.

	Fix information leak in DHCPv6. A crafted DHCPv6 packet can
	cause dnsmasq to forward memory from outside the packet
	buffer to a DHCPv6 server when acting as a relay.
	CVE-2017-14494 applies.
	Credit to Felix Wilhelm, Fermin J. Serna, Gabriel Campana
	Kevin Hamacher and Ron Bowes of the Google Security Team for
	finding this.

	Fix DoS in DNS. Invalid boundary checks in the
	add_pseudoheader function allows a memcpy call with negative
	size An attacker which can send malicious DNS queries
	to dnsmasq can trigger a DoS remotely.
	dnsmasq is vulnerable only if one of the following option is
	specified: --add-mac, --add-cpe-id or --add-subnet.
	CVE-2017-14496 applies.
	Credit to Felix Wilhelm, Fermin J. Serna, Gabriel Campana
	Kevin Hamacher and Ron Bowes of the Google Security Team for
	finding this.

	Fix out-of-memory Dos vulnerability. An attacker which can
	send malicious DNS queries to dnsmasq can trigger memory
	allocations in the add_pseudoheader function
	The allocated memory is never freed which leads to a DoS
	through memory exhaustion. dnsmasq is vulnerable only
	if one of the following option is specified:
	--add-mac, --add-cpe-id or --add-subnet.
	CVE-2017-14495 applies.
	Credit to Felix Wilhelm, Fermin J. Serna, Gabriel Campana
	Kevin Hamacher and Ron Bowes of the Google Security Team for
	finding this.


version 2.77
	Generate an error when configured with a CNAME loop,
	rather than a crash. Thanks to George Metz for
	spotting this problem.

	Calculate the length of TFTP error reply packet 
	correctly. This fixes a problem when the error 
	message in a TFTP packet exceeds the arbitrary 
	limit of 500 characters. The message was correctly
	truncated, but not the packet length, so 
	extra data was appended. This is a possible
	security risk, since the extra data comes from
	a buffer which is also used for DNS, so that
	previous DNS queries or replies may be leaked.
	Thanks to Mozilla for funding the security audit 
	which spotted this bug.

	Fix logic error in Linux netlink code. This could
	cause dnsmasq to enter a tight loop on systems
	with a very large number of network interfaces.
	Thanks to Ivan Kokshaysky for the diagnosis and
	patch.

	Fix problem with --dnssec-timestamp whereby receipt
	of SIGHUP would erroneously engage timestamp checking.
	Thanks to Kevin Darbyshire-Bryant for this work.

	Bump zone serial on reloading /etc/hosts and friends
	when providing authoritative DNS. Thanks to Harrald
	Dunkel for spotting this.

	Handle v4-mapped IPv6 addresses sanely in --synth-domain.
	These have standard representation like ::ffff:*******
	and are now converted to names like
	<prefix>--ffff-1-2-3-4.<domain>

	Handle binding upstream servers to an interface 
	(--server=*******@eth0) when the named interface
	is destroyed and recreated in the kernel. Thanks to 
	Beniamino Galvani for the patch.

	Allow wildcard CNAME records in authoritative zones.
	For example --cname=*.example.com,default.example.com
	Thanks to Pro Backup for sponsoring this development.

	Bump the allowed backlog of TCP connections from 5 to 32,
	and make this a compile-time configurable option. Thanks
	to Donatas Abraitis for diagnosing this as a potential
	problem.

	Add DNSMASQ_REQUESTED_OPTIONS environment variable to the 
	lease-change script. Thanks to ZHAO Yu for the patch.

	Fix foobar in rrfilter code, that could cause malformed 
	replies, especially when DNSSEC validation on, and 
	the upstream server returns answer with the RRs in a 
	particular order. The only DNS server known to tickle
	this is Nominum's. Thanks to Dave Täht for spotting the
	bug and assisting in the fix.

	Fix the manpage which lied that only the primary address
	of an interface is used by --interface-name.

	Make --localise-queries apply to names from --interface-name.
	Thanks to Kevin Darbyshire-Bryant and Eric Luehrsen
	for pushing this.

	Improve connection handling when talking to TCP upstream 
	servers. Specifically, be prepared to open a new TCP
	connection when we want to make multiple queries
	but the upstream server accepts fewer queries per connection.

	Improve logging of upstream servers when there are a lot
	of "local addresses only" entries. Thanks to Hannu Nyman for
	the patch.

	Make --bogus-priv apply to IPv6, for the prefixes specified
	in RFC6303. Thanks to Kevin Darbyshire-Bryant for work on this.

	Allow use of MAC addresses with --tftp-unique-root. Thanks
	to Floris Bos for the patch.

	Add --dhcp-reply-delay option. Thanks to Floris Bos
	for the patch.

	Add mtu setting facility to --ra-param. Thanks to David
	Flamand for the patch.

	Capture STDOUT and STDERR output from dhcp-script and log
	it as part of the dnsmasq log stream. Makes life easier
	for diagnosing unexpected problems in scripts.
	Thanks to Petr Mensik for the patch.

	Generate fatal errors when failing to parse the output
	of the dhcp-script in "init" mode. Avoids strange errors
	when the script accidentally emits error messages.
	Thanks to Petr Mensik for the patch.

	Make --rev-server for an RFC1918 subnet work even in the
	presence of the --bogus-priv flag. Thanks to
	Vladislav Grishenko for the patch.

	Extend --ra-param mtu: field to allow an interface name.
	This allows the MTU of a WAN interface to be advertised on
	the internal interfaces of a router. Thanks to
	Vladislav Grishenko for the patch.

	Do ICMP-ping check for address-in-use for DHCPv4 when
	the client specifies an address in DHCPDISCOVER, and when
	an address in configured locally. Thanks to Alin Năstac
	for spotting the problem.

	Add new DHCP tag "known-othernet" which is set when only a
	dhcp-host exists for another subnet. Can be used to ensure
	that privileged hosts are not given "guest" addresses by
	accident. Thanks to Todd Sanket for the suggestion.

	Remove historic automatic inclusion of IDN support when
	building internationalisation support. This doesn't
	fit now there is a choice of IDN libraries. Be sure
	to include either -DHAVE_IDN or -DHAVE_LIBIDN2 for
	IDN support.


version 2.76
	Include 0.0.0.0/8 in DNS rebind checks. This range 
	translates to hosts on  the local network, or, at 
	least, 0.0.0.0 accesses the local host, so could
	be targets for DNS rebinding. See RFC 5735 section 3 
	for details. Thanks to Stephen Röttger for the bug report.

	Enhance --add-subnet to allow arbitrary subnet addresses.
	Thanks to Ed Barsley for the patch.

	Respect the --no-resolv flag in inotify code. Fixes bug
	which caused dnsmasq to fail to start if a resolv-file 
	was a dangling symbolic link, even of --no-resolv set.
	Thanks to Alexander Kurtz for spotting the problem.

	Fix crash when an A or AAAA record is defined locally,
	in a hosts file, and an upstream server sends a reply
	that the same name is empty. Thanks to Edwin Török for
	the patch.

	Fix failure to correctly calculate cache-size when 
	reading a hosts-file fails. Thanks to André Glüpker 
	for the patch.

	Fix wrong answer to simple name query when --domain-needed
	set, but no upstream servers configured. Dnsmasq returned
	REFUSED, in this case, when it should be the same as when
	upstream servers are configured - NOERROR. Thanks to 
	Allain Legacy for spotting the problem.

	Return REFUSED when running out of forwarding table slots,
	not SERVFAIL.

	Add --max-port configuration. Thanks to Hans Dedecker for
	the patch.

	Add --script-arp and two new functions for the dhcp-script.
	These are "arp" and "arp-old" which announce the arrival and
	removal of entries in the ARP or neighbour tables.

	Extend --add-mac to allow a new encoding of the MAC address 
	as base64, by configuring --add-mac=base64

	Add --add-cpe-id option.

	Don't crash with divide-by-zero if an IPv6 dhcp-range
	is declared as a whole /64.
	(ie xx::0 to xx::ffff:ffff:ffff:ffff) 
	Thanks to Laurent Bendel for spotting this problem.

	Add support for a TTL parameter in --host-record and
	--cname.

	Add --dhcp-ttl option.

	Add --tftp-mtu option. Thanks to Patrick McLean for the 
	initial patch.

	Check return-code of inet_pton() when parsing dhcp-option.
	Bad addresses could fail to generate errors and result in
	garbage dhcp-options being sent. Thanks to Marc Branchaud 
	for spotting this.

	Fix wrong value for EDNS UDP packet size when using 
	--servers-file to define upstream DNS servers. Thanks to
	Scott Bonar for the bug report.

	Move the dhcp_release and dhcp_lease_time tools from 
	contrib/wrt to contrib/lease-tools.

	Add dhcp_release6 to contrib/lease-tools. Many thanks 
	to Sergey Nechaev for this code.

	To avoid filling logs in configurations which define
	many upstream nameservers, don't log more that 30 servers.
	The number to be logged can be changed as SERVERS_LOGGED
	in src/config.h.

	Swap the values if BC_EFI and x86-64_EFI in --pxe-service. 
	These were previously wrong due to an error in RFC 4578.
	If you're using BC_EFI to boot 64-bit EFI machines, you
	will need to update your config.

	Add ARM32_EFI and ARM64_EFI as valid architectures in
	--pxe-service.

	Fix PXE booting for UEFI architectures. Modify PXE boot
	sequence in this case to force the client to talk to dnsmasq
	over port 4011. This makes PXE and especially proxy-DHCP PXE
	work with these architectures.

	Workaround problems with UEFI PXE clients. There exist
	in the wild PXE clients which have problems with PXE
	boot menus. To work around this, when there's a single
	--pxe-service which applies to client, then that target
	will be booted directly, rather then sending a
	single-item boot menu.

	Many thanks to Jarek Polok, Michael Kuron and Dreamcat4 
	for their work on the long-standing UEFI PXE problem.

	Subtle change in the semantics of "basename" in
	--pxe-service. The historical behaviour has always been
	that the actual filename downloaded from the TFTP server
	is <basename>.<layer> where <layer> is an integer which
	corresponds to the layer parameter supplied by the client.
	It's not clear what the function of the "layer" 
	actually is in the PXE protocol, and in practise layer 
	is always zero, so the filename is <basename>.0
	The new behaviour is the same as the old, except when
	<basename> includes a file suffix, in which case
	the layer suffix is no longer added. This allows
	sensible suffices to be used, rather then the
	meaningless ".0". Only in the unlikely event that you
	have a config with a basename which already has a
	suffix, is this an incompatible change, since the file
	downloaded will change from name.suffix.0 to just 
	name.suffix


version 2.75
	Fix reversion on 2.74 which caused 100% CPU use when a 
	dhcp-script is configured. Thanks to Adrian Davey for
	reporting the bug and testing the fix.


version 2.74
	Fix reversion in 2.73 where --conf-file would attempt to
	read the default file, rather than no file.

	Fix inotify code to handle dangling symlinks better and
	not SEGV in some circumstances.

	DNSSEC fix. In the case of a signed CNAME generated by a
	wildcard which pointed to an unsigned domain, the wrong
	status would be logged, and some necessary checks omitted.


version 2.73
	Fix crash at startup when an empty suffix is supplied to
	--conf-dir, also trivial memory leak. Thanks to 
	Tomas Hozza for spotting this.

	Remove floor of 4096 on advertised EDNS0 packet size when 
	DNSSEC in use, the original rationale for this has long gone.
	Thanks to Anders Kaseorg for spotting this.

	Use inotify for checking on updates to /etc/resolv.conf and
	friends under Linux. This fixes race conditions when the files are 
	updated rapidly and saves CPU by noy polling. To build
	a binary that runs on old Linux kernels without inotify,
	use make COPTS=-DNO_INOTIFY

	Fix breakage of --domain=<domain>,<subnet>,local - only reverse
	queries were intercepted. THis appears to have been broken 
	since 2.69. Thanks to Josh Stone for finding the bug.

	Eliminate IPv6 privacy addresses and deprecated addresses from
	the answers given by --interface-name. Note that reverse queries
	(ie looking for names, given addresses) are not affected. 
	Thanks to Michael Gorbach for the suggestion.

	Fix crash in DNSSEC code with long RRs. Thanks to Marco Davids
	for the bug report.

	Add --ignore-address option. Ignore replies to A-record 
	queries which include the specified address. No error is
	generated, dnsmasq simply continues to listen for another 
	reply. This is useful to defeat blocking strategies which
	rely on quickly supplying a forged answer to a DNS 
	request for certain domains, before the correct answer can
	arrive. Thanks to Glen Huang for the patch.

	Revisit the part of DNSSEC validation which determines if an 
	unsigned answer is legit, or is in some part of the DNS 
	tree which should be signed. Dnsmasq now works from the 
	DNS root downward looking for the limit of signed 
	delegations, rather than working bottom up. This is 
	both more correct, and less likely to trip over broken 
	nameservers in the unsigned parts of the DNS tree 
	which don't respond well to DNSSEC queries.

	Add --log-queries=extra option, which makes logs easier
	to search automatically.

	Add --min-cache-ttl option. I've resisted this for a long 
	time, on the grounds that disbelieving TTLs is never a 
	good idea, but I've been persuaded that there are 
	sometimes reasons to do it. (Step forward, GFW).
	To avoid misuse, there's a hard limit on the TTL 
	floor of one hour. Thanks to RinSatsuki for the patch.

	Cope with multiple interfaces with the same link-local 
	address. (IPv6 addresses are scoped, so this is allowed.)
	Thanks to Cory Benfield for help with this.

	Add --dhcp-hostsdir. This allows addition of new host
	configurations to a running dnsmasq instance much more 
	cheaply than having dnsmasq re-read all its existing
	configuration each time. 

	Don't reply to DHCPv6 SOLICIT messages if we're not 
	configured to do stateful DHCPv6. Thanks to Win King Wan 
	for the patch.

	Fix broken DNSSEC validation of ECDSA signatures.

	Add --dnssec-timestamp option, which provides an automatic
	way to detect when the system time becomes valid after 
	boot on systems without an RTC, whilst allowing DNS 
	queries before the clock is valid so that NTP can run. 
	Thanks to Kevin Darbyshire-Bryant for developing this idea.

	Add --tftp-no-fail option. Thanks to Stefan Tomanek for
	the patch.

	Fix crash caused by looking up servers.bind, CHAOS text 
	record, when more than about five --servers= lines are 
	in the dnsmasq config. This causes memory corruption 
	which causes a crash later. Thanks to Matt Coddington for 
	sterling work chasing this down.

	Fix crash on receipt of certain malformed DNS requests.
	Thanks to Nick Sampanis for spotting the problem.
	Note that this is could allow the dnsmasq process's
	memory to be read by an attacker under certain
	circumstances, so it has a CVE, CVE-2015-3294 

	Fix crash in authoritative DNS code, if a .arpa zone 
	is declared as authoritative, and then a PTR query which
	is not to be treated as authoritative arrived. Normally, 
	directly declaring .arpa zone as authoritative is not 
	done, so this crash wouldn't be seen. Instead the 
	relevant .arpa zone should be specified as a subnet
	in the auth-zone declaration. Thanks to Johnny S. Lee
	for the bugreport and initial patch.

	Fix authoritative DNS code to correctly reply to NS 
	and SOA queries for .arpa zones for which we are 
	declared authoritative by means of a subnet in auth-zone.
	Previously we provided correct answers to PTR queries
	in such zones (including NS and SOA) but not direct
	NS and SOA queries. Thanks to Johnny S. Lee for 
	pointing out the problem.

	Fix logging of DHCPREPLY which should be suppressed 
	by quiet-dhcp6. Thanks to J. Pablo Abonia for 
	spotting the problem.

	Try and handle net connections with broken fragmentation 
	that lose large UDP packets. If a server times out, 
	reduce the maximum UDP packet size field in the EDNS0
	header to 1280 bytes. If it then answers, make that
	change permanent.

	Check IPv4-mapped IPv6 addresses when --stop-rebind
	is active. Thanks to Jordan Milne for spotting this.

	Allow DHCPv4 options T1 and T2 to be set using --dhcp-option.
	Thanks to Kevin Benton for patches and work on this.

	Fix code for DHCPCONFIRM DHCPv6 messages to confirm addresses
	in the correct subnet, even of not in dynamic address 
	allocation range. Thanks to Steve Hirsch for spotting
	the problem.

	Add AddDhcpLease and DeleteDhcpLease DBus methods. Thanks
	to Nicolas Cavallari for the patch.

	Allow configuration of router advertisements without the 
	"on-link" bit set. Thanks to Neil Jerram for the patch.

	Extend --bridge-interface to DHCPv6 and router 
	advertisements. Thanks to Neil Jerram for the patch.


version 2.72
	Add ra-advrouter mode, for RFC-3775 mobile IPv6 support.

	Add support for "ipsets" in *BSD, using pf. Thanks to 
	Sven Falempin for the patch.

	Fix race condition which could lock up dnsmasq when an 
	interface goes down and up rapidly. Thanks to Conrad 
	Kostecki for helping to chase this down.

	Add DBus methods SetFilterWin2KOption and SetBogusPrivOption
	Thanks to the Smoothwall project for the patch.

	Fix failure to build against Nettle-3.0. Thanks to Steven 
	Barth for spotting this and finding the fix. 

	When assigning existing DHCP leases to interfaces by comparing 
	networks, handle the case that two or more interfaces have the
	same network part, but different prefix lengths (favour the
	longer prefix length.) Thanks to Lung-Pin Chang for the 
	patch.

	Add a mode which detects and removes DNS forwarding loops, ie 
	a query sent to an upstream server returns as a new query to 
	dnsmasq, and would therefore be forwarded again, resulting in 
	a query which loops many times before being dropped. Upstream
	servers which loop back are disabled and this event is logged.
	Thanks to Smoothwall for their sponsorship of this feature.

	Extend --conf-dir to allow filtering of files. So
	--conf-dir=/etc/dnsmasq.d,\*.conf
	will load all the files in /etc/dnsmasq.d which end in .conf

	Fix bug when resulted in NXDOMAIN answers instead of NODATA in
	some circumstances.

	Fix bug which caused dnsmasq to become unresponsive if it 
	failed to send packets due to a network interface disappearing.
	Thanks to Niels Peen for spotting this.

	Fix problem with --local-service option on big-endian platforms
	Thanks to Richard Genoud for the patch.


version 2.71
	Subtle change to error handling to help DNSSEC validation 
	when servers fail to provide NODATA answers for 
	non-existent DS records.

	Tweak code which removes DNSSEC records from answers when
	not required. Fixes broken answers when additional section
	has real records in it. Thanks to Marco Davids for the bug 
	report.

	Fix DNSSEC validation of ANY queries. Thanks to Marco Davids
	for spotting that too.

	Fix total DNS failure and 100% CPU use if cachesize set to zero,
	regression introduced in 2.69. Thanks to James Hunt and
	the Ubuntu crowd for assistance in fixing this.


version 2.70
	Fix crash, introduced in 2.69, on TCP request when dnsmasq
	compiled with DNSSEC support, but running without DNSSEC
	enabled. Thanks to Manish Sing for spotting that one.

	Fix regression which broke ipset functionality. Thanks to 
	Wang Jian for the bug report.


version 2.69
	Implement dynamic interface discovery on *BSD. This allows
	the constructor: syntax to be used in dhcp-range for DHCPv6
	on the BSD platform. Thanks to Matthias Andree for
	valuable research on how to implement this.

	Fix infinite loop associated with some --bogus-nxdomain
	configs. Thanks fogobogo for the bug report.

	Fix missing RA RDNS option with configuration like
	--dhcp-option=option6:23,[::] Thanks to Tsachi Kimeldorfer
	for spotting the problem.

	Add [fd00::] and [fe80::] as special addresses in DHCPv6
	options, analogous to [::]. [fd00::] is replaced with the
	actual ULA of the interface on the machine running
	dnsmasq, [fe80::] with the link-local address. 
	Thanks to Tsachi Kimeldorfer for championing this.

	DNSSEC validation and caching. Dnsmasq needs to be
	compiled with this enabled, with 

	make dnsmasq COPTS=-DHAVE_DNSSEC

	this adds dependencies on the nettle crypto library and the 
	gmp maths library. It's possible to have these linked
	statically with

	make dnsmasq COPTS='-DHAVE_DNSSEC -DHAVE_DNSSEC_STATIC'

	which bloats the dnsmasq binary, but saves the size of 
	the shared libraries which are much bigger.

	To enable, DNSSEC, you will need a set of
	trust-anchors. Now that the TLDs are signed, this can be
	the keys for the root zone, and for convenience they are
	included in trust-anchors.conf in the dnsmasq
	distribution. You should of course check that these are
	legitimate and up-to-date. So, adding

	conf-file=/path/to/trust-anchors.conf
	dnssec

	to your config is all that's needed to get things
	working. The upstream nameservers have to be DNSSEC-capable
	too, of course. Many ISP nameservers aren't, but the
	Google public nameservers (******* and 8.8.4.4) are.
	When DNSSEC is configured, dnsmasq validates any queries 
	for domains which are signed. Query results which are 
	bogus are replaced with SERVFAIL replies, and results 
	which are correctly signed have the AD bit set. In 
	addition, and just as importantly, dnsmasq supplies 
	correct DNSSEC information to clients which are doing 
	their own validation, and caches DNSKEY, DS and RRSIG
	records, which significantly improve the performance of 
	downstream validators. Setting --log-queries will show 
	DNSSEC in action.

	If a domain is returned from an upstream nameserver without 
	DNSSEC signature, dnsmasq by default trusts this. This 
	means that for unsigned zone (still the majority) there 
	is effectively no cost for having DNSSEC enabled. Of course
	this allows an attacker to replace a signed record with a 
	false unsigned record. This is addressed by the 
	--dnssec-check-unsigned flag, which instructs dnsmasq
	to prove that an unsigned record is legitimate, by finding  
	a secure proof that the zone containing the record is not
	signed. Doing this has costs (typically one or two extra
	upstream queries). It also has a nasty failure mode if
	dnsmasq's upstream nameservers are not DNSSEC capable. 
	Without --dnssec-check-unsigned using such an upstream
	server will simply result in not queries being validated; 
	with --dnssec-check-unsigned enabled and a 
	DNSSEC-ignorant upstream server, _all_ queries will fail.

	Note that DNSSEC requires that the local time is valid and 
	accurate, if not then DNSSEC validation will fail. NTP 
	should be running. This presents a problem for routers
	without a battery-backed clock. To set the time needs NTP 
	to do DNS lookups, but lookups will fail until NTP has run.
	To address this, there's a flag, --dnssec-no-timecheck 
	which disables the time checks (only) in DNSSEC. When dnsmasq
	is started and the clock is not synced, this flag should
	be used. As soon as the clock is synced, SIGHUP dnsmasq. 
	The SIGHUP clears the cache of partially-validated data and
	resets the no-timecheck flag, so that all DNSSEC checks 
	henceforward will be complete.

	The development of DNSSEC in dnsmasq was started by 
	Giovanni Bajo, to whom huge thanks are owed. It has been
	supported by Comcast, whose techfund grant has allowed for 
	an invaluable period of full-time work to get it to 
	a workable state.

	Add --rev-server. Thanks to Dave Taht for suggesting this.

	Add --servers-file. Allows dynamic update of upstream servers 
	full access to configuration. 

	Add --local-service. Accept DNS queries only from hosts 
	whose address is on a local subnet, ie a subnet for which 
	an interface exists on the server. This option
	only has effect if there are no --interface --except-interface,
	--listen-address or --auth-server options. It is intended 
	to be set as a default on installation, to allow
	unconfigured installations to be useful but also safe from 
	being used for DNS amplification attacks.

	Fix crashes in cache_get_cname_target() when dangling CNAMEs
	encountered. Thanks to Andy and the rt-n56u project for
	find this and helping to chase it down.

	Fix wrong RCODE in authoritative DNS replies to PTR queries. The
	correct answer was included, but the RCODE was set to NXDOMAIN.
	Thanks to Craig McQueen for spotting this.

	Make statistics available as DNS queries in the .bind TLD as 
	well as logging them.


version 2.68
	Use random addresses for DHCPv6 temporary address
	allocations, instead of algorithmically determined stable
	addresses.

	Fix bug which meant that the DHCPv6 DUID was not available
	in DHCP script runs during the lifetime of the dnsmasq
	process which created the DUID de-novo. Once the DUID was
	created and stored in the lease file and dnsmasq
	restarted, this bug disappeared.

	Fix bug introduced in 2.67 which could result in erroneous
	NXDOMAIN returns to CNAME queries.

	Fix build failures on MacOS X and openBSD.

	Allow subnet specifications in --auth-zone to be interface 
	names as well as address literals. This makes it possible
	to configure authoritative DNS when local address ranges
	are dynamic and works much better than the previous
	work-around which exempted constructed DHCP ranges from the
	IP address filtering. As a consequence, that work-around
	is removed. Under certain circumstances, this change wil
	break existing configuration: if you're relying on the
	constructed-range exception, you need to change --auth-zone
	to specify the same interface as is used to construct your
	DHCP ranges, probably with a trailing "/6" like this: 
	--auth-zone=example.com,eth0/6 to limit the addresses to
	IPv6 addresses of eth0.

	Fix problems when advertising deleted IPv6 prefixes. If
	the prefix is deleted (rather than replaced), it doesn't
	get advertised with zero preferred time. Thanks to Tsachi
	for the bug report. 

	Fix segfault with some locally configured CNAMEs. Thanks
	to Andrew Childs for spotting the problem.

	Fix memory leak on re-reading /etc/hosts and friends,
	introduced in 2.67.

	Check the arrival interface of incoming DNS and TFTP
	requests via IPv6, even in --bind-interfaces mode. This
	isn't possible for IPv4 and can generate scary warnings,
	but as it's always possible for IPv6 (the API always
	exists) then we should do it always. 

	Tweak the rules on prefix-lengths in --dhcp-range for
	IPv6. The new rule is that the specified prefix length
	must be larger than or equal to the prefix length of the
	corresponding address on the local interface. 


version 2.67
	Fix crash if upstream server returns SERVFAIL when
	--conntrack in use. Thanks to Giacomo Tazzari for finding
	this and supplying the patch. 

	Repair regression in 2.64. That release stopped sending
	lease-time information in the reply to DHCPINFORM
	requests, on the correct grounds that it was a standards
	violation. However, this broke the dnsmasq-specific
	dhcp_lease_time utility. Now, DHCPINFORM returns
	lease-time only if it's specifically requested
	(maintaining standards) and the dhcp_lease_time utility
	has been taught to ask for it (restoring functionality). 

	Fix --dhcp-match, --dhcp-vendorclass and --dhcp-userclass
	to work with BOOTP and well as DHCP. Thanks to Peter
	Korsgaard for spotting the problem. 

	Add --synth-domain. Thanks to Vishvananda Ishaya for
	suggesting this.

	Fix failure to compile ipset.c if old kernel headers are
	in use. Thanks to Eugene Rudoy for pointing this out.

	Handle IPv4 interface-address labels in Linux. These are
	often used to emulate the old IP-alias addresses. Before,
	using --interface=eth0 would service all the addresses of
	eth0, including ones configured as aliases, which appear
	in ifconfig as eth0:0. Now, only addresses with the label
	eth0 are active. This is not backwards compatible: if you
	want to continue to bind the aliases too, you need to add
	eg. --interface=eth0:0 to the config. 

	Fix "failed to set SO_BINDTODEVICE on DHCP socket: Socket 
	operation on non-socket" error on startup with
	configurations which have exactly one --interface option
	and do RA but _not_ DHCPv6. Thanks to Trever Adams for the
	bug report.

	Generalise --interface-name to cope with IPv6 addresses
	and multiple addresses per interface per address family.

	Fix option parsing for --dhcp-host, which was generating a
	spurious error when all seven possible items were
	included. Thanks to Zhiqiang Wang for the bug report.

	Remove restriction on prefix-length in --auth-zone. Thanks
	to Toke Hoiland-Jorgensen for suggesting this.

	Log when the maximum number of concurrent DNS queries is
	reached. Thanks to Marcelo Salhab Brogliato for the patch.

	If wildcards are used in --interface, don't assume that 
	there will only ever be one available interface for DHCP
	just because there is one at start-up. More may appear, so
	we can't use SO_BINDTODEVICE. Thanks to Natrio for the bug
	report. 

	Increase timeout/number of retries in TFTP to accommodate
	AudioCodes Voice Gateways doing streaming writes to flash.
	Thanks to Damian Kaczkowski for spotting the problem.

	Fix crash with empty DHCP string options when adding zero
	terminator. Thanks to Patrick McLean for the bug report.

	Allow hostnames to start with a number, as allowed in
	RFC-1123. Thanks to Kyle Mestery for the patch. 

	Fixes to DHCP FQDN option handling: don't terminate FQDN
	if domain not known and allow a FQDN option with blank
	name to request that a FQDN option is returned in the
	reply. Thanks to Roy Marples for the patch.

	Make --clear-on-reload apply to setting upstream servers
	via DBus too.

	When the address which triggered the construction of an
	advertised IPv6 prefix disappears, continue to advertise 
	the prefix for up to 2 hours, with the preferred lifetime
	set to zero. This satisfies RFC 6204 4.3 L-13 and makes
	things work better if a prefix disappears without being
	deprecated first. Thanks to Uwe Schindler for persuasively
	arguing for this.

	Fix MAC address enumeration on *BSD. Thanks to Brad Smith
	for the bug report.

	Support RFC-4242 information-refresh-time options in the 
	reply to DHCPv6 information-request. The lease time of the
	smallest valid dhcp-range is sent. Thanks to Uwe Schindler 
	for suggesting this.

	Make --listen-address higher priority than --except-interface
	in all circumstances. Thanks to Thomas Hood for the bugreport.

	Provide independent control over which interfaces get TFTP 
	service. If enable-tftp is given a list of interfaces, then TFTP 
	is provided on those. Without the list, the previous behaviour
	(provide TFTP to the same interfaces we provide DHCP to) 
	is retained. Thanks to Lonnie Abelbeck for the suggestion.

	Add --dhcp-relay config option. Many thanks to vtsl.net
	for sponsoring this development.

	Fix crash with empty tag: in --dhcp-range. Thanks to
	Kaspar Schleiser for the bug report.

	Add "baseline" and "bloatcheck" makefile targets, for 
	revealing size changes during development. Thanks to
	Vladislav Grishenko for the patch. 

	Cope with DHCPv6 clients which send REQUESTs without
	address options - treat them as SOLICIT with rapid commit.

	Support identification of clients by MAC address in
	DHCPv6. When using a relay, the relay must support RFC
	6939 for this to work. It always works for directly
	connected clients. Thanks to Vladislav Grishenko
	for prompting this feature.

	Remove the rule for constructed DHCP ranges that the local
	address must be either the first or last address in the
	range. This was originally to avoid SLAAC addresses, but
	we now explicitly autoconfig and privacy addresses instead.  

	Update Polish translation. Thanks to Jan Psota.

	Fix problem in DHCPv6 vendorclass/userclass matching
	code. Thanks to Tanguy Bouzeloc for the patch.

	Update Spanish translation. Thanks to Vicente Soriano.

	Add --ra-param option. Thanks to Vladislav Grishenko for
	inspiration on this.

	Add --add-subnet configuration, to tell upstream DNS
	servers where the original client is. Thanks to DNSthingy
	for sponsoring this feature.

	Add --quiet-dhcp, --quiet-dhcp6 and --quiet-ra. Thanks to
	Kevin Darbyshire-Bryant for the initial patch.

	Allow A/AAAA records created by --interface-name to be the
	target of --cname. Thanks to Hadmut Danisch for the
	suggestion. 

	Avoid treating a --dhcp-host which has an IPv6 address
	as eligible for use with DHCPv4 on the grounds that it has
	no address, and vice-versa. Thanks to Yury Konovalov for
	spotting the problem.

	Do a better job caching dangling CNAMEs. Thanks to Yves
	Dorfsman for spotting the problem.


version 2.66
	Add the ability to act as an authoritative DNS
	server. Dnsmasq can now answer queries from the wider 'net
	with local data, as long as the correct NS records are set
	up. Only local data is provided, to avoid creating an open
	DNS relay. Zone transfer is supported, to allow secondary
	servers to be configured.

	Add "constructed DHCP ranges" for DHCPv6. This is intended
	for IPv6 routers which get prefixes dynamically via prefix
	delegation. With suitable configuration, stateful DHCPv6
	and RA can happen automatically as prefixes are delegated
	and then deprecated, without having  to re-write the
	dnsmasq configuration file or restart the daemon. Thanks to
	Steven Barth for extensive testing and development work on
	this idea.

	Fix crash on startup on Solaris 11. Regression probably
	introduced in 2.61.  Thanks to Geoff Johnstone for the
	patch.

	Add code to make behaviour for TCP DNS requests that same
	as for UDP requests, when a request arrives for an allowed 
	address, but via a banned interface. This change is only
	active on Linux, since the relevant API is missing (AFAIK)
	on other platforms. Many thanks to Tomas Hozza for
	spotting the problem, and doing invaluable discovery of
	the obscure and undocumented API required for the solution.

	Don't send the default DHCP option advertising dnsmasq as
	the local DNS server if dnsmasq is configured to not act
	as DNS server, or it's configured to a non-standard port.

	Add DNSMASQ_CIRCUIT_ID, DNSMASQ_SUBSCRIBER_ID,
	DNSMASQ_REMOTE_ID variables to the environment of the
	lease-change script (and the corresponding Lua). These hold
	information inserted into the DHCP request by a DHCP relay
	agent. Thanks to Lakefield Communications for providing a
	bounty for this addition.

	Fixed crash, introduced in 2.64, whilst handling DHCPv6
	information-requests with some common configurations.
	Thanks to Robert M. Albrecht for the bug report and 
	chasing the problem.

	Add --ipset option. Thanks to Jason A. Donenfeld for the 
	patch.

	Don't erroneously reject some option names in --dhcp-match
	options. Thanks to Benedikt Hochstrasser for the bug report.

	Allow a trailing '*' wildcard in all interface-name
	configurations. Thanks to Christian Parpart for the patch.

	Handle the situation where libc headers define
	SO_REUSEPORT, but the kernel in use doesn't, to cope with
	the introduction of this option to Linux. Thanks to Rich
	Felker for the bug report.

	Update Polish translation. Thanks to Jan Psota.

	Fix crash if the configured DHCP lease limit is
	reached. Regression occurred in 2.61. Thanks to Tsachi for
	the bug report. 

	Update the French translation. Thanks to Gildas le Nadan.


version 2.65
	Fix regression which broke forwarding of queries sent via
	TCP which are not for A and AAAA and which were directed to
	non-default servers. Thanks to Niax for the bug report.

	Fix failure to build with DHCP support excluded. Thanks to 
	Gustavo Zacarias for the patch.

	Fix nasty regression in 2.64 which completely broke caching.


version 2.64
	Handle DHCP FQDN options with all flag bits zero and
	--dhcp-client-update set. Thanks to Bernd Krumbroeck for
	spotting the problem.

	Finesse the check for /etc/hosts names which conflict with
	DHCP names. Previously a name/address pair in /etc/hosts
	which didn't match the name/address of a DHCP lease would
	generate a warning. Now that only happens if there is not
	also a match. This allows multiple addresses for a name in 
	/etc/hosts with one of them assigned via DHCP.

	Fix broken vendor-option processing for BOOTP. Thanks to
	Hans-Joachim Baader for the bug report.

	Don't report spurious netlink errors, regression in
	2.63. Thanks to Vladislav Grishenko for the patch.

	Flag DHCP or DHCPv6 in startup logging. Thanks to 
	Vladislav Grishenko for the patch.

	Add SetServersEx method in DBus interface. Thanks to Dan
	Williams for the patch.

	Add SetDomainServers method in DBus interface. Thanks to
	Roy Marples for the patch.

	Fix build with later Lua libraries. Thanks to Cristian
	Rodriguez for the patch.

	Add --max-cache-ttl option. Thanks to Dennis Kaarsemaker
	for the patch.

	Fix breakage of --host-record parsing, resulting in
	infinite loop at startup. Regression in 2.63. Thanks to
	Haim Gelfenbeyn for spotting this.

	Set SO_REUSEADDRESS and SO_V6ONLY options on the DHCPv6
	socket, this allows multiple instances of dnsmasq on a
	single machine, in the same way as for DHCPv4. Thanks to
	Gene Czarcinski and Vladislav Grishenko for work on this.

	Fix DHCPv6 to do access control correctly when it's 
	configured with --listen-address. Thanks to
	Gene Czarcinski for sorting this out. 

	Add a "wildcard" dhcp-range which works for any IPv6
	subnet, --dhcp-range=::,static Useful for Stateless 
	DHCPv6. Thanks to Vladislav Grishenko for the patch.

	Don't include lease-time in DHCPACK replies to DHCPINFORM
	queries, since RFC-2131 says we shouldn't. Thanks to
	Wouter Ibens for pointing this out.  

	Makefile tweak to do dependency checking on header files.
	Thanks to Johan Peeters for the patch.

	Check interface for outgoing unsolicited router 
	advertisements, rather than relying on interface address 
	configuration. Thanks to Gene Czarinski for the patch.

	Handle better attempts to transmit on interfaces which are
	still doing DAD, and specifically do not just transmit
	without setting source address and interface, since this
	can cause very puzzling effects when a router
	advertisement goes astray. Thanks again to Gene Czarinski.

	Get RA timers right when there is more than one
	dhcp-range on a subnet.


version 2.63
	Do duplicate dhcp-host address check in --test mode.

	Check that tftp-root directories are accessible before
	start-up. Thanks to Daniel Veillard for the initial patch.

	Allow more than one --tfp-root flag. The per-interface
	stuff is pointless without that.

	Add --bind-dynamic. A hybrid mode between the default and
	--bind-interfaces which copes with dynamically created
	interfaces. 

	A couple of fixes to the build system for Android. Thanks
	to Metin Kaya for the patches.

	Remove the interface:<interface> argument in --dhcp-range, and
	the interface argument to --enable-tftp. These were a
	still-born attempt to allow automatic isolated
	configuration by libvirt, but have never (to my knowledge)
	been used, had very strange semantics, and have been
	superseded by other mechanisms. 

	Fixed bug logging filenames when duplicate dhcp-host
	addresses are found. Thanks to John Hanks for the patch.

	Fix regression in 2.61 which broke caching of CNAME
	chains. Thanks to Atul Gupta for the bug report.

	Allow the target of a --cname flag to be another --cname.

	Teach DHCPv6 about the RFC 4242 information-refresh-time
	option, and add parsing if the minutes, hours and days
	format for options. Thanks to Francois-Xavier Le Bail for
	the suggestion.

	Allow "w" (for week) as multiplier in lease times, as well
	as seconds, minutes, hours and days.  Álvaro Gámez Machado 
	spotted the omission.

	Update French translation. Thanks to Gildas Le Nadan.

	Allow a DBus service name to be given with --enable-dbus
	which overrides the default,
	uk.org.thekelleys.dnsmasq. Thanks to Mathieu
	Trudel-Lapierre for the patch. 

	Set the "prefix on-link" bit in Router
	Advertisements. Thanks to Gui Iribarren for the patch.


version 2.62
	Update German translation. Thanks to Conrad Kostecki.

	Cope with router-solict packets which don't have a valid 
	source address. Thanks to Vladislav Grishenko for the patch.

	Fixed bug which caused missing periodic router
	advertisements with some configurations. Thanks to
	Vladislav Grishenko for the patch.

	Fixed bug which broke DHCPv6/RA with prefix lengths 
	which are not divisible by 8. Thanks to Andre Coetzee 
	for spotting this.

	Fix non-response to router-solicitations when
	router-advertisement configured, but DHCPv6 not
	configured. Thanks to Marien Zwart for the patch.

	Add --dns-rr, to allow arbitrary DNS resource records.

	Fixed bug which broke RA scheduling when an interface had
	two addresses in the same network. Thanks to Jim Bos for
	his help nailing this.

version 2.61
	Re-write interface discovery code on *BSD to use
	getifaddrs. This is more portable, more straightforward,
	and allows us to find the prefix length for IPv6
	addresses.

	Add ra-names, ra-stateless and slaac keywords for DHCPv6.
	Dnsmasq can now synthesise AAAA records for dual-stack 
	hosts which get IPv6 addresses via SLAAC. It is also now 
	possible to use SLAAC and stateless DHCPv6, and to 
	tell clients to use SLAAC addresses as well as DHCP ones.
	Thanks to Dave Taht for help with this.

	Add --dhcp-duid to allow DUID-EN uids to be used.

	Explicitly send DHCPv6 replies to the correct port, instead
	of relying on clients to send requests with the correct
	source address, since at least one client in the wild gets
	this wrong. Thanks to Conrad Kostecki for help tracking
	this down.

	Send a preference value of 255 in DHCPv6 replies when 
	--dhcp-authoritative is in effect. This tells clients not
	to wait around for other DHCP servers.

	Better logging of DHCPv6 options.

	Add --host-record. Thanks to Rob Zwissler for the
	suggestion.

	Invoke the DHCP script with action "tftp" when a TFTP file
	transfer completes. The size of the file, address to which
	it was sent and complete pathname are supplied. Note that
	version 2.60 introduced some script incompatibilities
	associated with DHCPv6, and this is a further change. To
	be safe, scripts should ignore unknown actions, and if
	not IPv6-aware, should exit if the environment
	variable DNSMASQ_IAID is set. The use-case for this is
	to track netboot/install.  Suggestion from Shantanu
	Gadgil.

	Update contrib/port-forward/dnsmasq-portforward to reflect
	the above.

	Set the environment variable DNSMASQ_LOG_DHCP when running
	the script id --log-dhcp is in effect, so that script can
	taylor their logging verbosity. Suggestion from Malte
	Forkel.

	Arrange that addresses specified with --listen-address
	work even if there is no interface carrying the
	address. This is chiefly useful for IPv4 loopback
	addresses, where any address in *********/8 is a valid
	loopback address, but normally only 127.0.0.1 appears on
	the lo interface. Thanks to Mathieu Trudel-Lapierre for
	the idea and initial patch. 

	Fix crash, introduced in 2.60, when a DHCPINFORM is
	received from a network which has no valid dhcp-range.
	Thanks to Stephane Glondu for the bug report.

	Add a new DHCP lease time keyword, "deprecated" for
	--dhcp-range. This is only valid for IPv6, and sets the
	preferred lease time for both DHCP and RA to zero. The
	effect is that clients can continue to use the address 
	for existing connections, but new connections will use
	other addresses, if they exist. This makes hitless
	renumbering at least possible.

	Fix bug in address6_available() which caused DHCPv6 lease
	acquisition to fail if more than one dhcp-range in use.

	Provide RDNSS and DNSSL data in router advertisements,
	using the settings provided for DHCP options
	option6:domain-search and option6:dns-server.

	Tweak logo/favicon.ico to add some transparency. Thanks to
	SamLT for work on this.

	Don't cache data from non-recursive nameservers, since it
	may erroneously look like a valid CNAME to a non-existent
	name. Thanks to Ben Winslow for finding this.

	Call SO_BINDTODEVICE on the DHCP socket(s) when doing DHCP
	on exactly one interface and --bind-interfaces is set. This 
	makes the OpenStack use-case of one dnsmasq per virtual
	interface work. This is only available on Linux; it's not
	supported on other platforms. Thanks to Vishvananda Ishaya
	and the OpenStack team for the suggestion.

	Updated French translation. Thanks to Gildas Le Nadan.

	Give correct from-cache answers to explicit CNAME queries.
	Thanks to Rob Zwissler for spotting this.

	Add --tftp-lowercase option. Thanks to Oliver Rath for the
	patch. 

	Ensure that the DBus DhcpLeaseUpdated events are generated
	when a lease goes through INIT_REBOOT state, even if the
	dhcp-script is not in use. Thanks to Antoaneta-Ecaterina
	Ene for the patch.

	Fix failure of TFTP over IPv4 on OpenBSD platform. Thanks
	to Brad Smith for spotting this.


version 2.60
	Fix compilation problem in Mac OS X Lion. Thanks to Olaf
	Flebbe for the patch.

	Fix DHCP when using --listen-address with an IP address
	which is not the primary address of an interface.

	Add --dhcp-client-update option.

	Add Lua integration. Dnsmasq can now execute a DHCP
	lease-change script written in Lua. This needs to be
	enabled at compile time by setting HAVE_LUASCRIPT in 
	src/config.h or running "make COPTS=-DHAVE_LUASCRIPT"
	Thanks to Jan-Piet Mens for the idea and proof-of-concept 
	implementation.

	Tidied src/config.h to distinguish between
	platform-dependent compile-time options which are selected
	automatically, and builder-selectable compile time
	options. Document the latter better, and describe how to
	set them from the make command line.

	Tidied up IPPROTO_IP/SOL_IP (and IPv6 equivalent)
	confusion. IPPROTO_IP works everywhere now.

	Set TOS on DHCP sockets, this improves things on busy
	wireless networks. Thanks to Dave Taht for the patch.

	Determine VERSION automatically based on git magic:
	release tags or hash values.

	Improve start-up speed when reading large hosts files 
	containing many distinct addresses.

	Fix problem if dnsmasq is started without the stdin,
	stdout and stderr file descriptors open. This can manifest
	itself as 100% CPU use. Thanks to Chris Moore for finding
	this.

	Fix shell-scripting bug in bld/pkg-wrapper. Thanks to 
	Mark Mitchell for the patch.

	Allow the TFP server or boot server in --pxe-service, to
	be a domain name instead of an IP address. This allows for
	round-robin to multiple servers, in the same way as
	--dhcp-boot. A good suggestion from Cristiano Cumer.

	Support BUILDDIR variable in the Makefile. Allows builds 
	for multiple archs from the same source tree with eg.
	make BUILDDIR=linux             (relative to dnsmasq tree)
	make BUILDDIR=/tmp/openbsd      (absolute path)
	If BUILDDIR is not set, compilation happens in the src
	directory, as before. Suggestion from Mark Mitchell.

	Support DHCPv6. Support is there for the sort of things
	the existing v4 server does, including tags, options, 
	static addresses and relay support. Missing is prefix 
	delegation, which is probably not required in the dnsmasq
	niche, and an easy way to accept prefix delegations from
	an upstream DHCPv6 server, which is. Future plans include
	support for DHCPv6 router option and MAC address option
	(to make selecting clients by MAC address work like IPv4).
	These will be added as the standards mature.
	This code has been tested, but this is the first release,
	so don't bet the farm on it just yet. Many thanks to all 
	testers who have got it this far.

	Support IPv6 router advertisements. This is a
	simple-minded implementation, aimed at providing the
	vestigial RA needed to go alongside IPv6. Is picks up
	configuration from the DHCPv6 conf, and should just need
	enabling with --enable-ra.   

	Fix long-standing wrinkle with --localise-queries that
	could result in wrong answers when DNS packets arrive
	via an interface other than the expected one. Thanks to 
	Lorenzo Milesi and John Hanks for spotting this one.

	Update French translation. Thanks to Gildas Le Nadan.

	Update Polish translation. Thanks to Jan Psota.


version 2.59
	Fix regression in 2.58 which caused failure to start up
	with some combinations of dnsmasq config and IPv6 kernel
	network config. Thanks to Brielle Bruns for the bug
	report.

	Improve dnsmasq's behaviour when network interfaces are
	still doing duplicate address detection (DAD). Previously,
	dnsmasq would wait up to 20 seconds at start-up for the
	DAD state to terminate. This is broken for bridge
	interfaces on recent Linux kernels, which don't start DAD
	until the bridge comes up, and so can take arbitrary
	time. The new behaviour lets dnsmasq poll for an arbitrary
	time whilst providing service on other interfaces. Thanks
	to Stephen Hemminger for pointing out the problem.


version 2.58
	Provide a definition of the SA_SIZE macro where it's 
	missing. Fixes build failure on openBSD.

	Don't include a zero terminator at the end of messages
	sent to /dev/log when /dev/log is a datagram socket.
	Thanks to Didier Rabound for spotting the problem.

	Add --dhcp-sequential-ip flag, to force allocation of IP
	addresses in ascending order. Note that the default
	pseudo-random mode is in general better but some
	server-deployment applications need this.

	Fix problem where a server-id of 0.0.0.0 is sent to a
	client when a dhcp-relay is in use if a client renews a
	lease after dnsmasq restart and before any clients on the
	subnet get a new lease. Thanks to Mike Ruiz for assistance
	in chasing this one down. 

	Don't return NXDOMAIN to an AAAA query if we have CNAME
	which points to an A record only: NODATA is the correct
	reply in this case. Thanks to Tom Fernandes for spotting
	the problem.

	Relax the need to supply a netmask in --dhcp-range for
	networks which use a DHCP relay. Whilst this is still
	desirable, in the absence of a netmask dnsmasq will use
	a default based on the class (A, B, or C) of the address. 
	This should at least remove a cause of mysterious failure 
	for people using RFC1918 addresses and relays.

	Add support for Linux conntrack connection marking. If 
	enabled with --conntrack, the connection mark for incoming
	DNS queries will be copied  to the outgoing connections
	used to answer those queries. This allows clever firewall
	and accounting stuff. Only available if dnsmasq is
	compiled with HAVE_CONNTRACK and adds a dependency on 
	libnetfilter-conntrack. Thanks to Ed Wildgoose for the
	initial idea, testing and sponsorship of this function.

	Provide a sane error message when someone attempts to 
	match a tag in --dhcp-host.

	Tweak the behaviour of --domain-needed, to avoid problems
	with recursive nameservers downstream of dnsmasq. The new
	behaviour only stops A and AAAA queries, and returns
	NODATA rather than NXDOMAIN replies. 

	Efficiency fix for very large DHCP configurations, thanks
	to James Gartrell and Mike Ruiz for help with this. 

	Allow the TFTP-server address in --dhcp-boot to be a
	domain-name which is looked up in /etc/hosts. This can 
	give multiple IP addresses which are used round-robin,
	thus doing TFTP server load-balancing. Thanks to Sushil
	Agrawal for the patch.

	When two tagged dhcp-options for a particular option
	number are both valid, use the one which is valid without
	a tag from the dhcp-range. Allows overriding of the value
	of a DHCP option for a particular host as well as
	per-network values.  So 
	--dhcp-range=set:interface1,......
	--dhcp-host=set:myhost,.....  
	--dhcp-option=tag:interface1,option:nis-domain,"domain1" 
	--dhcp-option=tag:myhost,option:nis-domain,"domain2" 
	will set the NIS-domain to domain1 for hosts in the range, but
	override that to domain2 for a particular host.

	Fix bug which resulted in truncated files and timeouts for
	some TFTP transfers. The bug only occurs with netascii
	transfers and needs an unfortunate relationship between
	file size, blocksize and the number of newlines in the
	last block before it manifests itself. Many thanks to 
	Alkis Georgopoulos for spotting the problem and providing
	a comprehensive test-case. 

	Fix regression in TFTP server on *BSD platforms introduced
	in version 2.56, due to confusion with sockaddr
	length. Many thanks to Loic Pefferkorn for finding this.

	Support scope-ids in IPv6 addresses of nameservers from
	/etc/resolv.conf and in --server options. Eg
	nameserver fe80::202:a412:4512:7bbf%eth0 or
	server=fe80::202:a412:4512:7bbf%eth0. Thanks to 
	Michael Stapelberg for the suggestion.

	Update Polish translation, thanks to Jan Psota.

	Update French translation. Thanks to Gildas Le Nadan.


version 2.57
	Add patches to allow build under Android.

	Provide our own header for the DNS protocol, rather than
	relying on arpa/nameser.h. This has proved more or less
	defective over the years and the final straw is that it's
	effectively empty on Android.

	Fix regression in 2.56 which caused hex constants in
	configuration to be rejected if they contain the '*'
	wildcard.

	Correct wrong casts of arguments to ctype.h functions,
	isdigit(), isxdigit() etc. Thanks to Matthias Andree for
	spotting this.

	Allow build with IDN support independently from i18n. 
	IDN support continues to be included automatically 
	when i18n is included. 
	'make COPTS=-DHAVE_IDN' is the magic incantation. 

	Modify check on extraneous command line junk (added in
	2.56) so that it doesn't complain about extra _empty_ 
	arguments. Otherwise this breaks libvirt.


version 2.56
	Add a patch to allow dnsmasq to get interface names right in a
	Solaris zone. Thanks to Dj Padzensky for this.

	Improve data-type parsing heuristics so that
	--dhcp-option=option:domain-search,. 
	treats the value as a string and not an IP address.
	Thanks to Clemens Fischer for spotting that.

	Add IPv6 support to the TFTP server. Many thanks to Jan 
	'RedBully' Seiffert for the patches.

	Log DNS queries at level LOG_INFO, rather then
	LOG_DEBUG. This makes things consistent with DHCP
	logging. Thanks to Adam Pribyl for spotting the problem.

	Ensure that dnsmasq terminates cleanly when using
	--syslog-async even if it cannot make a connection to the
	syslogd.

	Add --add-mac option. This is to support currently 
	experimental DNS filtering facilities. Thanks to Benjamin
	Petrin for the original patch. 

	Fix bug which meant that tags were ignored in dhcp-range
	configuration specifying PXE-proxy service. Thanks to
	Cristiano Cumer for spotting this.

	Raise an error if there is extra junk, not part of an
	option, on the command line.

	Flag a couple of log messages in cache.c as coming from
	the DHCP subsystem. Thanks to Olaf Westrik for the patch.

	Omit timestamps from logs when a) logging to stderr and 
	b) --keep-in-foreground is set. The logging facility on the
	other end of stderr can be assumed to supply them. Thanks
	to John Hallam for the patch.

	Don't complain about strings longer than 255 characters in
	--txt-record, just split the long strings into 255
	character chunks instead.

	Fix crash on double-free. This bug can only happen when
	dhcp-script is in use and then only in rare circumstances
	triggered by high DHCP transaction rate and a slow
	script. Thanks to Ferenc Wagner for finding the problem.

	Only log that a file has been sent by TFTP after the
	transfer has completed successfully. 

	A good suggestion from Ferenc Wagner: extend
	the --domain option to allow this sort of thing:
	--domain=thekelleys.org.uk,***********/24,local
	which automatically creates
	--local=/thekelleys.org.uk/
	--local=/0.168.192.in-addr.arpa/ 

	Tighten up syntax checking of hex constants in the config
	file.  Thanks to Fred Damen for spotting this.

	Add dnsmasq logo/icon, contributed by Justin Swift. Many
	thanks for that.

	Never cache DNS replies which have the 'cd' bit set, or
	which result from queries forwarded with the 'cd' bit
	set. The 'cd' bit instructs a DNSSEC validating server
	upstream to ignore signature failures and return replies
	anyway. Without this change it's possible to pollute the
	dnsmasq cache with bad data by making a query with the
	'cd' bit set and subsequent queries would return this data
	without its being marked as suspect. Thanks to Anders
	Kaseorg for pointing out this problem.

	Add --proxy-dnssec flag, for compliance with RFC
	4035. Dnsmasq will now clear the 'ad' bit in answers returned
	from upstream validating nameservers unless this option is
	set.

	Allow a filename of "-" for --conf-file to read
	stdin. Suggestion from Timothy Redaelli.

	Rotate the order of SRV records in replies, to provide
	round-robin load balancing when all the priorities are
	equal. Thanks to Peter McKinney for the suggestion. 

	Edit
	contrib/MacOSX-launchd/uk.org.thekelleys.dnsmasq.plist 
	so that it doesn't log all queries to a file by
	default. Thanks again to Peter McKinney.    

	By default, setting an IPv4 address for a domain but not
	an IPv6 address causes dnsmasq to return
	a NODATA reply for IPv6 (or vice-versa). So
	--address=/google.com/******* stops IPv6 queries for
	*google.com from being forwarded. Make it possible to
	override this behaviour by defining the semantics if the
	same domain appears in  both --server and --address.
	In that case, the --address has priority for the address
	family in which is appears, but the --server has priority
	of the address family which doesn't appear in --address  
	So:
	--address=/google.com/*******
	--server=/google.com/#
	will return ******* for IPv4 queries for *.google.com but
	forward IPv6 queries to the normal upstream nameserver.
	Similarly when setting an IPv6 address
	only this will allow forwarding of IPv4 queries. Thanks to
	William for pointing out the need for this.

	Allow more than one --dhcp-optsfile and --dhcp-hostsfile
	and make them understand directories as arguments in the
	same way as --addn-hosts. Suggestion from John Hanks. 

	Ignore rebinding requests for leases we don't know
	about. Rebind is broadcast, so we might get to overhear a
	request meant for another DHCP server. NAKing this is
	wrong. Thanks to Brad D'Hondt for assistance with this.

	Fix cosmetic bug which produced strange output when
	dumping cache statistics with some configurations. Thanks
	to Fedor Kozhevnikov for spotting this.


version 2.55
	Fix crash when /etc/ethers is in use. Thanks to 
	Gianluigi Tiesi for finding this.

	Fix crash in netlink_multicast(). Thanks to Arno Wald for
	finding this one.

	Allow the empty domain "." in dhcp domain-search (119)
	options. 


version 2.54
	There is no version 2.54 to avoid confusion with 2.53,
	which incorrectly identifies itself as 2.54.


version 2.53
	Fix failure to compile on Debian/kFreeBSD. Thanks to 
	Axel Beckert and Petr Salinger.

	Fix code to avoid scary strict-aliasing warnings
	generated by gcc 4.4.
	
	Added FAQ entry warning about DHCP failures with Vista
	when firewalls block ***************.
	
	Fixed bug which caused bad things to happen if a 
	resolv.conf file which exists is subsequently removed.
	Thanks to Nikolai Saoukh for the patch.

	Rationalised the DHCP tag system. Every configuration item
	which can set a tag does so by adding "set:<tag>" and
	every configuration item which is conditional on a tag is
	made so by "tag:<tag>". The NOT operator changes to '!',
	which is a bit more intuitive too. Dhcp-host directives
	can set more than one tag now. The old '#' NOT, 
	"net:" prefix and no-prefixes are still honoured, so 
	no existing config file needs to be changed, but 
	the documentation and new-style config files should be 
	much less confusing. 

	Added --tag-if to allow boolean operations on tags. 
	This allows complicated logic to be clearer and more 
	general. A great suggestion from Richard Voigt. 

	Add broadcast/unicast information to DHCP logging.

	Allow --dhcp-broadcast to be unconditional.

	Fixed incorrect behaviour with NOT <tag> conditionals in
	dhcp-options. Thanks to Max Turkewitz for assistance
	finding this.

	If we send vendor-class encapsulated options based on the
	vendor-class supplied by the client, and no explicit 
	vendor-class option is given, echo back the vendor-class
	from the client.
	
	Fix bug which stopped dnsmasq from matching both a
	circuitid and a remoteid. Thanks to Ignacio Bravo for
	finding this.

	Add --dhcp-proxy, which makes it possible to configure
	dnsmasq to use a DHCP relay agent as a full proxy, with
	all DHCP messages passing through the proxy. This is
	useful if the relay adds extra information to the packets
	it forwards, but cannot be configured with the RFC 5107 
	server-override option.

	Added interface:<iface name> part to dhcp-range. The
	semantics of this are very odd at first sight, but it
	allows a single line  of the form
	dhcp-range=interface:virt0,192.168.0.4,192.168.0.200
	to be added to dnsmasq configuration which then supplies
	DHCP and DNS services to that interface, without affecting
	what services are supplied to other interfaces and 
	irrespective of the existence or lack of 
	interface=<interface> 
	lines elsewhere in the dnsmasq configuration. The idea is
	that such a line can be added automatically by libvirt
	or equivalent systems, without disturbing any manual
	configuration.

	Similarly to the above, allow --enable-tftp=<interface>

	Allow a TFTP root to be set separately for requests via
	different interfaces, --tftp-root=<path>,<interface>             

	Correctly handle and log clashes between CNAMES and 
	DNS names being given to DHCP leases. This fixes a bug 
	which caused nonsense IP addresses to be logged. Thanks to 
	Sergei Zhirikov for finding and analysing the problem.

	Tweak flush_log so as to avoid leaving the log
	file in non-blocking mode. O_NONBLOCK is a property of the
	file, not the process/descriptor.

	Fix contrib/Solaris10/create_package
	(/usr/man -> /usr/share/man) Thanks to Vita Batrla.

	Fix a problem where, if a client got a lease, then went
	to another subnet and got another lease, then moved back,
	it couldn't resume the old lease, but would instead get 
	a new address. Thanks to Leonardo Rodrigues for spotting
	this and testing the fix.

	Fix weird bug which sometimes omitted certain characters
	from the start of quoted strings in dhcp-options. Thanks
	to Dayton Turner for spotting the problem.

	Add facility to redirect some domains to the standard
	upstream servers: this allows something like 
	--server=/google.com/******* --server=/www.google.com/#
	which will send queries for *.google.com to *******,
	except *www.google.com which will be forwarded as usual.
	Thanks to AJ Weber for prompting this addition.

	Improve the hash-algorithm used to generate IP addresses
	from MAC addresses during initial DHCP address
	allocation. This improves performance when large numbers
	of hosts with similar MAC addresses all try and get an IP
	address at the same time. Thanks to Paul Smith for his
	work on this.

	Tweak DHCP code so that --bridge-interface can be used to
	select which IP alias of an interface should be used for
	DHCP purposes on Linux. If eth0 has an alias eth0:dhcp
	then adding  --bridge-interface=eth0:dhcp,eth0 will use 
	the address of eth0:dhcp to determine the correct subnet 
	for DHCP address allocation. Thanks to Pawel Golaszewski 
	for prompting this and Eric Cooper for further testing.

	Add --dhcp-generate-names. Suggestion by Ferenc Wagner.

	Tweak DNS server selection algorithm when there is more
	than one server available for a domain, eg.
	--server=/mydomain/*******
	--server=/mydomain/*******
	Thanks to Alberto Cuesta-Canada for spotting a weakness
	here.

	Add --max-ttl. Thanks to Fredrik Ringertz for the patch.

	Allow --log-facility=- to force all logging to
	stderr. Suggestion from Clemens Fischer.

	Fix regression which caused configuration like
	--address=/.domain.com/******* to be rejected. The dot to the 
	left of the domain has been implied and not required for a
	long time, but it should be accepted for backward
	compatibility. Thanks to Andrew Burcin for spotting this.

	Add --rebind-domain-ok and --rebind-localhost-ok.
	Suggestion from Clemens Fischer.

	Log replies to queries of type TXT, when --log-queries 
	is set.

	Fix compiler warnings when compiled with -DNO_DHCP. Thanks
	to Shantanu Gadgil for the patch.

	Updated French translation. Thanks to Gildas Le Nadan.

	Updated Polish translation. Thanks to Jan Psota.

	Updated German translation. Thanks to Matthias Andree.

	Added contrib/static-arp, thanks to Darren Hoo.

	Fix corruption of the domain when a name from /etc/hosts
	overrides one supplied by a DHCP client. Thanks to Fedor
	Kozhevnikov for spotting the problem.

	Updated Spanish translation. Thanks to Chris Chatham.


version 2.52
	Work around a Linux kernel bug which insists that the 
	length of the option passed to setsockopt must be at least
	sizeof(int) bytes, even if we're calling SO_BINDTODEVICE
	and the device name is "lo".  Note that this is fixed 
	in kernel 2.6.31, but the workaround is harmless and 
	allows earlier kernels to be used. Also fix dnsmasq 
	bug which reported the wrong address when this failed. 
	Thanks to Fedor for finding this.

	The API for IPv6 PKTINFO changed around Linux kernel
	2.6.14. Workaround the case where dnsmasq is compiled
	against newer headers, but then run on an old kernel:
	necessary for some *WRT distros.

	Re-read the set of network interfaces when re-loading
	/etc/resolv.conf if --bind-interfaces is not set. This
	handles the case that loopback interfaces do not exist
	when dnsmasq is first started.

	Tweak the PXE code to support port 4011. This should
	reduce broadcasts and make things more reliable when other
	servers are around. It also improves inter-operability
	with certain clients.

	Make a pxe-service configuration with no filename or boot 
	service type legal: this does a local boot. eg.
	pxe-service=x86PC, "Local boot" 

	Be more conservative in detecting "A for A"
	queries. Dnsmasq checks if the name in a type=A query looks
	like a dotted-quad IP address and answers the query itself
	if so, rather than forwarding it. Previously dnsmasq
	relied in the library function inet_addr() to convert
	addresses, and that will accept some things which are
	confusing in this context, like 1.2.3 or even just
	1234. Now we only do A for A processing for four decimal
	numbers delimited by dots.

	A couple of tweaks to fix compilation on Solaris. Thanks
	to Joel Macklow for help with this.

	Another Solaris compilation tweak, needed for Solaris
	2009.06. Thanks to Lee Essen for that.

	Added extract packaging stuff from Lee Essen to 
	contrib/Solaris10.

	Increased the default limit on number of leases to 1000
	(from 150). This is mainly a defence against DoS attacks,
	and for the average "one for two class C networks"
	installation, IP address exhaustion does that just as
	well. Making the limit greater than the number of IP
	addresses available in such an installation removes a
	surprise which otherwise can catch people out.

	Removed extraneous trailing space in the value of the
	DNSMASQ_TIME_REMAINING DNSMASQ_LEASE_LENGTH and
	DNSMASQ_LEASE_EXPIRES environment variables. Thanks to
	Gildas Le Nadan for spotting this.

	Provide the network-id tags for a DHCP transaction to 
	the lease-change script in the environment variable
	DNSMASQ_TAGS. A good suggestion from Gildas Le Nadan.  

	Add support for RFC3925 "Vendor-Identifying Vendor
	Options". The syntax looks like this:  
	--dhcp-option=vi-encap:<enterprise number>, .........

	Add support to --dhcp-match to allow matching against
	RFC3925 "Vendor-Identifying Vendor Classes". The syntax
	looks like this:
	--dhcp-match=tag,vi-encap<enterprise number>, <value>

	Add some application specific code to assist in
	implementing the Broadband forum TR069 CPE-WAN
	specification. The details are in contrib/CPE-WAN/README

	Increase the default DNS packet size limit to 4096, as
	recommended by RFC5625 section 4.4.3. This can be
	reconfigured using --edns-packet-max if needed. Thanks to
	Francis Dupont for pointing this out.

	Rewrite query-ids even for TSIG signed packets, since
	this is allowed by RFC5625 section 4.5.

	Use getopt_long by default on OS X. It has been supported
	since version 10.3.0. Thanks to Arek Dreyer for spotting
	this.

	Added up-to-date startup configuration for MacOSX/launchd
	in contrib/MacOSX-launchd. Thanks to Arek Dreyer for
	providing this.

	Fix link error when including Dbus but excluding DHCP. 
	Thanks to Oschtan for the bug report.

	Updated French translation. Thanks to Gildas Le Nadan.

	Updated Polish translation. Thanks to Jan Psota.

	Updated Spanish translation. Thanks to Chris Chatham.

	Fixed confusion about domains, when looking up DHCP hosts
	in /etc/hosts. This could cause spurious "Ignoring
	domain..." messages. Thanks to Fedor Kozhevnikov for
	finding and analysing the problem.


version 2.51
	Add support for internationalised DNS. Non-ASCII characters
	in domain names found in /etc/hosts, /etc/ethers and 
	/etc/dnsmasq.conf will be correctly handled by translation to
	punycode, as specified in RFC3490. This function is only
	available if dnsmasq is compiled with internationalisation
	support, and adds a dependency on GNU libidn. Without i18n
	support, dnsmasq continues to be compilable with just
	standard tools. Thanks to Yves Dorfsman for the
	suggestion. 

	Add two more environment variables for lease-change scripts:
	First, DNSMASQ_SUPPLIED_HOSTNAME; this is set to the hostname
	supplied by a client, even if the actual hostname used is
	over-ridden by dhcp-host or dhcp-ignore-names directives.
	Also DNSMASQ_RELAY_ADDRESS which gives the address of 
	a DHCP relay, if used.
	Suggestions from Michael Rack.

	Fix regression which broke echo of relay-agent
	options. Thanks to Michael Rack for spotting this.

	Don't treat option 67 as being interchangeable with
	dhcp-boot parameters if it's specified as
	dhcp-option-force.

	Make the code to call scripts on lease-change compile-time
	optional. It can be switched off by editing src/config.h
	or building with "make COPTS=-DNO_SCRIPT".

	Make the TFTP server cope with filenames from Windows/DOS
	which use '\' as pathname separator. Thanks to Ralf for
	the patch.

	Updated Polish translation. Thanks to Jan Psota.

	Warn if an IP address is duplicated in /etc/ethers. Thanks
	to Felix Schwarz for pointing this out.

	Teach --conf-dir to take an option list of file suffices
	which will be ignored when scanning the directory. Useful
	for backup files etc. Thanks to Helmut Hullen for the
	suggestion. 

	Add new DHCP option named tftpserver-address, which
	corresponds to the third argument of dhcp-boot. This
	allows the complete functionality of dhcp-boot to be
	replicated with dhcp-option. Useful when using 
	dhcp-optsfile.

	Test which upstream nameserver to use every 10 seconds
	or 50 queries and not just when a query times out and 
	is retried. This should improve performance when there
	is a slow nameserver in the list. Thanks to Joe for the
	suggestion. 

	Don't do any PXE processing, even for clients with the 
	correct vendorclass, unless at least one pxe-prompt or 
	pxe-service option is given. This stops dnsmasq 
	interfering with proxy PXE subsystems when it is just 
	the DHCP server. Thanks to Spencer Clark for spotting this.

	Limit the blocksize used for TFTP transfers to a value
	which avoids packet fragmentation, based on the MTU of the
	local interface. Many netboot ROMs can't cope with
	fragmented packets.

	Honour dhcp-ignore configuration for PXE and proxy-PXE 
	requests. Thanks to Niels Basjes for the bug report.

	Updated French translation. Thanks to Gildas Le Nadan.


version 2.50
	Fix security problem which allowed any host permitted to 
	do TFTP to possibly compromise dnsmasq by remote buffer 
	overflow when TFTP enabled. Thanks to Core Security 
	Technologies and Iván Arce, Pablo Hernán Jorge, Alejandro 
	Pablo Rodriguez, Martín Coco, Alberto Soliño Testa and
	Pablo Annetta. This problem has Bugtraq id: 36121 
	and CVE: 2009-2957

	Fix a problem which allowed a malicious TFTP client to 
	crash dnsmasq. Thanks to Steve Grubb at Red Hat for 
	spotting this. This problem has Bugtraq id: 36120 and 
	CVE: 2009-2958


version 2.49
	Fix regression in 2.48 which disables the lease-change
	script. Thanks to Jose Luis Duran for spotting this.

	Log TFTP "file not found" errors. These were not logged,
	since a normal PXELinux boot generates many of them, but
	the lack of the messages seems to be more confusing than
	routinely seeing them when there is no real error.

	Update Spanish translation. Thanks to Chris Chatham.


version 2.48
	Archived the extensive, backwards, changelog to
	CHANGELOG.archive. The current changelog now runs from
	version 2.43 and runs conventionally.

	Fixed bug which broke binding of servers to physical
	interfaces when interface names were longer than four
	characters. Thanks to MURASE Katsunori for the patch.

	Fixed netlink code to check that messages come from the
	correct source, and not another userspace process. Thanks
	to Steve Grubb for the patch.

	Maintainability drive: removed bug and missing feature
	workarounds for some old platforms. Solaris 9, OpenBSD
	older than 4.1, Glibc older than 2.2, Linux 2.2.x and 
	DBus older than 1.1.x are no longer supported. 

	Don't read included configuration files more than once:
	allows complex configuration structures without problems.

	Mark log messages from the various subsystems in dnsmasq:
	messages from the DHCP subsystem now have the ident string
	"dnsmasq-dhcp" and messages from TFTP have ident
	"dnsmasq-tftp". Thanks to Olaf Westrik for the patch.

	Fix possible infinite DHCP protocol loop when an IP
	address nailed to a hostname (not a MAC address)  and a 
	host sometimes provides the name, sometimes not.

	Allow --addn-hosts to take a directory: all the files 
	in the directory are read. Thanks to Phil Cornelius for 
	the suggestion. 

	Support --bridge-interface on all platforms, not just BSD.

	Added support for advanced PXE functions. It's now
	possible to define a prompt and menu options which will
	be displayed when a client PXE boots. It's also possible to
	hand-off booting to other boot servers. Proxy-DHCP, where
	dnsmasq just supplies the PXE information and another DHCP
	server does address allocation, is also allowed. See the
	--pxe-prompt and --pxe-service keywords. Thanks to 
	Alkis Georgopoulos for the suggestion and Guilherme Moro
	and Michael Brown for assistance.

	Improvements to DHCP logging. Thanks to Tom Metro for
	useful suggestions.

	Add ability to build dnsmasq without DHCP support. To do
	this, edit src/config.h or build with
	"make COPTS=-DNO_DHCP". Thanks to Mahavir Jain for the patch. 

	Added --test command-line switch - syntax check
	configuration files only.

	Updated French translation. Thanks to Gildas Le Nadan.


version 2.47
	Updated French translation. Thanks to Gildas Le Nadan.

	Fixed interface enumeration code to work on NetBSD
	5.0. Thanks to Roy Marples for the patch. 

	Updated config.h to use the same location for the lease
	file on NetBSD as the other *BSD variants. Also allow
	LEASEFILE and CONFFILE symbols to be overridden in CFLAGS.  

	Handle duplicate address detection on IPv6 more
	intelligently. In IPv6, an interface can have an address
	which is not usable, because it is still undergoing DAD
	(such addresses are marked "tentative"). Attempting to
	bind to an address in this state returns an error,
	EADDRNOTAVAIL. Previously, on getting such an error,
	dnsmasq would silently abandon the address, and never
	listen on it. Now, it retries once per second for 20
	seconds before generating a fatal error. 20 seconds should
	be long enough for any DAD process to complete, but can be
	adjusted in src/config.h if necessary. Thanks to Martin
	Krafft for the bug report.

	Add DBus introspection. Patch from Jeremy Laine.

	Update Dbus configuration file. Patch from Colin Walters.
	Fix for this bug:
	http://bugs.freedesktop.org/show_bug.cgi?id=18961

	Support arbitrarily encapsulated DHCP options, suggestion
	and initial patch from Samium Gromoff. This is useful for
	(eg) iPXE, which expect all its private options to be
	encapsulated inside a single option 175. So, eg, 

	dhcp-option = encap:175, 190, "iscsi-client0"
	dhcp-option = encap:175, 191, "iscsi-client0-secret"

	will provide iSCSI parameters to iPXE.

	Enhance --dhcp-match to allow testing of the contents of a
	client-sent option, as well as its presence. This
	application in mind for this is RFC 4578
	client-architecture specifiers, but it's generally useful.
	Joey Korkames suggested the enhancement. 

	Move from using the IP_XMIT_IF ioctl to IP_BOUND_IF on
	OpenSolaris. Thanks to Bastian Machek for the heads-up.

	No longer complain about blank lines in
	/etc/ethers. Thanks to Jon Nelson for the patch.

	Fix binding of servers to physical devices, eg
	--server=/domain/*******@eth0 which was broken from 2.43
	onwards unless --query-port=0 set. Thanks to Peter Naulls
	for the bug report.

	Reply to DHCPINFORM requests even when the supplied ciaddr
	doesn't fall in any dhcp-range. In this case it's not
	possible to supply a complete configuration, but
	individually-configured options (eg PAC) may be useful.

	Allow the source address of an alias to be a range:
	--alias=***********,10.0.0.0,************* maps the whole
	subnet ***********->************* to 10.0.0.0->**********,
	as before.
	--alias=************-************,10.0.0.0,*************
	maps only the ************->************ region. Thanks to
	Ib Uhrskov for the suggestion.

	Don't dynamically allocate DHCP addresses which may break
	Windows.  Addresses which end in .255 or .0 are broken in
	Windows even when using supernetting.
	--dhcp-range=***********,*************,255,255,254.0 means 
	************* is a valid IP address, but not for Windows. 
	See Microsoft KB281579. We therefore no longer allocate 
	these addresses to avoid hard-to-diagnose problems. 

	Update Polish translation. Thanks to Jan Psota.

	Delete the PID-file when dnsmasq shuts down. Note that by
	this time, dnsmasq is normally not running as root, so
	this will fail if the PID-file is stored in a root-owned
	directory; such failure is silently ignored. To take
	advantage of this feature, the PID-file must be stored in a
	directory owned and write-able by the user running
	dnsmasq.


version 2.46
	Allow --bootp-dynamic to take a netid tag, so that it may
	be selectively enabled. Thanks to Olaf Westrik for the
	suggestion. 

	Remove ISC-leasefile reading code. This has been
	deprecated for a long time, and last time I removed it, it
	ended up going back by request of one user. This time,
	it's gone for good; otherwise it would need to be
	re-worked to support multiple domains (see below).

	Support DHCP clients in multiple DNS domains. This is a
	long-standing request. Clients are assigned to a domain
	based in their IP address.  

	Add --dhcp-fqdn flag, which changes behaviour if DNS names
	assigned to DHCP clients. When this is set, there must be
	a domain associated with each client, and only
	fully-qualified domain names are added to the DNS. The
	advantage is that the only the FQDN needs to be unique,
	so that two or more DHCP clients can share a hostname, as
	long as they are in different domains.

	Set environment variable DNSMASQ_DOMAIN when invoking
	lease-change script. This may be useful information to
	have now that it's variable.

	Tighten up data-checking code for DNS packet
	handling. Thanks to Steve Dodd who found certain illegal
	packets which could crash dnsmasq. No memory overwrite was
	possible, so this is not a security issue beyond the DoS
	potential.  

	Update example config dhcp option 47, the previous
	suggestion generated an illegal, zero-length,
	option. Thanks to Matthias Andree for finding this.

	Rewrite hosts-file reading code to remove the limit of
	1024 characters per line. John C Meuser found this.

	Create a net-id tag with the name of the interface on
	which the DHCP request was received.

	Fixed minor memory leak in DBus code, thanks to Jeremy
	Laine for the patch.

	Emit DBus signals as the DHCP lease database
	changes. Thanks to Jeremy Laine for the patch.

	Allow for more that one MAC address in a dhcp-host
	line. This configuration tells dnsmasq that it's OK to
	abandon a DHCP lease of the fixed address to one MAC
	address, if another MAC address in the dhcp-host statement 
	asks for an address. This is useful to give a fixed
	address to a host which has two network interfaces
	(say, a laptop with wired and wireless interfaces.) 
	It's very important to ensure that only one interface 
	at a time is up, since dnsmasq abandons the first lease 
	and re-uses the address before the leased time has
	elapsed. John Gray suggested this.

	Tweak the response to a DHCP request packet with a wrong
	server-id when --dhcp-authoritative is set; dnsmasq now
	returns a DHCPNAK, rather than silently ignoring the
	packet. Thanks to Chris Marget for spotting this
	improvement.

	Add --cname option. This provides a limited alias
	function, usable for DHCP names. Thanks to AJ Weber for
	suggestions on this.

	Updated contrib/webmin with latest version from Neil
	Fisher.

	Updated Polish translation. Thanks to Jan Psota.

	Correct the text names for DHCP options 64 and 65 to be
	"nis+-domain" and "nis+-servers".

	Updated Spanish translation. Thanks to Chris Chatham.

	Force re-reading of /etc/resolv.conf when an "interface
	up" event occurs.


version 2.45
	Fix total DNS failure in release 2.44 unless --min-port 
	specified. Thanks to Steven Barth and Grant Coady for
	bugreport. Also reject out-of-range port spec, which could
	break things too: suggestion from Gilles Espinasse.


version 2.44
	Fix  crash when unknown client attempts to renew a DHCP
	lease, problem introduced in version 2.43. Thanks to
	Carlos Carvalho for help chasing this down.

	Fix potential crash when a host which doesn't have a lease
	does DHCPINFORM. Again introduced in 2.43. This bug has
	never been reported in the wild.

	Fix crash in netlink code introduced in 2.43. Thanks to
	Jean Wolter for finding this.

	Change implementation of min_port to work even if min-port
	is large.

	Patch to enable compilation of latest Mac OS X. Thanks to
	David Gilman.

	Update Spanish translation. Thanks to Christopher Chatham.


version 2.43
	Updated Polish translation. Thanks to Jan Psota.

	Flag errors when configuration options are repeated
	illegally.

	Further tweaks for GNU/kFreeBSD

	Add --no-wrap to msgmerge call - provides nicer .po file
	format.

	Honour lease-time spec in dhcp-host lines even for
	BOOTP. The user is assumed to known what they are doing in
	this case. (Hosts without the time spec still get infinite
	leases for BOOTP, over-riding the default in the
	dhcp-range.) Thanks to Peter Katzmann for uncovering this.

	Fix problem matching relay-agent ids. Thanks to Michael
	Rack for the bug report.

	Add --naptr-record option. Suggestion from Johan
	Bergquist.

	Implement RFC 5107 server-id-override DHCP relay agent
	option.

	Apply patches from Stefan Kruger for compilation on
	Solaris 10 under Sun studio.

	Yet more tweaking of Linux capability code, to suppress
	pointless wingeing from kernel 2.6.25 and above.

	Improve error checking during startup. Previously, some
	errors which occurred during startup would be worked
	around, with dnsmasq still starting up. Some were logged,
	some silent. Now, they all cause a fatal error and dnsmasq 
	terminates with a non-zero exit code. The errors are those
	associated with changing uid and gid, setting process 
	capabilities and writing the pidfile. Thanks to Uwe
	Gansert and the Suse security team for pointing out 
	this improvement, and Bill Reimers for good implementation
	suggestions.

	Provide NO_LARGEFILE compile option to switch off largefile
	support when compiling against versions of uclibc which
	don't support it. Thanks to Stephane Billiart for the patch.

	Implement random source ports for interactions with
	upstream nameservers. New spoofing attacks have been found
	against nameservers which do not do this, though it is not
	clear if dnsmasq is vulnerable, since to doesn't implement
	recursion. By default dnsmasq will now use a different
	source port (and socket) for each query it sends
	upstream. This behaviour can suppressed using the
	--query-port option, and the old default behaviour
	restored using --query-port=0. Explicit source-port
	specifications in --server configs are still honoured.

	Replace the random number generator, for better
	security. On most BSD systems, dnsmasq uses the
	arc4random() RNG, which is secure, but on other platforms,
	it relied on the C-library RNG, which may be
	guessable and therefore allow spoofing. This release
	replaces the libc RNG with the SURF RNG, from Daniel
	J. Berstein's DJBDNS package.  

	Don't attempt to change user or group or set capabilities
	if dnsmasq is run as a non-root user. Without this, the
	change from soft to hard errors when these fail causes
	problems for non-root daemons listening on high
	ports. Thanks to Patrick McLean for spotting this.

	Updated French translation. Thanks to Gildas Le Nadan.


version 2.42
	The changelog for version 2.42 and earlier is 
	available in CHANGELOG.archive.
