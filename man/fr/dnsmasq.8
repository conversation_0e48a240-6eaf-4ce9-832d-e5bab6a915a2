.TH DNSMASQ 8
.SH NAME
Dnsmasq \- Un serveur DHCP et cache DNS poids-plume.
.SH SYNOPSIS
.B dnsmasq
.I [OPTION]...
.SH "DESCRIPTION"
.BR dnsmasq
est un serveur à faible empreinte mémoire faisant DNS, TFTP, PXE, annonces de
routeurs et DHCP. Il offre à la fois les services DNS et DHCP pour un réseau
local (LAN).
.PP
Dnsmasq accepte les requêtes DNS et y répond soit en utilisant un petit cache
local, soit en effectuant une requête à un serveur DNS récursif externe (par
exemple celui de votre fournisseur d'accès internet). Il charge le contenu du
fichier /etc/hosts afin que les noms locaux n'apparaissant pas dans les DNS
globaux soient tout de même résolus, et assure également la résolution de nom
pour les hôtes présents dans le service DHCP. Il peut aussi agir en temps que
serveur DNS faisant autorité pour un ou plusieurs domaines, permettant à des
noms locaux d'apparaitre dans le DNS global.
.PP
Le serveur DHCP de Dnsmasq supporte les définitions d'adresses statiques et les
réseaux multiples. Il fournit par défaut un jeu raisonnable de paramètres DHCP,
et peut être configuré pour fournir n'importe quelle option DHCP.
Il inclut un serveur TFTP sécurisé en lecture seule permettant le démarrage via
le réseau/PXE de clients DHCP et supporte également le protocole BOOTP. Le
support PXE est complet, et comprend un mode proxy permettant de fournir des
informations PXE aux clients alors que l'allocation d'adresse via DHCP est
effectuée par un autre serveur.
.PP
Le serveur DHCPv6 de dnsmasq possède non seulement les mêmes fonctionnalités
que le serveur DHCPv4, mais aussi le support des annonces de routeurs ainsi
qu'une fonctionnalité permettant l'addition de ressources AAAA pour des
clients utilisant DHCPv4 et la configuration IPv6 sans état (stateless
autoconfiguration).
Il inclut le support d'allocations d'adresses (à la fois en DHCPv6 et en
annonces de routeurs - RA) pour des sous-réseaux dynamiquement délégués via
une délégation de préfixe DHCPv6.
.PP
Dnsmasq est développé pour de petits systèmes embarqués. Il tend à avoir
l'empreinte mémoire la plus faible possible pour les fonctions supportées,
et permet d'exclure les fonctions inutiles du binaire compilé.
.SH OPTIONS
Notes : Il est possible d'utiliser des options sans leur donner de paramètre.
Dans ce cas, la fonction correspondante sera désactivée. Par exemple
.B --pid-file=
(sans paramètre après le =) désactive l'écriture du fichier PID.
Sur BSD, à moins que le logiciel ne soit compilé avec la bibliothèque GNU
getopt, la forme longue des options ne fonctionne pas en ligne de commande; elle
est toujours supportée dans le fichier de configuration.
.TP
.B --test
Vérifie la syntaxe du ou des fichiers de configuration. Se termine avec le
code de retour 0 si tout est OK, ou un code différent de 0 dans le cas
contraire. Ne démarre pas Dnsmasq.
.TP
.B \-w, --help
Affiche toutes les options de ligne de commande.
.B --help dhcp
affiche les options de configuration connues pour DHCPv4, et
.B --help dhcp6
affiche les options de configuration connues pour DHCPv6.
.TP
.B \-h, --no-hosts
Ne pas charger les noms d'hôtes du fichier /etc/hosts.
.TP
.B \-H, --addn-hosts=<fichier>
Fichiers d'hôtes additionnels. Lire le fichier spécifié en plus de /etc/hosts.
Si 
.B -h
est spécifié, lire uniquement le fichier spécifié. Cette option peut être
répétée afin d'ajouter d'autres fichiers. Si un répertoire est donné, lis les
fichiers contenus dans ce répertoire.
.TP
.B \-E, --expand-hosts
Ajoute le nom de domaine aux noms simples (ne contenant pas de point dans le
nom) contenus dans le fichier /etc/hosts, de la même façon que pour le service
DHCP. Notez que cela ne s'applique pas aux noms de domaine dans les CNAME, les
enregistrements PTR, TXT, etc...
.TP
.B \-T, --local-ttl=<durée>
Lorsque Dnsmasq répond avec une information provenant du fichier /etc/hosts ou
avec un bail DHCP, il donne un temps de vie (time-to-live) positionné à zéro,
afin d'indiquer à la machine faisant la requête que celle-ci  ne doit pas être
mise dans un cache. Ceci est le comportement correct dans presque toutes les
situations.
Cette option permet de spécifier la valeur de time-to-live à retourner (en
secondes). Cela permet de réduire la charge sur le serveur, mais les clients
risquent d'utiliser des données périmées dans certains cas.
.TP
.B --neg-ttl=<durée>
Les réponses négatives provenant des serveurs amonts contiennent normalement
une information de durée de vie (time-to-live) dans les enregistrements SOA,
information dont dnsmasq se sert pour mettre la réponse en cache. Si la réponse
du serveur amont omet cette information, dnsmasq ne cache pas la réponse. Cette
option permet de donner une valeur de durée de vie par défaut (en secondes) que
dnsmasq utilise pour mettre les réponses négatives dans son cache, même en
l'absence d'enregistrement SOA.
.TP
.B --max-ttl=<durée>
Définie la valeur de TTL maximum qui sera fournie aux clients. La valeur maximum
de TTL spécifiée sera fournie aux clients en remplacement de la vraie valeur de
TTL si cette dernière est supérieure. La valeur réelle de TTL est cependant
conservée dans le cache afin d'éviter de saturer les serveurs DNS en amont.
.TP
.B --max-cache-ttl=<durée>
Définie la valeur de TTL maximum pour les entrées dans le cache
.TP
.B --auth-ttl=<durée>
Définie la valeur de TTL retournée pour les réponses du serveur faisant
autorité.
.TP
.B \-k, --keep-in-foreground
Ne pas aller en tâche de fond au lancement, mais en dehors de cela, fonctionner
normalement. Ce mode est prévu pour les cas où Dnsmasq est lancé par daemontools
ou launchd.
.TP
.B \-d, --no-daemon
Mode debug (déverminage) : ne pas aller en tâche de fond, ne pas écrire de
fichier pid, ne pas changer d'identifiant utilisateur, générer un état complet
du cache lors de la réception d'un signal SIGUSR1, envoyer les logs sur la
sortie standard d'erreur ("stderr") de même que dans le syslog, ne pas créer de
processus fils pour traiter les requêtes TCP. A noter que cette option est à
user pour du déverminage seulement : pour empêcher dnsmasq se fonctionner en
mode démon en production, utiliser
.B -k.
.TP
.B \-q, --log-queries
Enregistrer les résultats des requêtes DNS traitées par Dnsmasq dans un fichier
de traces ("logs"). Active la génération d'un état complet du cache lors de la
réception d'un signal SIGUSR1.
.TP
.B \-8, --log-facility=<facility>
Définit la "facility" dans laquelle Dnsmasq enverra ses entrées syslog, par
défaut DAEMON ou LOCAL0 si le mode debug est activé. Si la "facility" contient
au moins un caractère "/", alors Dnsmasq considère qu'il s'agit d'un fichier et
enverra les logs dans le fichier correspondant à la place du syslog. Si la
"facility" est '-', alors dnsmasq envoie les logs sur la sortie d'erreur
standard stderr. (Les erreurs lors de la lecture de la configuration vont
toujours vers le syslog, mais tous les messages postérieurs à un démarrage
réussi seront exclusivement envoyés vers le fichier de logs).
Lorsque Dnsmasq est configuré pour envoyer
ses traces vers un fichier, la réception d'un signal SIGUSR2 entraine la
fermeture et réouverture du fichier. Cela permet la rotation de fichiers de
traces sans nécessiter l'arrêt de Dnsmasq.
.TP
.B --log-async[=<lignes>]
Permet l'envoi de traces de manière asynchrone, et de manière optionnelle, le
nombre de lignes devant être mises dans la file d'attente par Dnsmasq lorsque
l'écriture vers le syslog est lente.
Dnsmasq peut envoyer ses logs de manière asynchrone : cela lui permet de
continuer à fonctionner sans être bloqué par le syslog, et permet à syslog
d'utiliser Dnsmasq pour les résolutions DNS sans risque d'interblocage.
Si la file d'attente devient pleine, Dnsmasq loggera le dépassement de file et
le nombre de messages perdus. La longueur par défaut de la file d'attente est de
5 et une valeur saine sera comprise entre 5 et 25, avec une limite maximum
imposée de 100.
.TP
.B \-x, --pid-file=<chemin>
Spécifie un fichier dans lequel stocker le numéro de processus (pid). La valeur
par défaut est /var/run/dnsmasq.pid.
.TP
.B \-u, --user=<nom d'utilisateur>
Spécifie l'identité (nom d'utilisateur) prise par Dnsmasq après le démarrage.
Dnsmasq doit normalement être démarré en temps que root ("super-utilisateur"),
mais abandonne ses privilèges après le démarrage en changeant d'identité.
Normalement cet utilisateur est l'utilisateur nobody ("personne"), mais il est
possible d'en définir un autre par le biais de ce paramètre.
.TP
.B \-g, --group=<nom de groupe> 
Spécifie le groupe sous lequel Dnsmasq s'exécute. Par défaut, il s'agit du
groupe "dip", afin de faciliter l'accès au fichier /etc/ppp/resolv.conf qui
n'est en général pas en lecture par tout le monde.
.TP
.B \-v, --version
Imprime le numéro de version.
.TP
.B \-p, --port=<port>
Ecoute sur le port numéro <port> au lieu du port DNS standard (53). Paramétrer
cette valeur à zéro désactive complètement la fonction DNS pour ne laisser actif
que le DHCP ou le TFTP.
.TP
.B \-P, --edns-packet-max=<taille>
Spécifie la taille maximum de paquet UDP EDNS.0 supporté par le relai DNS. Le
défaut est de 4096, qui est la valeur recommandée dans la RFC5625.
.TP
.B \-Q, --query-port=<numéro de port>
Envoie et écoute les requêtes DNS sortantes depuis le port UDP spécifié par
<numéro de port>, et non sur un port aléatoire. NOTE : Cette option rends
dnsmasq moins sûr contre les attaques par usurpation DNS ("DNS spoofing"), mais
cela peut permettre d'utiliser moins de ressources et d'être plus rapide. Donner
une valeur de zéro à cette option restaure le comportement par défaut présent dans
les versions de dnsmasq inférieures à 2.43 qui consiste à n'allouer qu'un seul port
alloué par le système d'exploitation.
.TP
.B --min-port=<port>
Ne pas utiliser de port dont le numéro est inférieur à la valeur donnée en paramètre
pour les requêtes DNS sortantes. Dnsmasq choisis un port source aléatoire pour les
requêtes sortantes : lorsque cette option est fournie, les ports utilisés seront toujours
au dessus de la valeur spécifiée. Utile pour des systèmes derrière des dispositifs
garde-barrières ("firewalls").
.TP
.B \-i, --interface=<nom d'interface>
N'écouter que sur l'interface réseau spécifiée. Dnsmasq ajoute automatiquement
l'interface locale ("loopback") à la liste des interfaces lorsque l'option
.B --interface
est utilisée.
Si aucune option
.B --interface
ou
.B --listen-address
n'est donnée, Dnsmasq écoutera sur toutes les interfaces disponibles sauf
celle(s) spécifiée(s) par l'option
.B --except-interface.
Les alias d'interfaces IP (par exemple "eth1:0") ne peuvent être utilisés ni avec
.B --interface
ni
.B \--except-interface.
Utiliser l'option 
.B --listen-address
à la place. Un simple joker, consistant en un '*' final, peut être utilisé dans
les options
.B \--interface 
et
.B \--except-interface
.TP
.B \-I, --except-interface=<interface name>
Ne pas écouter sur l'interface spécifiée. Notez que l'ordre dans lesquelles les
options
.B \--listen-address
,
.B --interface
et
.B --except-interface
sont fournies n'importe pas, et que l'option 
.B --except-interface
l'emporte toujours sur les autres.
.TP
.B --auth-server=<domaine>,<interface>|<adresse IP>
Active le mode DNS faisant autorité pour les requêtes arrivant sur cette
interface ou sur cette adresse. Noter que l'interface ou l'adresse n'ont
pas besoin d'être mentionnées ni dans
.B --interface
ni dans
.B --listen-address
En effet,
.B --auth-server
va passer outre ceux-ci et fournir un service DNS différent sur l'interface
spécifiée. La valeur de <domaine> est l'enregistrement de type "colle"
("glue record"). Il doit correspondre dans le service DNS global avec un
enregistrement de type A et/ou AAAA pointant sur l'adresse sur laquelle dnsmasq
écoute pour le mode DNS faisant autorité.
.TP 
.B \-2, --no-dhcp-interface=<nom d'interface>
Ne pas fournir de service DHCP sur l'interface spécifiée, mais fournir tout de
même le service DNS.
.TP
.B \-a, --listen-address=<adresse IP>
Ecouter sur la ou les adresse(s) IP spécifiée(s). Les options 
.B \--interface
et
.B \--listen-address
peuvent être spécifiées simultanément, auquel cas un jeu d'interfaces et
d'adresses seront utilisées. Notez que si
aucune option
.B \--interface
n'est donnée alors qu'une option 
.B \--listen-address
l'est, Dnsmasq n'écoutera pas automatiquement sur l'interface locale
("loopback"). Pour activer l'écoute sur l'interface locale, il est alors
nécessaire de fournir explicitement son adresse IP, 127.0.0.1 via l'option
.B \--listen-address.
.TP
.B \-z, --bind-interfaces
Sur les systèmes qui le supportent, Dnsmasq s'associe avec l'interface joker
("wildcard"), même lorsqu'il ne doit écouter que sur certaines interfaces. Par
la suite, il rejette les requêtes auxquelles il ne doit pas répondre. Cette
situation présente l'avantage de fonctionner même lorsque les interfaces vont
et viennent ou changent d'adresses. L'option 
.B --bind-interfaces
force Dnsmasq à ne réellement s'associer qu'avec les interfaces sur lesquelles
il doit écouter. L'un des seuls cas où cette option est utile est celui où un
autre serveur de nom (ou une autre instance de Dnsmasq) tourne sur la même
machine. Utiliser cette option permet également d'avoir plusieurs instances de
Dnsmasq fournissant un service DHCP sur la même machine.
.TP
.B --bind-dynamic
Autorise un mode réseau intermédiaire entre
.B --bind-interfaces
et le mode par défaut. Dnsmasq s'associe à une seule interface, ce qui permet
plusieurs instances de dnsmasq, mais si une interface ou adresse apparaissent,
il se mettra automatiquement à écouter sur celles-ci (les règles de contrôle
d'accès s'appliquent).
De fait, les interfaces créées dynamiquement fonctionnent de la même façon que
dans le comportement par défaut. Ce fonctionnement nécessite des APIs réseau
non standard et n'est disponible que sous Linux. Sur les autres plateformes,
le fonctionnement est celui du mode --bind-interfaces.
.TP
.B \-y, --localise-queries
Retourne des réponses aux requêtes DNS dépendantes de l'interface sur laquelle
la requête a été reçue, à partir du fichier /etc/hosts. Si un nom dans
/etc/hosts a plus d'une adresse associée avec lui, et qu'une des adresses au
moins est dans le même sous-réseau que l'interface sur laquelle la requête a été
reçue, alors ne retourne que la(les) adresse(s) du sous-réseau considéré. Cela
permet d'avoir dans /etc/hosts un serveur avec de multiples adresses, une pour
chacune de ses interfaces, et de fournir aux hôtes l'adresse correcte (basée sur
le réseau auquel ils sont attachés). Cette possibilité est actuellement limitée
à IPv4.
.TP
.B \-b, --bogus-priv
Fausse résolution inverse pour les réseaux privés. Toutes les requêtes DNS
inverses pour des adresses IP privées (192.168.x.x, etc...) qui ne sont pas
trouvées dans /etc/hosts ou dans le fichier de baux DHCP se voient retournées
une réponse "pas de tel domaine" ("no such domain") au lieu d'être transmises
aux serveurs de nom amont ("upstream server").
.TP
.B \-V, --alias=[<ancienne IP>]|[<IP de début>-<IP de fin>],<nouvelle IP>[,<masque>]
Modifie les adresses IPv4 retournées par les serveurs de nom amont;
<ancienne IP> est remplacée par <nouvelle IP>. Si le <masque> optionnel est
fourni, alors toute adresse correspondant à l'adresse <ancienne IP>/<masque>
sera réécrite. Ainsi par exemple
.B --alias=*******,*******,************* 
modifiera ******** en ******** et ******** en ********. 
Cette fonctionnalité correspond à ce que les routeurs Cisco PIX appellent
"bidouillage DNS" ("DNS doctoring"). Si l'ancienne IP est donnée sous la forme
d'une gamme d'adresses, alors seules les adresses dans cette gamme seront
réécrites, et non le sous-réseau dans son ensemble. Ainsi,
.B --alias=************-************,10.0.0.0,*************
fait correspondre ************->************ à *********->*********
.TP 
.B \-B, --bogus-nxdomain=<adresse IP>
Transforme les réponses contenant l'adresse IP fournie en réponses "pas de tel
domaine" ("no such domain"). Ceci a pour but de neutraliser la modification
sournoise mise en place par Verisign en septembre 2003, lorsqu'ils ont commencé
à retourner l'adresse d'un serveur web publicitaire en réponse aux requêtes pour
les noms de domaines non enregistrés, au lieu de la réponse correcte "NXDOMAIN".
Cette option demande à Dnsmasq de retourner la réponse correcte lorsqu'il
constate ce comportement. L'adresse retournée par Verisign en septembre 2003
est ************.
.TP
.B \-f, --filterwin2k
Les dernières versions de windows font des requêtes DNS périodiques auxquelles
non seulement les serveurs DNS publics ne peuvent donner de réponse, mais qui,
de surcroît, peuvent poser des problèmes en déclenchant des connexions
intempestives pour des liens réseaux avec des connexions "à la demande". Fournir
cette option active le filtrage des requêtes de ce type. Les requêtes bloquées
sont les requêtes pour les entrées de type SOA ou SRV, ainsi que les requêtes de
type ANY avec des noms possédant des caractères sous-lignés (requêtes pour des
serveurs LDAP).
.TP
.B \-r, --resolv-file=<fichier>
Lis les adresses des serveurs de nom amont dans le fichier de nom <fichier>,
au lieu du fichier /etc/resolv.conf. Pour le format de ce fichier, voir dans le
manuel pour
.BR resolv.conf (5) 
les entrées correspondant aux serveurs de noms (nameserver). Dnsmasq peut lire
plusieurs fichiers de type resolv.conf, le premier fichier spécifié remplace le
fichier par défaut, le contenu des suivants est rajouté dans la liste des
fichiers à consulter. Seul le fichier ayant la dernière date de modification
sera chargé en mémoire.
.TP
.B \-R, --no-resolv
Ne pas lire le contenu du fichier /etc/resolv.conf. N'obtenir l'adresse des
serveurs de nom amont que depuis la ligne de commande ou le fichier de
configuration de Dnsmasq.
.TP
.B \-1, --enable-dbus[=<nom de service>]
Autoriser la mise à jour de la configuration de Dnsmasq par le biais d'appel de
méthodes DBus. Il est possible par ce biais de mettre à jour l'adresse de
serveurs DNS amont (et les domaines correspondants) et de vider le cache. Cette
option nécessite que Dnsmasq soit compilé avec le support DBus. Si un nom de
service est fourni, dnsmasq fourni un service à ce nom, plutôt qu'avec la
valeur par défaut :
.B uk.org.thekelleys.dnsmasq
.TP 
.B \-o, --strict-order
Par défaut, Dnsmasq envoie les requêtes à n'importe lequel des serveurs amonts
dont il a connaissance tout en essayant de favoriser les serveurs qu'il sait
fonctionner. Cette option force Dnsmasq à essayer d'interroger, pour chaque
requête, les serveurs DNS dans leur ordre d'apparition dans le fichier
/etc/resolv.conf.
.TP
.B --all-servers
Par défaut, lorsque dnsmasq a plus d'un serveur amont disponible, il n'envoie
les requêtes qu'à un seul serveur. Spécifier cette option force dnsmasq à
effectuer ses requêtes à tous les serveurs disponibles. Le résultat renvoyé
au client sera celui fournit par le premier serveur ayant répondu.
.TP
.B --stop-dns-rebind
Rejette (et enregistre dans le journal d'activité) les adresses dans la gamme
d'adresses IP privée (au sens RFC1918) qui pourraient être renvoyées par les
serveurs amonts suite à une résolution de nom. Cela bloque les attaques cherchant
à détourner de leur usage les logiciels de navigation web ('browser') en s'en
servant pour découvrir les machines situées sur le réseau local.
.TP
.B --rebind-localhost-ok
Exclue 127.0.0/8 des vérifications de réassociation DNS. Cette gamme d'adresses
est retournée par les serveurs Realtime Blackhole (RBL, utilisés dans la
lutte contre le spam), la bloquer peut entraîner des dysfonctionnements de ces
services.
.TP 
.B  --rebind-domain-ok=[<domaine>]|[[/<domaine>/[<domaine>/]
Ne pas détecter ni bloquer les actions de type dns-rebind pour ces domaines.
Cette option peut prendre comme valeur soit un nom de domaine soit plusieurs
noms de domaine entourés par des '/', selon une syntaxe similaire à l'option
--server, c-à-d :
.B  --rebind-domain-ok=/domaine1/domaine2/domaine3/
.TP
.B \-n, --no-poll
Ne pas vérifier régulièrement si le fichier /etc/resolv.conf a été modifié.
.TP
.B --clear-on-reload
Lorsque le fichier /etc/resolv.conf est relu, ou si les serveurs amonts sont
configurés via DBus, vider le cache DNS.
Cela est utile si les nouveaux serveurs sont susceptibles d'avoir des données
différentes de celles stockées dans le cache.
.TP
.B \-D, --domain-needed
Indique à Dnsmasq de ne jamais transmettre en amont de requêtes A ou AAAA pour
des noms simples, c'est à dire ne comprenant ni points ni nom de domaine. Si un
nom n'est pas dans /etc/hosts ou dans la liste des baux DHCP, alors une réponse
de type "non trouvé" est renvoyée.
.TP
.B \-S, --local, --server=[/[<domaine>]/[domaine/]][<Adresse IP>[#<port>][@<Adresse IP source>|<interface>[#<port>]]]
Spécifie directement l'adresse IP d'un serveur de nom amont. Cette option ne
supprime pas la lecture du fichier /etc/resolv.conf : utiliser pour cela
l'option
.B -R .
Si un ou plusieurs nom(s) de domaine(s) optionnel(s) sont fournis, ce
serveur sera uniquement utilisé uniquement pour ce(s) domaine(s), et toute
requête concernant ce(s) domaine(s) sera adressée uniquement à ce serveur.
Cette option est destinée aux serveurs de nom privés : si vous avez un serveur
de nom sur votre réseau ayant pour adresse IP *********** et effectuant la
résolution des noms de la forme xxx.internal.thekelleys.org.uk, alors
.B -S /internal.thekelleys.org.uk/*********** 
enverra toutes les requêtes pour les machines internes vers ce serveur de nom,
alors que toutes les autres requêtes seront adressées aux serveurs indiqués dans
le fichier /etc/resolv.conf. Une spécification de nom de domaine vide,
.B // 
possède le sens particulier de "pour les noms non qualifiés uniquement",
c'est-à-dire les noms ne possédant pas de points. Un port non standard peut être
rajouté à la suite des adresses IP en utilisant le caractère #. Plus d'une
option
.B -S
est autorisée, en répétant les domaines et adresses IP comme requis.

Le domaine le plus spécifique l'emporte sur le domaine le moins spécifique,
ainsi :
.B --server=/google.com/*******
.B --server=/www.google.com/*******
enverra les requêtes pour *.google.com à *******, à l'exception des requêtes
*www.google.com, qui seront envoyées à *******.

L'adresse spéciale '#' signifie "utiliser les serveurs standards", ainsi
.B --server=/google.com/*******
.B --server=/www.google.com/#
enverra les requêtes pour *.google.com à *******, à l'exception des requêtes
pour *www.google.com qui seront envoyées comme d'habitude (c-à-d aux serveurs
définis par défaut).

Il est également permis de donner une option
.B -S
avec un nom de domaine mais sans
adresse IP; Cela informe Dnsmasq que le domaine est local et qu'il doit répondre
aux requêtes le concernant depuis les entrées contenues dans le fichier
/etc/hosts ou les baux DHCP, et ne doit en aucun cas transmettre les requêtes
aux serveurs amonts.
.B local
est synonyme de
.B server
("serveur") afin de rendre plus claire l'utilisation de cette option pour cet
usage particulier.

Les adresses IPv6 peuvent inclure un identifiant de zone sous la forme
%interface tel que par exemple
fe80::202:a412:4512:7bbf%eth0.

La chaîne de caractères optionnelle suivant le caractère @ permet de définir
la source que Dnsmasq doit utiliser pour les réponses à ce
serveur de nom. Il doit s'agir d'une des adresses IP appartenant à la machine sur
laquelle tourne Dnsmasq ou sinon la ligne sera ignorée et une erreur sera
consignée dans le journal des événements, ou alors d'un nom d'interface. Si un nom
d'interface est donné, alors les requêtes vers le serveur de nom seront envoyées
depuis cette interface; si une adresse IP est donnée, alors l'adresse source de
la requête sera l'adresse en question. L'option query-port est ignorée pour tous
les serveurs ayant une adresse source spécifiée, mais il est possible de la donner
directement dans la spécification de l'adresse source. Forcer les requêtes à être
émises depuis une interface spécifique n'est pas possible sur toutes les plateformes
supportées par dnsmasq.
.TP
.B \-A, --address=/<domaine>/[domaine/]<adresse IP>
Spécifie une adresse IP à retourner pour toute requête pour les domaines fournis
en option. Les requêtes pour ce(s) domaine(s) ne sont jamais transmises aux
serveurs amonts et reçoivent comme réponse l'adresse IP spécifiée qui peut être
une adresse IPv4 ou IPv6. Pour donner à la fois une adresse IPv4 et une adresse
IPv6 pour un domaine, utiliser plusieurs options
.B -A.
Il faut noter que le
contenu du fichier /etc/hosts et de celui des baux DHCP supplante ceci pour des
noms individuels. Une utilisation courante de cette option est de rediriger la
totalité du domaine doubleclick.net vers un serveur web local afin d'éviter les
bannières publicitaires. La spécification de domaine fonctionne de la même façon
que
.B  --server,
avec la caractéristique supplémentaire que
.B /#/
coïncide avec tout domaine. Ainsi,
.B --address=/#/*******
retournera ******* pour toute requête
n'ayant de réponse ni dans /etc/hosts, ni dans les baux DHCP, et n'étant pas
transmise à un serveur spécifique par le biais d'une directive
.B --server.
.TP
.B --ipset=/<domaine>/[domaine/]<ipset>[,<ipset>]
Obtient les adresses IP des domaines spécifiés et les place dans les groupes
d'IP netfilter (ipset) indiqués. Domaines et sous-domaines sont résolus de la
même façon que pour --address. Ces groupes d'IP doivent déjà exister. Voir
ipset(8) pour plus de détails.
.TP
.B \-m, --mx-host=<nom de l'hôte>[[,<nom du MX>],<préférence>]
Spécifie un enregistrement de type MX pour <nom de l'hôte> retournant le nom
donné dans <nom du MX> (s'il est présent), ou sinon le nom spécifié dans
l'option
.B --mx-target
si elle est présente. Sinon retourne le nom de la machine
sur laquelle Dnsmasq tourne. La valeur par défaut (spécifiée dans l'option
.B --mx-target
) est utile dans un réseau local pour rediriger les courriers
électroniques vers un serveur central. La valeur de préférence est optionnelle
et vaut par défaut 1 si elle n'est pas spécifiée. Plus d'une entrée MX peut être
fournie pour un hôte donné.
.TP 
.B \-t, --mx-target=<nom d'hôte>
Spécifie la réponse par défaut fournie par Dnsmasq pour les requêtes sur des
enregistrements de type MX. Voir
.B --mx-host.
Si
.B --mx-target
est donné mais pas de
.B --mx-host,
alors Dnsmasq retourne comme réponse un enregistrement MX
contenant le nom d'hôte spécifié dans l'option
.B --mx-target
pour toute requête
concernant le MX de la machine sur laquelle tourne Dnsmasq.
.TP
.B \-e, --selfmx
Définit, pour toutes les machines locales, un MX correspondant à l'hôte
considéré. Les machines locales sont celles définies dans le fichier /etc/hosts
ou dans un bail DHCP.
.TP 
.B \-L, --localmx
Définit, pour toutes les machines locales, un enregistrement MX pointant sur
l'hôte spécifié par mx-target (ou la machine sur laquelle Dnsmasq tourne). Les
machines locales sont celles définies dans le fichier /etc/hosts ou dans un bail
DHCP.
.TP
.B \-W --srv-host=<_service>.<_protocole>.[<domaine>],[<cible>[,<port>[,<priorité>[,<poids>]]]]
Spécifie un enregistrement DNS de type SRV. Voir la RFC2782 pour plus de
détails. Si le champs <domaine> n'est pas fourni, prends par défaut la valeur
fournie dans l'option
.B --domain.
La valeur par défaut pour le domaine est vide et le port par défaut est 1, alors
que les poids et priorités par défaut sont 0. Attention lorsque vous transposez
des valeurs issues d'une configuration BIND : les ports, poids et priorités sont
dans un ordre différents. Pour un service/domaine donné, plus d'un
enregistrement SRV est autorisé et tous les enregistrements qui coïncident sont
retournés dans la réponse.
.TP
.B --host-record=<nom>[,<nom>....][<adresse IPv4>],[<adresse IPv6>]
Ajoute des enregistrements A, AAAA et PTR dans le DNS. Ceci permet d'ajouter
un ou plusieurs noms dans le DNS et de les associer à des enregistrements IPv4
(A) ou IPv6 (AAAA). Un nom peut apparaître dans plus d'une entrée
.B host-record
et de fait être associé à plus d'une adresse. Seule la première entrée créée
l'enregistrement PTR associée au nom. Ceci correspond à la même règle que celle
utilisée lors de la lecture du fichier hosts.
Les options
.B host-record
sont considérées lues avant le fichier hosts, ainsi un nom apparaissant dans
une option host-record et dans le fichier hosts n'aura pas d'enregistrement
PTR associé à l'entrée dans le fichier hosts. A l'inverse du fichier hosts, les
noms ne sont pas étendus, même lorsque l'option
.B expand-hosts
est activée. Les noms longs et les noms courts peuvent apparaitre dans la même
entrée 
.B host-record,
c-à-d
.B --host-record=laptop,laptop.thekelleys.org,***********,1234::100
.TP
.B \-Y, --txt-record=<nom>[[,<texte>],<texte>]
Définit un enregistrement DNS de type TXT. La valeur de l'enregistrement TXT est
un ensemble de chaînes de caractères, donc un nombre variable de chaînes de
caractères peuvent être spécifiées, séparées par des virgules. Utilisez des
guillemets pour mettre une virgule dans une chaîne de caractères. Notez que la
longueur maximale pour une chaîne est de 255 caractères, les chaînes plus
longues étant découpées en morceaux de 255 caractères de longs.
.TP
.B --ptr-record=<nom>[,<cible>]
Définit un enregistrement DNS de type PTR.
.TP
.B --naptr-record=<nom>,<ordre>,<préférence>,<drapeaux>,<service>,<expr. régulière>[,<remplacement>]
Retourne un enregistrement de type NAPTR, tel que spécifié dans le RFC3403.
.TP
.B --cname=<cname>,<cible>
Retourne un enregistrement de type CNAME qui indique que <cname> est en
réalité <cible>. Il existe des contraintes importantes sur la valeur
cible; il doit s'agir d'un nom DNS qui est connu de dnsmasq via /etc/hosts
(ou un fichier hôtes additionnel), via DHCP, via interface--name ou par un autre
.B --cname.
Si une cible ne satisfait pas ces critères, le CNAME est ignoré. Le CNAME
doit être unique, mais il est autorisé d'avoir plus d'un CNAME pointant
vers la même cible.
.TP
.B --dns-rr=<nom>,<numéro-RR>,[<données hexadécimales>]
Retourne un enregistrement DNS arbitraire. Le numéro correspond au type
d'enregistrement (qui est toujours de la classe C_IN). La valeur de
l'enregistrement est donnée dans les données hexadécimales, qui peuvent
être de la forme 01:23:45, 01 23 45,+012345 ou n'importe quelle combinaison.
.TP
.B --interface-name=<nom>,<interface>
Définit un enregistrement DNS associant le nom avec l'adresse primaire sur
l'interface donnée en argument. Cette option spécifie un enregistrement de type
A pour le nom donné en argument de la même façon que s'il était défini par une
ligne de /etc/hosts, sauf que l'adresse n'est pas constante mais dépendante de
l'interface définie. Si l'interface est inactive, non existante ou non
configurée, une réponse vide est fournie. Un enregistrement inverse (PTR) est
également créé par cette option, associant l'adresse de l'interface avec le nom.
Plus d'un nom peut être associé à une interface donnée en répétant cette option
plusieurs fois; dans ce cas, l'enregistrement inverse pointe vers le nom fourni
dans la première instance de cette option.
.TP
.B --synth-domain=<domaine>,<plage d'adresses>[,<préfixe>]
Créé des enregistrements A/AAAA ou PTR pour une plage d'adresses. Les
enregistrements utilisent l'adresse ainsi que les points (ou les deux points
dans le cas d'IPv6) remplacés par des tirets.

Un exemple devrait rendre cela plus clair :
La configuration
.B --synth-domain=thekelleys.org.uk,***********/24,internal-
permet de retourner internal-192-168-0-56.thekelleys.org.uk lors d'une requête
sur l'adresse ************ et vice-versa pour la requête inverse. La même
logique s'applique pour IPv6, avec la particularité suivante : les adresses
IPv6 pouvant commencer par '::', mais les noms DNS ne pouvant pas commencer
par '-', si aucun préfixe n'est donné, un zéro est ajouté en début de nom.
Ainsi, ::1 devient 0--1.

La plage d'adresses peut être de la forme
<adresse IP>,<adresse IP> ou <adresse IP>/<masque réseau>
.TP
.B --add-mac
Ajoute l'adresse MAC du requêteur aux requêtes DNS transmises aux serveurs
amonts. Cela peut être utilisé dans un but de filtrage DNS par les serveurs
amonts. L'adresse MAC peut uniquement être ajoutée si le requêteur est sur le
même sous-réseau que le serveur dnsmasq. Veuillez noter que le mécanisme
utilisé pour effectuer cela (une option EDNS0) n'est pas encore standardisée,
aussi cette fonctionnalité doit être considérée comme expérimentale. Notez
également qu'exposer les adresses MAC de la sorte peut avoir des implications
en termes de sécurité et de vie privée. L'avertissement donné pour --add-subnet
s'applique également ici.
.TP
.B --add-subnet[[=<longueur de préfixe IPv4>],<longueur de préfixe IPv6>]
Rajoute l'adresse de sous-réseau du requêteur aux requêtes DNS transmises
aux serveurs amonts. La quantité d'adresses transmises dépend du paramètre
longueur du préfixe : 32 (ou 128 dans le cas d'IPv6) transmet la totalité
de l'adresse, 0 n'en transmet aucun mais marque néanmoins la requête ce qui
fait qu'aucun serveur amont ne rajoutera d'adresse client. La valeur par
défaut est zéro et pour IPv4 et pour IPv6. A noter que les serveurs amonts
peuvent être configurés pour retourner des valeurs différentes en fonction
de cette information mais que le cache de dnsmasq n'en tient pas compte.
Si une instance de dnsmasq est configurée de telle manière que des valeurs
différentes pourraient être rencontrées, alors le cache devrait être désactivé.
.TP
.B \-c, --cache-size=<taille>
Définit la taille du cache de Dnsmasq. La valeur par défaut est de 150 noms.
Définir une valeur de zéro désactive le cache. Remarque: la taille importante
du cache a un impact sur les performances.
.TP
.B \-N, --no-negcache
Désactive le "cache négatif". Le "cache négatif" permet à Dnsmasq de se souvenir
des réponses de type "no such domain" fournies par les serveurs DNS en amont et
de fournir les réponses sans avoir à retransmettre les requêtes aux serveurs
amont.
.TP
.B \-0, --dns-forward-max=<nombre de requêtes>
Définit le nombre maximum de requêtes DNS simultanées. La valeur par défaut est
150, ce qui devrait être suffisant dans la majorité des configurations. La seule
situation identifiée dans laquelle cette valeur nécessite d'être augmentée est
lorsqu'un serveur web a la résolution de nom activée pour l'enregistrement de
son journal des requêtes, ce qui peut générer un nombre important de requêtes
simultanées.
.TP
.B --proxy-dnssec
Un résolveur sur une machine cliente peut effectuer la validation DNSSEC de
deux façons : il peut effectuer lui-même les opérations de chiffrements sur
la réponse reçue, ou il peut laisser le serveur récursif amont faire la
validation et positionner un drapeau dans la réponse au cas où celle-ci est
correcte. Dnsmasq n'est pas un validateur DNSSEC, aussi il ne peut effectuer
la validation comme un serveur de nom récursif, cependant il peut retransmettre
les résultats de validation de ses serveurs amonts. Cette option permet
l'activation de cette fonctionnalité. Vous ne devriez utiliser cela que si vous
faites confiance aux serveurs amonts
.I ainsi que le réseau entre vous et eux.
Si vous utilisez le premier mode DNSSEC, la validation par le résolveur des
clients, cette option n'est pas requise. Dnsmasq retourne toujours toutes les
données nécessaires par un client pour effectuer la validation lui-même.
.TP

.B --auth-zone=<domaine>[,<sous-réseau>[/<longueur de préfixe>][,<sous-réseau>[/<longueur de préfixe>].....]]
Définie une zone DNS pour laquelle dnsmasq agit en temps que serveur faisant
autorité. Les enregistrements DNS définis localement et correspondant à ce
domaine seront fournis. Les enregistrements A et AAAA doivent se situer dans
l'un des sous-réseaux définis, ou dans un réseau correspondant à une plage DHCP
(ce comportement peut être désactivé par
.B constructor-noauth:
). Le ou les sous-réseaux sont également utilisé(s) pour définir les domaines
in-addr.arpa et ip6.arpa servant à l'interrogation DNS inverse. Si la longueur
de préfixe n'est pas spécifiée, elle sera par défaut de 24 pour IPv4 et 64 pour
IPv6. Dans le cas d'IPv4, la longueur du masque de réseau devrait être de 8, 16
ou 24, sauf si en cas de mise en place d'une délégation de la zone in-addr.arpa
conforme au RFC 2317.
.TP
.B --auth-soa=<numéro de série>[,<mainteneur de zone (hostmaster)>[,<rafraichissement>[,<nombre de réessais>[,<expiration>]]]]
Spécifie les champs de l'enregistrement de type SOA (Start Of Authority)
associé à une zone pour laquelle le serveur fait autorité. A noter que cela est
optionnel, les valeurs par défaut devant convenir à la majorité des cas.
.TP
.B --auth-sec-servers=<domaine>[,<domaine>[,<domaine>...]]
Spécifie un ou plusieurs serveur de nom secondaires pour une zone pour
laquelle dnsmasq fait autorité. Ces serveurs doivent être configurés pour
récupérer auprès de dnsmasq les informations liées à la zone au travers d'un
transfert de zone, et répondre aux requêtes pour toutes les zones pour
lesquelles dnsmasq fait autorité.
.TP
.B --auth-peer=<adresse IP>[,<adresse IP>[,<adresse IP>...]]
Spécifie la ou les adresses de serveurs secondaires autorisés à initier des
requêtes de transfert de zone (AXFR) pour les zones pour lesquelles
dnsmasq fait autorité. Si cette option n'est pas fournie, les requêtes AXFR
seront acceptées pour tous les serveurs secondaires.
.TP 
.B --conntrack
Lis le marquage de suivi de connexion Linux associé aux requêtes DNS entrantes
et positionne la même marque au trafic amont utilisé pour répondre à ces
requêtes. Cela permet au trafic généré par Dnsmasq d'être associé aux requêtes
l'ayant déclenché, ce qui est pratique pour la gestion de la bande passante
(accounting) et le filtrage (firewall). Dnsmasq doit pour cela être compilé
avec le support conntrack, le noyau doit également inclure conntrack et être
configuré pour cela. Cette option ne peut pas être combinée avec
--query-port.
.TP
.B \-F, --dhcp-range=[tag:<label>[,tag:<label>],][set:<label>],]<adresse de début>[,<adresse de fin>][,<mode>][,<masque de réseau>[,<broadcast>]][,<durée de bail>]
.TP
.B \-F, --dhcp-range=[tag:<label>[,tag:<label>],][set:<label>],]<adresse IPv6 de début>[,<adresse IPv6 de fin>|constructor:<interface>][,<mode>][,<longueur de préfixe>][,<durée de bail>]

Active le serveur DHCP. Les adresses seront données dans la plage comprise entre
<adresse de début> et <adresse de fin> et à partir des adresses définies
statiquement dans l'option
.B dhcp-host.
Si une durée de bail est donnée, alors les baux seront donnés pour cette
durée. La durée de bail est donnée en secondes, en minutes (exemple : 45m),
en heures (exemple : 1h) ou être la chaine de caractère "infinite" pour une
durée indéterminée. Si aucune valeur n'est donnée, une durée de bail par défaut
de une heure est appliquée. La valeur minimum pour un bail DHCP est de 2
minutes.

Pour les plages IPv6, la durée de bail peut être égale au mot-clef "deprecated"
(obsolète); Cela positionne la durée de vie préférée envoyée dans les baux DHCP
ou les annonces routeurs à zéro, ce qui incite les clients à utiliser d'autres
adresses autant que possible, pour toute nouvelle connexion, en préalable à
la renumérotation.

Cette option peut être répétée, avec différentes adresses,
pour activer le service DHCP sur plus d'un réseau. Pour des réseaux directement
connectés (c'est-à-dire des réseaux dans lesquels la machine sur laquelle tourne
Dnsmasq possède une interface), le masque de réseau est optionnel : Dnsmasq la
déterminera à partir de la configuration des interfaces.

Pour les réseaux pour lesquels le service DHCP se fait via un relais DHCP
("relay agent"), Dnsmasq est incapable de déterminer le masque par lui-même,
aussi il doit être spécifié, faute de quoi Dnsmasq essaiera de le deviner en
fonction de la classe (A, B ou C) de l'adresse réseau. L'adresse de broadcast
est toujours optionnelle.

Il est toujours possible d'avoir plus d'une plage DHCP pour un même
sous-réseau.

Pour IPv6, les paramètres sont légèrement différents : au lieu d'un masque de
réseau et d'une adresse de broadcast, il existe une longueur de préfixe
optionnelle. Si elle est omise, la valeur par défaut est 64. À la différence
d'IPv4, la longueur de préfixe n'est pas automatiquement déduite de la
configuration de l'interface. La taille minimale pour la longueur de préfixe
est 64.

Pour IPv6 (et IPv6 uniquement), il est possible de définir les plages d'une
autre façon. Dans ce cas, l'adresse de départ et l'adresse de fin optionnelle
contiennent uniquement la partie réseau (par exemple ::1) et sont suivies par
.B constructor:<interface>.
Cela forme un modèle décrivant comment construire la plage, à partir des
adresses assignées à l'interface. Par exemple

.B --dhcp-range=::1,::400,constructor:eth0

provoque la recherche d'adresses de la forme <réseau>::1 sur eth0 et crée une
plage allant de <réseau>::1 à <réseau>:400. Si une interface est assignée à
plus d'un réseau, les plages correspondantes seront automatiquement créées,
rendues obsolètes puis supprimées lorsque l'adresse est rendue obsolète puis
supprimée. Le nom de l'interface peut être spécifié avec un caractère joker '*'
final.

provoque la recherche d'adresses sur eth0 et crée une plage allant de
<réseau>::1 à <réseau>:400. Si l'interface est assignée à
plus d'un réseau, les plages correspondantes seront respectivement
automatiquement créées, rendues obsolètes et supprimées lorsque l'adresse
est rendue obsolète et supprimée. Le nom de l'interface peut être spécifié avec
un caractère joker '*' final. Les adresses autoconfigurées, privées ou
obsolètes ne conviennent pas.

Si une plage dhcp-range est uniquement utilisée pour du DHCP sans-état
("stateless") ou de l'autoconfiguration sans état ("SLAAC"), alors l'adresse
peut être indiquée sous la forme '::'

.B --dhcp-range=::,constructor:eth0

Il existe une variante de la syntaxe constructor: qui consiste en l'utilisation
du mot-clef
.B constructor-noauth.
Voir
.B --auth-zone
pour des explications à ce sujet.

L'identifiant de label optionnel
.B set:<label>
fournie une étiquette alphanumérique qui identifie ce réseau, afin de permettre
la fourniture d'options DHCP spécifiques à chaque réseau.
Lorsque préfixé par 'tag:', la signification change, et au lieu de définir un
label, il définit le label pour laquelle la règle s'applique. Un seul label peut-
être défini mais plusieurs labels peuvent coïncider.

Le mot clef optionnel <mode> peut être égal à
.B static
("statique") ce qui indique à Dnsmasq d'activer le service DHCP pour le réseau
spécifié, mais de ne pas activer l'allocation dynamique d'adresses IP : Seuls
les hôtes possédant des adresses IP statiques fournies via 
.B dhcp-host
ou présentes dans le fichier /etc/ethers seront alors servis par le DHCP. Il est
possible d'activer un mode "fourre-tout" en définissant un réseau statique
comportant uniquement des zéros, c'est à dire :
.B --dhcp=range=::,static
Cela permet de retourner des réponses à tous les paquets de type
Information-request (requête d'information) en mode DHCPv6 sans état sur le
sous-réseau configuré. 

Pour IPv4, le <mode> peut est égal à
.B proxy
, auquel cas Dnsmasq fournira un service de DHCP proxy pour le sous-réseau
spécifié. (voir
.B pxe-prompt
et
.B pxe-service
pour plus de détails).

Pour IPv6, le mode peut être une combinaison des valeurs
.B ra-only, slaac, ra-names, ra-stateless, off-link.

.B ra-only
indique à dnsmasq de n'effectuer que des annonces de routeur (Router
Advertisement, RA) sur ce sous-réseau, et de ne pas faire de DHCP.

.B slaac
indique à dnsmasq d'effectuer des annonces de routeur sur ce sous-réseau
et de positionner dans celles-ci le bit A, afin que les clients utilisent
des adresses SLAAC. Lorsqu'utilisé conjointement avec une plage DHCP ou des
affectations statiques d'adresses DHCP, les clients disposeront à la fois
d'adresses DHCP assignées et d'adresses SLAAC.

.B ra-stateless
indique à dnsmasq d'effectuer des annonces de routeur avec les bits 0 et A
positionnés, et de fournir un service DHCP sans état ("stateless"). Les clients
utiliseront des adresses SLAAC, et utiliseront DHCP pour toutes les autres
informations de configuration.

.B ra-names
active un mode qui fourni des noms DNS aux hôtes fonctionnant en double pile
("dual stack") et configurés pour faire du SLAAC en IPv6. Dnsmasq utilise le
bail IPv4 de l'hôte afin de dériver le nom, le segment de réseau et l'adresse
MAC et assume que l'hôte disposera d'une adresse IPv6 calculée via l'algorithme
SLAAC, sur le même segment de réseau. Un ping est envoyé à l'adresse, et si une
réponse est obtenue, un enregistrement AAAA est rajouté dans le DNS pour cette
adresse IPv6. Veuillez-noter que cela n'arrive que pour les réseaux directement
connectés (et non ceux pour lesquels DHCP se fait via relai), et ne
fonctionnera pas si un hôte utilise les "extensions de vie privée"
("privacy extensions").
.B ra-names
peut être combiné avec
.B ra-stateless
et
.B slaac.

.B off-link
indique à dnsmasq d'annoncer le préfixe sans le bit L (sur lien).

.TP
.B \-G, --dhcp-host=[<adresse matérielle>][,id:<identifiant client>|*][,set:<label>][,<adresse IP>][,<nom d'hôte>][,<durée de bail>][,ignore]
Spécifie les paramètres DHCP relatifs à un hôte. Cela permet à une machine
possédant une adresse matérielle spécifique de se voir toujours allouée les
mêmes nom d'hôte, adresse IP et durée de bail. Un nom d'hôte spécifié comme
ceci remplace le nom fourni par le client DHCP de la machine hôte. Il est
également possible d'omettre l'adresse matérielle et d'inclure le nom d'hôte,
auquel cas l'adresse IP et la durée de bail s'appliqueront à toute machine se
réclamant de ce nom. Par exemple
.B --dhcp-host=00:20:e0:3b:13:af,wap,infinite 
spécifie à Dnsmasq de fournir à la machine d'adresse matérielle
00:20:e0:3b:13:af le nom, et un bail de durée indéterminée.

.B --dhcp-host=lap,************* 
spécifie à Dnsmasq d'allouer toujours à la machine portant le nom lap
l'adresse IP *************.

Les adresses allouées de la sorte ne sont pas contraintes à une plage d'adresse
spécifiée par une option --dhcp-range, mais elles se trouver dans le même
sous-réseau qu'une plage dhcp-range valide. Pour les sous-réseaux qui n'ont pas
besoin d'adresses dynamiquement allouées, utiliser le mot-clef "static" dans la
déclaration de plage d'adresses dhcp-range.

Il est possible d'utiliser des identifiants clients (appelés "DUID client" dans
le monde IPv6) plutôt que des adresses matérielles pour identifier les hôtes,
en préfixant ceux-ci par 'id:'. Ainsi, 
.B --dhcp-host=id:01:02:03:04,..... 
réfère à l'hôte d'identifiant 01:02:03:04. Il est également possible de
spécifier l'identifiant client sous la forme d'une chaîne de caractères, comme
ceci :
.B --dhcp-host=id:identifiantclientsousformedechaine,..... 

Un seul
.B dhcp-host 
peut contenir une adresse IPv4, une adresse IPv6, ou les deux en même temps.
Les adresses IPv6 doivent être mises entre crochets comme suit :
.B --dhcp-host=laptop,[1234::56]
Les adresses IPv6 peuvent ne contenir que la partie identifiant de client :
.B --dhcp-host=laptop,[::56]
Dans ce cas, lorsque des plages dhcp sont définies automatiquement par le biais
de constructeurs, la partie réseau correspondante est rajoutée à l'adresse.

A noter que pour le DHCP IPv6, l'adresse matérielle n'est pas toujours
disponible, bien que ce soit toujours le cas pour des clients directement
connectés (sur le même domaine de broadcast) ou pour des clients utilisant
des relais DHCP qui supportent la RFC 6939.

En DHCPv4, l'option spéciale id:* signifie : "ignorer tout identifiant client et n'utiliser
que l'adresse matérielle". Cela est utile lorsqu'un client présente un
identifiant client mais pas les autres.

Si un nom apparaît dans /etc/hosts, l'adresse associée peut être allouée à un
bail DHCP mais seulement si une option
.B --dhcp-host
spécifiant le nom existe par ailleurs. Seul un nom d'hôte peut être donné dans
une option
.B dhcp-host
, mais les alias sont possibles au travers de l'utilisation des CNAMEs. (Voir
.B --cname
).
Le mot clef "ignore" ("ignorer") indique
à Dnsmasq de ne jamais fournir de bail DHCP à une machine. La machine peut être
spécifiée par son adresse matérielle, son identifiant client ou son nom d'hôte.
Par exemple
.B --dhcp-host=00:20:e0:3b:13:af,ignore
Cela est utile lorsqu'un autre serveur DHCP sur le réseau doit être utilisé par
certaines machines.

Le paramètre set:<identifiant réseau> permet de définir un
identifiant de réseau lorsque l'option dhcp-host est utilisée. Cela peut servir
à sélectionner des options DHCP juste pour cet hôte. Plus d'un label peut être
fourni dans une directive dhcp-host (et dans cette seule directive). Lorsqu'une
machine coïncide avec une directive dhcp-host (ou une impliquée par
/etc/ethers), alors le label réservé "known" ("connu") est associé. Cela permet à
Dnsmasq d'être configuré pour ignorer les requêtes issus de machines inconnue
 par le biais de 
.B --dhcp-ignore=tag:!known.

Les adresses ethernet (mais pas les identifiants clients) peuvent être définies
avec des octets joker, ainsi par exemple
.B --dhcp-host=00:20:e0:3b:13:*,ignore 
demande à Dnsmasq d'ignorer une gamme d'adresses matérielles. Il est  à noter
que "*" doit être précédé d'un caractère d'échappement ou mis entre guillemets
lorsque spécifié en option de ligne de commande, mais pas dans le fichier de
configuration.

Les adresses matérielles coïncident en principe avec n'importe
quel type de réseau (ARP), mais il est possible de les limiter à un seul type
ARP en les précédant du type ARP (en Hexadécimal) et de "-". Ainsi
.B --dhcp-host=06-00:20:e0:3b:13:af,******* 
coïncidera uniquement avec des adresses matérielles Token-Ring, puisque le type
ARP pour une adresse Token-Ring est 6.

Un cas spécial, pour IPv4, correspond à l'inclusion d'une ou plusieurs adresses
matérielles, c-à-d :
.B --dhcp-host=11:22:33:44:55:66,12:34:56:78:90:12,***********.
Cela permet à une adresse IP d'être associé à plusieurs adresses
matérielles, et donne à dnsmasq la permission d'abandonner un bail DHCP
attribué à l'une de ces adresses lorsqu'une autre adresse dans la liste
demande un bail. Ceci est une opération dangereuse qui ne fonctionnera
de manière fiable que si une adresse matérielle est active à un moment
donné et dnsmasq n'a aucun moyen de s'assurer de cela. Cela est utile,
par exemple, pour allouer une adresse IP stable à un laptop qui
aurait à la fois une connexion filaire et sans-fil.
.TP
.B --dhcp-hostsfile=<chemin>
Lis les informations d'hôtes DHCP dans le fichier spécifié. Si l'argument est
un chemin vers un répertoire, lis tous les fichiers de ce répertoire. Le
fichier contient des informations à raison d'un hôte par ligne. Le format
d'une ligne est la même que le texte fourni à la droite sur caractère "=" dans
l'option
.B --dhcp-host.
L'avantage de stocker les informations sur les hôtes DHCP dans ce fichier est
que celles-ci peuvent être modifiées sans recharger Dnsmasq; le fichier sera
relu lorsque Dnsmasq reçoit un signal SIGHUP.
.TP
.B --dhcp-optsfile=<chemin>
Lis les informations relatives aux options DHCP dans le fichier spécifié. Si
l'argument est un chemin vers un répertoire, lis tous les fichiers de ce
répertoire. L'intérêt d'utiliser cette option est le même que pour
 --dhcp-hostsfile : le fichier spécifié sera rechargé à la réception par
dnsmasq d'un signal SIGHUP. Notez qu'il est possible d'encoder l'information
via
.B --dhcp-boot
en utilisant les noms optionnels bootfile-name, server-ip-address et
tftp-server. Ceci permet d'inclure ces options dans un fichier "dhcp-optsfile".DNSMASQ_SUPPLIED_HOSTNAME
.TP
.B \-Z, --read-ethers
Lis les informations d'hôtes DHCP dans le fichier /etc/ethers. Le format de
/etc/ethers est une adresse matérielle suivie, soit par un nom d'hôte, soit par
une adresse IP sous la forme de 4 chiffres séparés par des points. Lorsque lu
par Dnsmasq, ces lignes ont exactement le même effet que l'option
.B --dhcp-host
contenant les mêmes informations. /etc/ethers est relu à la réception d'un
signal SIGHUP par Dnsmasq. Les adresses IPv6 ne sont PAS lues dans /etc/ethers.
.TP
.B \-O, --dhcp-option=[tag:<label>,[tag:<label>]][encap:<option>,][vi-encap:<entreprise>,][vendor:[<classe_vendeur>],][<option>|option:<nom d'option>|option6:<option>|option6:<nom d'option>],[<valeur>[,<valeur>]]
Spécifie des options différentes ou supplémentaires pour des clients DHCP. Par
défaut, Dnsmasq envoie un ensemble standard d'options aux clients DHCP : le
masque de réseau et l'adresse de broadcast sont les mêmes que pour l'hôte
sur lequel tourne Dnsmasq, et le serveur DNS ainsi que la route par défaut
prennent comme valeur l'adresse de la machine sur laquelle tourne Dnsmasq. 
(Des règles équivalentes s'appliquent en IPv6). Si une option de nom de domaine
a été définie, son contenu est transmis. Cette option de configuration permet
de changer toutes ces valeurs par défaut, ou de spécifier d'autres options.
L'option DHCP à transmettre peut être fournie sous forme d'un nombre décimal 
ou sous la forme "option:<nom d'option>". Les nombres correspondants aux options
sont définis dans la RFC2132 et suivants. Les noms d'options connus par Dnsmasq 
peuvent être obtenus via "Dnsmasq --help dhcp". Par exemple, pour définir la
route par défaut à ***********, il est possible de faire
.B --dhcp-option=3,*********** 
ou
.B --dhcp-option = option:router, ***********
ou encore, pour positionner l'adresse du serveur de temps à ***********, on peut
faire
.B --dhcp-option = 42,*********** 
ou 
.B --dhcp-option = option:ntp-server, ***********
L'adresse 0.0.0.0 prends ici le sens "d'adresse de la machine sur laquelle
tourne Dnsmasq". Les types de données autorisées sont des adresses IP sous la
forme de 4 chiffres séparés par des points, un nombre décimal, une liste de
caractères hexadécimaux séparés par des 2 points, ou une chaîne de caractères.
Si des labels optionnels sont fournis, alors cette option n'est envoyée
qu'aux réseaux dont tous les labels coïncident avec ceux de la requête.

Un traitement spécial est effectué sur les chaînes de caractères fournies pour
l'option 119, conformément à la RFC 3397. Les chaînes de caractères ou les
adresses IP sous forme de 4 chiffres séparés par des points donnés en arguments
de l'option 120 sont traités conformément à la RFC 3361. Les adresses IP sous
forme de 4 chiffres séparés par des points suivies par une barre montante "/",
puis une taille de masque sont encodés conformément à la RFC 3442.

Les options IPv6 sont fournies en utilisant le mot-clef
.B option6:
suivi par le numéro d'option ou le nom d'option. L'espace de nommage des options
IPv6 est disjoint de l'espace de nommage des options IPv4. Les adresses IPv6
en option doivent être entourées de crochets, comme par exemple :
.B --dhcp-option=option6:ntp-server,[1234::56]

Attention : aucun test n'étant fait pour vérifier que des données d'un type
adéquat sont envoyées pour un numéro d'option donné, il est tout à fait possible
de persuader Dnsmasq de générer des paquets DHCP illégaux par une utilisation
incorrecte de cette option. Lorsque la valeur est un nombre décimal, Dnsmasq
doit déterminer la taille des données. Cela est fait en examinant le numéro de
l'option et/ou la valeur, mais peut être évité en rajoutant un suffixe d'une
lettre comme suit :
b = un octet, s = 2 octets, i = 4 octets. Cela sert essentiellement pour des
options encapsulées de classes de vendeurs (voir plus bas), pour lesquelles 
Dnsmasq ne peut déterminer la taille de la valeur. Les données d'options
consistant uniquement de points et de décimaux sont interprétées par Dnsmasq
comme des adresses IP, et envoyées comme telles. Pour forcer l'envoi sous forme
de chaîne de caractère, il est nécessaire d'utiliser des guillemets doubles. Par
exemple, l'utilisation de l'option 66 pour fournir une adresse IP sous la forme
d'une chaîne de caractères comme nom de serveur TFTP, il est nécessaire de faire
comme suit :
.B --dhcp-option=66,"*******"

Les options encapsulées de classes de vendeurs peuvent être aussi spécifiées
(pour IPv4 seulement) en utilisant
.B --dhcp-option
: par exemple
.B --dhcp-option=vendor:PXEClient,1,0.0.0.0
envoie l'option encapsulée de classe de vendeur "mftp-address=0.0.0.0" à
n'importe quel client dont la classe de vendeur correspond à "PXEClient". La
correspondance pour les classes de vendeur s'effectue sur des sous-chaînes de
caractères (voir
.B --dhcp-vendorclass
pour plus de détails). Si une option de
classe de vendeur (numéro 60) est envoyée par Dnsmasq, alors cela est utilisé
pour sélectionner les options encapsulées, de préférence à toute option envoyée
par le client. Il est possible d'omettre complètement une classe de vendeur :
.B --dhcp-option=vendor:,1,0.0.0.0
Dans ce cas l'option encapsulée est toujours envoyée.

En IPv4, les options peuvent être encapsulées au sein d'autres options :
par exemple
.B --dhcp-option=encap:175, 190, "iscsi-client0"
enverra l'option 175, au sein de laquelle se trouve l'option 190.
Plusieurs options encapsulées avec le même numéro d'option seront correctement
combinées au sein d'une seule option encapsulée. Il n'est pas possible de
spécifier encap: et vendor: au sein d'une même option dhcp.

La dernière variante pour les options encapsulées est "l'option de Vendeur
identifiant le vendeur" ("Vendor-Identifying Vendor Options") telle que
décrite dans le RFC3925. Celles-ci sont spécifiées comme suit :
.B --dhcp-option=vi-encap:2, 10, "text"
Le numéro dans la section vi-encap: est le numéro IANA de l'entreprise servant
à identifier cette option. Cette forme d'encapsulation est également supportée
en IPv6.

L'adresse 0.0.0.0 n'est pas traitée de manière particulière lorsque fournie dans
une option encapsulée.
.TP
.B --dhcp-option-force=[tag:<label>,[tag:<label>]][encap:<option>,][vi-encap:<entreprise>,][vendor:[<classe_vendeur>],][<option>|option:<nom d'option>],[<valeur>[,<valeur>]]
Cela fonctionne exactement de la même façon que
.B --dhcp-option
sauf que cette option sera toujours envoyée, même si le client ne la demande pas
dans la liste de paramètres requis. Cela est parfois nécessaire, par exemple lors
de la fourniture d'options à PXELinux.
.TP
.B --dhcp-no-override
(IPv4 seulement) Désactive la réutilisation des champs DHCP nom de serveur et
nom de fichier comme espace supplémentaire pour les options. Si cela est
possible, dnsmasq déplace les informations sur le serveur de démarrage
et le nom de fichier (fournis par 'dhcp-boot') en dehors des champs
dédiés à cet usage dans les options DHCP. Cet espace supplémentaire est
alors disponible dans le paquet DHCP pour d'autres options, mais peut, dans
quelques rares cas, perturber des clients vieux ou défectueux. Cette
option force le comportement à l'utilisation des valeurs "simples et sûres"
afin d'éviter des problèmes dans de tels cas.
.TP
.B --dhcp-relay=<adresse locale>,<adresse de serveur>[,<interface]
Configure dnsmasq en temps que relais DHCP.  L'adresse locale est une
adresse allouée à l'une interface de la machine sur laquelle tourne dnsmasq.
Toutes les requêtes DHCP arrivant sur cette interface seront relayées au
serveur DHCP distant correspondant à l'adresse de serveur indiquée. Il est
possible de relayer depuis une unique adresse locale vers différents serveurs
distant en spécifiant plusieurs fois l'option dhcp-relay avec la même adresse
locale et différentes adresses de serveur. L'adresse de serveur doit être
sous forme numérique. Dans le cas de DHCPv6, l'adresse de serveur peut être
l'adresse de multicast ff05::1:3 correspondant à tous les serveurs DHCP. Dans
ce cas, l'interface doit être spécifiée et ne peut comporter de caractère
joker. Elle sera utilisée pour indiquer l'interface à partir de laquelle le
multicast pourra atteindre le serveur DHCP.

Le contrôle d'accès pour les clients DHCP suivent les mêmes règles que pour
les serveurs DHCP : voir --interface, --except-interface, etc. Le nom
d'interface optionel dans l'option dhcp-relay comporte une autre fonction :
il contrôle l'interface sur laquelle la réponse du serveur sera acceptée. Cela
sert par exemple dans des configurations à 3 interfaces : une à partir de
laquelle les requêtes sont relayées, une seconde permettant de se connecter à
un serveur DHCP, et une troisième reliée à un réseau non-sécurisé tel
qu'internet. Cela permet d'éviter l'arrivée de requêtes usurpées via cette
troisième interface.

Il est permis de configurer dnsmasq pour fonctionner comme serveur DHCP sur
certaines interfaces et en temps que relais sur d'autres. Cependant, même s'il
est possible de configurer dnsmasq de telle manière qu'il soit à la fois
serveur et relais pour une même interface, cela n'est pas supporté et la
fonction de relais prendra le dessus.

Le relais DHCPv4 et le relais DHCPv6 sont tous les deux supportés, mais il
n'est pas possible de relayer des requêtes DHCPv4 à un serveur DHCPv6 et
vice-versa.
.TP
.B \-U, --dhcp-vendorclass=set:<label>,[enterprise:<numéro IANA d'enterprise>,]<classe de vendeur>

Associe une chaîne de classe de vendeur à un label. La plupart
des clients DHCP fournissent une "classe de vendeur" ("vendor class") qui
représente, d'une certaine façon, le type d'hôte. Cette option associe des
classes de vendeur à des labels, de telle sorte que des options DHCP peuvent être
fournies de manière sélective aux différentes classes d'hôtes. Par exemple,
.B dhcp-vendorclass=set:printers,Hewlett-Packard JetDirect
ou
.B dhcp-vendorclass=printers,Hewlett-Packard JetDirect
permet de n'allouer des options qu'aux imprimantes HP de la manière suivante :
.B --dhcp-option=tag:printers,3,***********
La chaîne de caractères de la classe de vendeur fournie en argument est cherchée
en temps que sous-chaîne de caractères au sein de la classe de vendeur fournie
par le client, de façon à permettre la recherche d'un sous-ensemble de la chaîne
de caractères ("fuzzy matching"). Le préfixe set: est optionnel mais autorisé
afin de conserver une certaine homogénéité.

Notez qu'en IPv6 (et seulement en IPv6), les noms de classes de vendeurs
sont dans un espace de nom associé au numéro attribué à l'entreprise par
l'IANA. Ce numéro est fourni par le biais du mot-clef enterprise: et seules
les classes de vendeurs associées au numéro spécifié seront cherchées.
.TP
.B \-j, --dhcp-userclass=set:<label>,<classe utilisateur>
Associe une chaîne de classe d'utilisateur à un label (effectue la
recherche sur des sous-chaînes, comme pour les classes de vendeur). La plupart
des clients permettent de configurer une "classe d'utilisateur". Cette option
associe une classe d'utilisateur à un label, de telle manière qu'il soit
possible de fournir des options DHCP spécifiques à différentes classes d'hôtes.
Il est possible, par exemple, d'utiliser ceci pour définir un serveur
d'impression différent pour les hôtes de la classe "comptes" et ceux de la
classe "ingénierie".
.TP
.B \-4, --dhcp-mac=set:<label>,<adresse MAC>
Associe une adresse matérielle (MAC) à un label. L'adresse
matérielle peut inclure des jokers. Par exemple
.B --dhcp-mac=set:3com,01:34:23:*:*:*
permet de définir le label "3com" pour n'importe quel hôte dont l'adresse
matérielle coïncide avec les critères définis.
.TP
.B --dhcp-circuitid=set:<label>,<identifiant de circuit>, --dhcp-remoteid=set:<label>,<identifiant distant>
Associe des options de relais DHCP issus de la RFC3046 à des labels.
Cette information peut être fournie par des relais DHCP. L'identifiant
de circuit ou l'identifiant distant est normalement fourni sous la forme d'une
chaîne de valeurs hexadécimales séparées par des ":", mais il est également
possible qu'elle le soit sous la forme d'une simple chaîne de caractères. Si
l'identifiant de circuit ou d'agent correspond exactement à celui fourni par le
relais DHCP, alors le label est apposé.
.B dhcp-remoteid
est supporté en IPv6 (mais non dhcp-circuitid).
.TP
.B --dhcp-subscrid=set:<label>,<identifiant d'abonné>
(IPv4 et IPv6) Associe des options de relais DHCP issues de la RFC3993 à des
labels.
.TP
.B --dhcp-proxy[=<adresse IP>]......
(IPv4 seulement) Un agent relai DHCP normal est uniquement utilisé pour faire
suivre les éléments initiaux de l'interaction avec le serveur DHCP. Une fois
que le client est configuré, il communique directement avec le serveur. Cela
n'est pas souhaitable si le relais rajoute des informations supplémentaires
aux paquets DHCP, telles que celles utilisées dans
.B dhcp-circuitid
et
.B dhcp-remoteid.
Une implémentation complète de relai peut utiliser l'option serverid-override
de la RFC 5107 afin de forcer le serveur DHCP à utiliser le relai en temps que
proxy complet, de sorte que tous les paquets passent par le relai. Cette option
permet d'obtenir le même résultat pour des relais ne supportant pas la RFC
5107. Fournie seule, elle manipule la valeur de server-id pour toutes les
interactions via des relais. Si une liste d'adresses IP est donnée, seules les
interactions avec les relais dont l'adresse est dans la liste seront affectées.
.TP
.B --dhcp-match=set:<label>,<numéro d'option>|option:<nom d'option>|vi-encap:<entreprise>[,<valeur>]
Si aucune valeur n'est spécifiée, associe le label si le client
envoie une option DHCP avec le numéro ou le nom spécifié. Lorsqu'une valeur est
fournie, positionne le label seulement dans le cas où l'option est fournie et
correspond à la valeur. La valeur peut être de la forme "01:ff:*:02", auquel
cas le début de l'option doit correspondre (en respectant les jokers). La
valeur peut aussi être de la même forme que dans
.B dhcp-option
, auquel cas l'option est traitée comme un tableau de valeur, et un des
éléments doit correspondre, ainsi

--dhcp-match=set:efi-ia32,option:client-arch,6

spécifie le label "efi-ia32" si le numéro 6 apparaît dnas la liste
d'architectures envoyé par le client au sein de l'option 93. (se référer
au RFC 4578 pour plus de détails). Si la valeur est un chaine de caractères,
celle-ci est recherchée (correspondance en temps que sous-chaîne).

Pour la forme particulière vi-encap:<numéro d'entreprise>, la comparaison se
fait avec les classes de vendeur "identifiant de vendeur" ("vendor-identifying
vendor classes") pour l'entreprise dont le numéro est fourni en option.
Veuillez vous référer à la RFC 3925 pour plus de détails.
.TP
.B --tag-if=set:<label>[,set:<label>[,tag:<label>[,tag:<label>]]]
Effectue une opération booléenne sur les labels. Si tous les labels
apparaissant dans la liste tag:<label> sont positionnés, alors tous les
la de la liste "set:<labels>" sont positionnés (ou supprimés, dans le cas
où "tag:!<label>" utilisé).
Si aucun tag:<label> n'est spécifié, alors tous les labels fournis par
set:<label> sont positionnés.
N'importe quel nombre de set: ou tag: peuvent être fournis, et l'ordre est sans
importance.
Les lignes tag-if sont exécutées dans l'ordre, ce qui fait que si un label dans
tag:<label> est un label positionné par une rêgle
.B tag-if,
la ligne qui positionne le label doit précéder celle qui le teste.
.TP
.B \-J, --dhcp-ignore=tag:<label>[,tag:<label>]
Lorsque tous les labels fournis dans l'option sont présents, ignorer l'hôte et
ne pas donner de bail DHCP.
.TP
.B --dhcp-ignore-names[=tag:<label>[,tag:<label>]]
Lorsque tous les labels fournis dans l'option sont présents, ignorer le
nom de machine fourni par l'hôte. Il est à noter que, à la différence de
l'option "dhcp-ignore", il est permis de ne pas fournir de label.
Dans ce cas, les noms d'hôtes fournis par les clients DHCP seront toujours
ignorés, et les noms d'hôtes seront ajoutés au DNS en utilisant uniquement la
configuration dhcp-host de Dnsmasq, ainsi que le contenu des fichiers /etc/hosts
et /etc/ethers.
.TP
.B --dhcp-generate-names=tag:<label>[,tag:<label>]
(IPv4 seulement) Générer un nom pour les clients DHCP qui autrement n'en aurait
pas, en utilisant l'adresse MAC sous sa forme hexadécimale, séparée par des
tirets.
Noter que si un hôte fourni un nom, celui-ci sera utilisé de préférence au nom
autogénéré, à moins que
.B --dhcp-ignore-names 
ne soit positionné.
.TP
.B --dhcp-broadcast=[tag:<label>[,tag:<label>]]
(IPv4 seulement) Lorsque tous les labels fournis dans l'option sont présents,
toujours utiliser le broadcast pour communiquer avec l'hôte lorsque celui-ci
n'est pas configuré. Il est possible de ne spécifier aucun label, auquel cas
cette option s'applique inconditionnellement. La plupart des clients DHCP
nécessitant une réponse par le biais d'un broadcast activent une option dans
leur requête, ce qui fait que cela se fait automatiquement, mais ce n'est pas
le cas de certains vieux clients BOOTP.
.TP
.B \-M, --dhcp-boot=[tag:<label>,]<nom de fichier>,[<nom de serveur>[,<adresse de serveur>|<nom du serveur tftp>]]
(IPv4 seulement) Spécifie les options BOOTP devant être retournées par le
serveur DHCP. Le nom de serveur ainsi que l'adresse sont optionnels : s'ils
ne sont pas fournis, le nom est laissé vide et l'adresse fournie est celle de
la machine sur laquelle s'exécute Dnsmasq. Si Dnsmasq fournit un service TFTP (voir
.B --enable-tftp
), alors seul un nom de fichier est requis ici pour permettre un démarrage par
le réseau.
Si d'éventuels labels sont fournis, ils doivent coïncider avec
ceux du client pour que cet élément de configuration lui soit envoyé.
Une adresse de serveur TFTP peut être spécifiée à la place de l'adresse IP,
sous la forme d'un nom de domaine qui sera cherché dans le fichier /etc/hosts.
Ce nom peut être associé dans /etc/hosts avec plusieurs adresses IP, auquel cas
celles-ci seront utilisées tour à tour (algorithme round-robin).
Cela peut être utilisé pour équilibrer la charge tftp sur plusieurs serveurs.
.TP
.B --dhcp-sequential-ip
Dnsmasq est conçu pour choisir l'adresse IP des clients DHCP en utilisant
un hachage de l'adresse MAC du client. Cela permet en général à l'adresse
IP du client de rester stable au fil du temps, même lorsque le client laisse
expirer son bail DHCP de temps en temps. Dans ce mode de fonctionnement par
défaut, les adresses IP sont distribuées de façon pseudo-aléatoire dans la
totalité de la plage d'adresses utilisable. Il existe des circonstances (par
exemples pour du déploiement de serveur) où il est plus pratique d'allouer les
adresses IP de manière séquentielle, en commençant par la plus petite adresse
disponible, et c'est ce mode de fonctionnement qui est permis par cette option.
Veuillez noter que dans ce mode séquentiel, les clients qui laissent expirer
leur bail ont beaucoup plus de chance de voir leur adresse IP changer, aussi
cette option ne devrait pas être utilisée dans un cas général.
.TP
.B --dhcp-ignore-clid
Dnsmasq lit l'option 'client identifier' (RFC 2131) envoyée par les clients
(si disponible) afin d'identifier les clients. Cela permet de distribuer la
même adresse IP à un client utilisant plusieurs interfaces. Activer cette option
désactive la lecture du 'client identifier', afin de toujours identifier un client
en utilisant l'adresse MAC.
.TP
.B --pxe-service=[tag:<label>,]<CSA>,<entrée de menu>[,<nom de fichier>|<type de service de démarrage>][,<adresse de serveur>|<nom de serveur>]
La plupart des ROMS de démarrage PXE ne permettent au système PXE que la simple
obtention d'une adresse IP, le téléchargement du fichier spécifié dans
.B dhcp-boot
et son exécution. Cependant, le système PXE est capable de fonctions bien plus
complexes pour peu que le serveur DHCP soit adapté.

Ceci spécifie l'option de démarrage qui apparaitra dans un menu de démarrage
PXE. <CSA> est le type du système client. Seuls des types de services valides
apparaitront dans un menu. Les types connus sont x86PC, PC98, IA64_EFI, Alpha,
Arc_x86, Intel_Lean_Client, IA32_EFI, BC_EFI, Xscale_EFI et X86-64_EFI;
D'autres types peuvent être spécifiés sous la forme d'une valeur entière. Le
paramètre après le texte correspondant à l'entrée dans le menu peut être un nom
de fichier, auquel cas Dnsmasq agit comme un serveur de démarrage et indique au
client PXE qu'il faut télécharger ce fichier via TFTP, soit depuis ce serveur
(l'option
.B enable-tftp 
doit être spécifiée pour que cela marche), soit depuis un autre serveur TFTP
si une adresse ou un nom de serveur est fournie.
Veuillez noter que le suffixe de "couche" (en principe ".0") est fourni par PXE
et ne doit pas être rajouté au nom de fichier. Si une valeur numérique entière
est fournir pour le type de démarrage, en remplacement du nom de fichier, le
client PXE devra chercher un service de démarrage de ce type sur le réseau.
Cette recherche peut être faite via broadcast ou directement auprès d'un
serveur si son adresse IP ou son nom sont fournis dans l'option.
Si aucun nom de fichier n'est donné ni aucune valeur de type de service de
démarrage n'est fournie (ou qu'une valeur de 0 est donnée pour le type de
service), alors l'entrée de menu provoque l'interruption du démarrage par
le réseau et la poursuite du démarrage sur un média local. L'adresse de serveur
peut être donnée sous la forme de nom de domaine qui est recherché dans
/etc/hosts. Ce nom peut être associé à plusieurs adresses IP, qui dans ce cas
sont utilisées à tour de rôle (en "round-robin").
.TP
.B --pxe-prompt=[tag:<label>,]<invite>[,<délai>]
Cette option permet d'afficher une invite à la suite du démarrage PXE. Si un
délai est fourni, alors la première entrée du menu de démarrage sera
automatiquement exécutée après ce délai. Si le délai vaut 0, alors la première
entrée disponible sera exécutée immédiatement. Si
.B pxe-prompt
est omis, le système attendra un choix de l'utilisateur s'il existe plusieurs
entrées dans le menu, ou démarrera immédiatement dans le cas où il n'y a qu'une
seule entrée. Voir
.B pxe-service 
pour plus de détails sur les entrées de menu.

Dnsmasq peut servir de "proxy-DHCP" PXE, dans le cas où un autre serveur DHCP
sur le réseau est responsable de l'allocation des adresses IP, auquel cas
Dnsmasq se contente de fournir les informations données dans les options
.B pxe-prompt
et
.B pxe-service
pour permettre le démarrage par le réseau. Ce mode est activé en utilisant le
mot-clef
.B proxy
dans
.B dhcp-range.
.TP
.B \-X, --dhcp-lease-max=<nombre>
Limite Dnsmasq à un maximum de <nombre> baux DHCP. Le défaut est de 1000. Cette
limite permet d'éviter des attaques de déni de service ("DoS") par des hôtes
créant des milliers de baux et utilisant beaucoup de mémoire dans le processus
Dnsmasq.
.TP
.B \-K, --dhcp-authoritative
Doit être spécifié lorsque dnsmasq est réellement le seul serveur DHCP
sur le réseau. Pour DHCPv4, cela change le comportement par défaut qui est
celui d'un strict respect des RFC, afin que les requêtes DHCP pour des baux
inconnus par des hôtes inconnus ne soient pas ignorées. Cela permet à de
nouveaux hôtes d'obtenir des baux sans tenir compte de fastidieuses
temporisations ("timeout"). Cela permet également à Dnsmasq de reconstruire
sa base de données contenant les baux sans que les clients n'aient besoin de
redemander un bail, si celle-ci est perdue.
Dans le cas de DHCPv6, cela positionne la priorité des réponses à 255 (le
maximum) au lieu de 0 (le minimum).
.TP
.B --dhcp-alternate-port[=<port serveur>[,<port client>]]
(IPv4 seulement) Change les ports utilisés par défaut pour le DHCP. Si cette
option est donnée seule sans argument, alors change les ports utilisés pour le
DHCP de 67 et 68 respectivement à 1067 et 1068. Si un seul argument est donné, ce
numéro est utilisé pour le port serveur et ce numéro plus 1 est utilisé pour le
port client. Enfin, en fournissant deux numéros de ports, il est possible de
spécifier arbitrairement 2 ports à la fois pour le serveur et pour le client DHCP.
.TP
.B \-3, --bootp-dynamic[=<identifiant de réseau>[,<identifiant de réseau>]]
(IPv4 seulement) Permet l'allocation dynamique d'adresses IP à des clients BOOTP.
Utiliser cette option avec précaution, une adresse allouée à un client BOOTP
étant perpétuelle, et de fait n'est plus disponibles pour d'autres hôtes. Si
aucun argument n'est donné, alors cette option permet une allocation dynamique
dans tous les cas. Si des arguments sont spécifiés, alors l'allocation ne se
fait que lorsque tous les identifiants coïncident. Il est possible de répéter
cette option avec plusieurs jeux d'arguments.
.TP
.B \-5, --no-ping
(IPv4 seulement) Par défaut, le serveur DHCP tente de s'assurer qu'une adresse
n'est pas utilisée avant de l'allouer à un hôte. Cela est fait en envoyant une
requête ICMP de type "echo request" (aussi connue sous le nom de "ping") à
l'adresse en question. Si le serveur obtient une réponse, alors l'adresse doit
déjà être utilisée et une autre est essayée. Cette option permet de supprimer
cette vérification. A utiliser avec précaution.
.TP
.B --log-dhcp
Traces additionnelles pour le service DHCP : enregistre toutes les options
envoyées aux clients DHCP et les labels utilisés pour la
détermination de celles-ci.
.TP
.B --quiet-dhcp, --quiet-dhcp6, --quiet-ra
Supprime les logs des opérations de routine des protocoles concernés. Les
erreurs et les problèmes seront toujours enregistrés. L'option --log-dhcp
prends le pas sur --quiet-dhcp et quiet-dhcp6.
.TP
.B \-l, --dhcp-leasefile=<chemin de fichier>
Utilise le fichier dont le chemin est fourni pour stocker les informations de
baux DHCP.
.TP
.B --dhcp-duid=<ID d'entreprise>,<uid>
(IPv6 seulement) Spécifie le numéro d'UID de serveur persistant que le serveur
DHCPv6 doit utiliser. Cette option n'est normalement pas requise, Dnsmasq
créant un DUID automatiquement lorsque cela est nécessaire. Lorsque cette
option est positionnée, elle fournit à Dnsmasq les données nécessaires à la
création d'un DUID de type DUID-EN. Veuillez noter qu'une fois créé, le DUID
est stocké dans la base des baux, aussi changer entre un DUID créé
automatiquement et un DUID-EN et vice-versa impose de réinitialiser la base de
baux. Le numéro d'ID d'entreprise est assigné par l'IANA, et l'uid est une
chaine hexadécimale unique à chaque serveur.
.TP
.B \-6 --dhcp-script=<chemin de fichier>
Lorsqu'un bail DHCP est créé, qu'un ancien est supprimé, ou qu'un transfert
TFTP est terminé, le fichier dont le
chemin  est spécifié est exécuté. Le <chemin de fichier> doit être un chemin
absolu, aucune recherche n'est effectuée via la variable d'environnement PATH.
Les arguments fournis à celui-ci sont soit
"add" ("ajouter"), "old" ("ancien") ou "del" ("supprimer"), suivi de l'adresse
MAC de l'hôte (ou le DUID pour IPv6) puis l'adresse IP et le nom d'hôte si
celui-ci est connu."add" signifie qu'un bail a été créé, "del" signifie qu'il a
été supprimé, "old" notifie que le bail existait au lancement de Dnsmasq, ou un
changement d'adresse MAC ou de nom d'hôte pour un bail existant (ou, dans le cas
où leasefile-ro est spécifié, un changement de durée de bail ou d'identifiant
d'hôte). Si l'adresse Mac est d'un type de réseau autre qu'ethernet, il est
nécessaire de la préceder du type de réseau, par exemple "06-01:23:45:67:89:ab"
pour du token ring. Le processus est exécuté en temps que super-utilisateur 
(si Dnsmasq a été lancé en temps que "root"), même si Dnsmasq est configuré 
pour changer son UID pour celle d'un utilisateur non-privilégié.

L'environnement est hérité de celui de l'invocation du processus Dnsmasq,
auquel se rajoute quelques unes ou toutes les variables décrites ci-dessous :

Pour IPv4 et IPv6 :

DNSMASQ_DOMAIN si le nom de domaine pleinement qualifié de l'hôte est connu, la
part relative au domaine y est stockée. (Notez que le nom d'hôte transmis comme
argument au script n'est jamais pleinement qualifié).

Si le client fournit un nom d'hôte, DNSMASQ_SUPPLIED_HOSTNAME.

Si le client fournit des classes d'utilisateur, DNSMASQ_USER_CLASS0 à
DNSMASQ_USER_CLASSn.

Si Dnsmasq a été compilé avec l'option HAVE_BROKEN_RTC ("horloge RTC
défectueuse"), alors la durée du bail (en secondes) est stockée dans la
variable DNSMASQ_LEASE_LENGTH, sinon la date d'expiration du bail est toujours
stocké dans la variable d'environnement DNSMASQ_LEASE_EXPIRES. Le nombre de
secondes avant expiration est toujours stocké dans DNSMASQ_TIME_REMAINING.

Si un bail était associé à un nom d'hôte et
que celui-ci est supprimé, un évênement de type "old" est généré avec le
nouveau statut du bail, c-à-d sans nom d'hôte, et le nom initial est fourni
dans la variable d'environnement DNSMASQ_OLD_HOSTNAME.

La variable DNSMASQ_INTERFACE contient le nom de l'interface sur laquelle la
requête est arrivée; ceci n'est pas renseigné dans le cas des actions "old"
ayant lieu après un redémarrage de dnsmasq.

La variable DNSMASQ_RELAY_ADDRESS est renseignée si le client a utilisé un
relai DHCP pour contacter Dnsmasq, si l'adresse IP du relai est connue.

DNSMASQ_TAGS contient tous les labels fournis pendant la transaction DHCP,
séparés par des espaces.

DNSMASQ_LOG_DHCP est positionné si
.B --log-dhcp
est activé.

Pour IPv4 seulement :

DNSMASQ_CLIENT_ID, si l'hôte a fourni un identifiant de client.

DNSMASQ_CIRCUIT_ID, DNSMASQ_SUBSCRIBER_ID, DNSMASQ_REMOTE_ID si un relai DHCP a
rajouté l'une de ces options.

Si le client fournit une information de classe de vendeur, DNSMASQ_VENDOR_CLASS.

Pour IPv6 seulement :

Si le client fournit une classe de vendeur (vendor-class), positionne
DNSMASQ_VENDOR_CLASS_ID avec comme contenu le numéro IANA de l'entreprise pour
la classe, et DNSMASQ_VENDOR_CLASS0..DNSMASQ_VENDOR_CLASSn pour les données.

DNSMASQ_SERVER_DUID contient le DUID du serveur : cette valeur est la même
pour chaque appel au script.

DNSMASQ_IAID contenant l'IAID pour le bail. Si le bail est une allocation
temporaire, cela est préfixé par le caractère 'T'.

DNSMASQ_MAC contient l'adresse MAC du client, si celle-ci est connue.

A noter que le nom d'hôte fourni, la classe de vendeur ou les données de classe
d'utilisateur sont uniquement fournies pour les actions "add" ou l'action "old"
lorsqu'un hôte reprend un bail existant, puisque ces informations ne sont pas
conservées dans la base de baux de dnsmasq.

Tous les descripteurs de fichiers sont fermés, sauf stdin, stdout et stderr qui
sont ouverts sur /dev/null (sauf en mode déverminage).

Le script n'est pas lancé de manière concurrente : au plus une instance du
script est exécutée à la fois (dnsmasq attend qu'une instance de script se
termine avant de lancer la suivante). Les changements dans la base des baux
nécessitant le lancement du script sont placé en attente dans une queue jusqu'à
terminaison d'une instance du script en cours. Si cette mise en queue fait que
plusieurs changements d'états apparaissent pour un bail donné avant que le
script puisse être lancé, alors les états les plus anciens sont supprimés et
lorsque le script sera finalement lancé, ce sera avec l'état courant du bail.

Au démarrage de Dnsmasq, le script sera invoqué pour chacun des baux existants
dans le fichier des baux. Le script sera lancé avec l'action "del" pour les
baux expirés, et "old" pour les autres. Lorsque Dnsmasq reçoit un signal HUP,
le script sera invoqué avec une action "old" pour tous les baux existants.

Il existe deux autres actions pouvant apparaître comme argument au script :
"init" et "tftp". D'autres sont susceptibles d'être rajoutées dans le futur,
aussi les scripts devraient être écrits de sorte à ignorer les actions
inconnues. "init" est décrite ci-dessous dans
.B --leasefile-ro.
L'action "tftp" est invoquée lorsqu'un transfert de fichier TFTP s'est
terminé. Ses arguments sont la taille du fichier en octets, l'adresse à
laquelle le fichier a été envoyé, ainsi que le chemin complet du fichier.

.TP
.B --dhcp-luascript=<chemin>
Spécifie un script écrit en Lua, devant être exécuté lorsque des baux sont
créés, détruits ou modifiés. Pour utiliser cette option, dnsmasq doit être
compilé avec avec le support de Lua. L'interpréteur Lua est initialisé une
seule fois, lorsque dnsmasq démarre, ce qui fait que les variables globales
persistent entre les événements liés aux baux. Le code Lua doit définir une
fonction
.B lease
et peut fournir des fonctions
.B init
et
.B shutdown
qui sont appellées, sans arguments, lorsque dnsmasq démarre ou s'arrête.
Il peut également fournir une fonction
.B tftp.

La fonction
.B lease
reçoit les informations détaillées dans
.B --dhcp-script. 
Il reçoit deux arguments. Le premier spécifie l'action, qui est une chaîne de
caractères contenant les valeurs "add" (ajout), "old" (réactivation d'un bail
existant) ou "del" (suppression). Le deuxième est une table contenant des
paires de valeurs de labels. Les labels correspondent pour l'essentiel aux
valeurs d'environnement détaillées ci-dessus, ainsi le label "domain" (domaine)
contient les mêmes données que la variable d'environnement DNSMASQ_DOMAIN. Il
existe quelques labels supplémentaires contenant les données fournies comme
arguments à
.B --dhcp-script. 
Ces labels sont
.B mac_address, ip_address
(pour respectivement l'adresse MAC et l'adresse IP)
et
.B hostname
(le nom d'hôte) dans le cas d'IPv4, et
.B client_duid, ip_address
(valeur DUID du client et adresse IP respectivement)
ainsi que
.B hostname
(le nom d'hôte) dans le cas d'IPv6.

La fonction
.B tftp
est appelée de la même façon que la fonction "lease", et la table contient les
labels
.B destination_address,
.B file_name
et
.B file_size
(respectivement "adresse de destination", "nom de fichier" et "taille de fichier").
.TP
.B --dhcp-scriptuser
Spécifie l'utilisateur sous lequel le script shell lease-change ou le script
doivent être exécutés. La valeur par défaut correspond à l'utilisateur root
mais peut être changée par le biais de cette option.
.TP
.B \-9, --leasefile-ro
Supprimer complètement l'usage du fichier servant de base de donnée pour les
baux DHCP. Le fichier ne sera ni créé, ni lu, ni écrit. Change la façon dont le
script de changement d'état de bail est lancé (si celui-ci est fourni par le
biais de l'option
.B --dhcp-script
), de sorte que la base de données de baux puisse
être complètement gérée par le script sur un stockage externe. En addition aux
actions décrites dans 
.B  --dhcp-script,
le script de changement d'état de bail est appelé une fois, au lancement de
Dnsmasq, avec pour seul argument "init". Lorsqu'appelé de la sorte, le script
doit fournir l'état de la base de baux, dans le format de fichier de baux de
Dnsmasq, sur sa sortie standard (stdout) et retourner un code de retour de 0.
Positionner cette option provoque également une invocation du script de
changement d'état de bail à chaque changement de l'identifiant de client, de
longueur de bail ou de date d'expiration.
.TP
.B --bridge-interface=<interface>,<alias>[,<alias>]
Traiter les requêtes DHCP (v4 et v6) et IPv6 Router Solicit arrivant
sur n'importe laquelle des interfaces <alias> comme si elles
arrivaient de l'interface <interface>. Cette option permet à dnsmasq
de fournir les service DHCP et RA sur les interfaces ethernet non
adressés et non pontés; par exemple sur un hôte de calcul d'OpenStack
où chaque telle interface est une interface TAP à une machine
virtuelle, ou lors de l'utilisation de pont ethernet "ancien mode" sur
plate-forme BSD.  Chaque <alias> peut finir avec un simple '*' joker.
.TP
.B \-s, --domain=<domaine>[,<gamme d'adresses>[,local]]
Spécifie le domaine du serveur DHCP. Le domaine peut être donné de manière
inconditionnelle (sans spécifier de gamme d'adresses IP) ou pour des gammes
d'adresses IP limitées. Cela a deux effets; tout d'abord, le
serveur DHCP retourne le domaine à tous les hôtes le demandant, deuxièmement,
cela spécifie le domaine valide pour les hôtes DHCP configurés. Le but de cela
est de contraindre les noms d'hôte afin qu'aucun hôte sur le LAN ne puisse
fournir via DHCP un nom tel que par exemple "microsoft.com" et capturer du
trafic de manière illégitime. Si aucun nom de domaine n'est spécifié, alors
les noms d'hôtes avec un nom de domaine (c-à-d un point dans le nom) seront
interdits et enregistrés dans le journal (logs). Si un suffixe est fourni, alors
les noms d'hôtes possédant un domaine sont autorisés, pour peu que le nom de
domaine coïncide avec le nom fourni. De plus, si un suffixe est fourni, alors
les noms d'hôtes ne possédant pas de nom de domain se voient rajouter le
suffixe fourni dans l'option
.B --domain.
Ainsi, sur mon réseau, je peux configurer
.B --domain=thekelleys.org.uk
et avoir une machine dont le nom DHCP serait "laptop". L'adresse IP de cette
machine sera disponible à la fois pour "laptop" et "laptop.thekelleys.org.uk".
Si la valeur fournie pour <domaine> est "#", alors le nom de domaine est
positionné à la première valeur de la directive "search" du fichier
/etc/resolv.conf (ou équivalent).

La gamme d'adresses peut être de la forme
<adresse IP>,<adresse IP> ou <adresse IP>/<masque de réseau> voire une simple
<adresse IP>. Voir
.B --dhcp-fqdn
qui peut changer le comportement de dnsmasq relatif aux domaines.

Si la gamme d'adresse est fournie sous la forme
<adresse IP>/<taille de réseau>, alors le drapeau "local" peut être rajouté
qui a pour effet d'ajouter --local-declarations aux requêtes DNS directes et
inverses. C-à-d
.B --domain=thekelleys.org.uk,***********/24,local
est identique à
.B --domain=thekelleys.org.uk,***********/24
--local=/thekelleys.org.uk/ --local=/0.168.192.in-addr.arpa/
La taille de réseau doit être de 8, 16 ou 24 pour être valide.
.TP
.B --dhcp-fqdn
Dans le mode par défaut, dnsmasq insère les noms non-qualifiés des clients
DHCP dans le DNS. Pour cette raison, les noms doivent être uniques, même si
deux clients ayant le même nom sont dans deux domaines différents. Si un
deuxième client DHCP apparaît ayant le même nom qu'un client déjà existant,
ce nom est transféré au nouveau client. Si
.B --dhcp-fqdn
est spécifié, ce comportement change : les noms non qualifiés ne sont plus
rajoutés dans le DNS, seuls les noms qualifiés le sont. Deux clients DHCP
avec le même nom peuvent tous les deux garder le nom, pour peu que la partie
relative au domaine soit différente (c-à-d que les noms pleinement qualifiés
diffèrent). Pour s'assurer que tous les noms ont une partie domaine, il doit y
avoir au moins un
.B --domain
sans gamme d'adresses de spécifié lorsque l'option
.B --dhcp-fqdn 
est configurée.
.TP
.B --dhcp-client-update
Normalement, lorsque dnsmasq fournit un bail DHCP, il positionne un label
dans l'option FQDN pour indiquer au client qu'il ne doit pas tenter de faire
une mise à jour DDNS avec son nom et son adresse IP. Ceci parce que la paire
Nom-IP est rajoutée automatiquement dans la partie DNS de dnsmasq. Cette option
inhibe ce comportement ce qui est utile, par exemple, pour permettre aux clients
Windows de la mise à jour de serveurs Active Directory. Voir la RFC 4702 pour
plus de détails.
.TP
.B --enable-ra
Active la fonctionnalité d'annonces routeurs IPv6 ("IPv6 Router Advertisement").
DHCPv6 ne gère pas la configuration complète du réseau de la même façon que
DHCPv4. La découverte de routeurs et la découverte (éventuelle) de préfixes pour
la création autonome d'adresse sont gérées par un protocole différent.
Lorsque DHCP est utilisé, seul un sous-ensemble de tout ceci est nécessaire et
dnsmasq est à même de le gérer, en utilisant la configuration DHCP présente pour
fournir la majorité des données. Lorsque les annonces routeurs (RA pour "Router
Advertisement") sont activées, dnsmasq va annoncer un préfixe pour chaque
dhcp-range et, par défaut, fournir comme valeur de routeur et de DNS récursif
la valeur d'adresse link-local appropriée parmi celles de la machine sur
laquelle tourne dnsmasq.
Par défaut, les bits "managed address" sont positionnés, et le bit "use SLAAC"
("utiliser SLAAC") est réinitialisé. Cela peut être changé pour des
sous-réseaux donnés par le biais du mot clef de mode décris dans
.B --dhcp-range.
Les paramètres DNS du RFC6106 sont inclus dans les annonces. Par défaut,
l'adresse link-local appropriée parmi celles de la machine sur laquelle tourne
dnsmasq est spécifiée comme DNS récursif. Si elles sont fournies, les
options dns-server et domain-search sont utilisées respectivement pour RDNSS et
DNSSL.
.TP
.B --ra-param=<interface>,[mtu:<valeur>|<interface>|off,][high,|low,]<intervalle d'annonce routeur>[,<durée de vie route>]
Configure pour une interface donnée des valeurs pour les annonces routeurs
différentes des valeurs par défaut. La valeur par défaut du champ priorité
pour le routeur peut être changée de "medium" (moyen) à "high" (haute) ou
"low" (basse). Par exemple :
.B --ra-param=eth0,high,0.
Un intervalle (en secondes) entre les annonces routeur peut être fourni par :
.B --ra-param=eth0,60.
La durée de vie de la route peut être changée ou mise à zéro, auquel cas
le routeur peut annoncer les préfixes mais pas de route :
.B --ra-param=eth0,0,0
(une valeur de zéro pour l'intervalle signifie qu'il garde la valeur par défaut).
Ces quatre paramètres peuvent être configurés en une fois :
.B --ra-param=eth0,mtu:1280,low,60,1200
La valeur pour l'interface peut inclure un caractère joker.
.TP
.B --enable-tftp[=<interface>[,<interface>]]
Active la fonction serveur TFTP. Celui-ci est de manière délibérée limité aux
fonctions nécessaires au démarrage par le réseau ("net-boot") d'un client. Seul
un accès en lecture est possible; les extensions tsize et blksize sont supportées
(tsize est seulement supportée en mode octet). Sans argument optionnel, le service
TFTP est fourni sur les mêmes interfaces que le service DHCP. Si une liste
d'interfaces est fournie, cela définit les interfaces sur lesquelles le
service TFTP sera activé.
.TP
.B --tftp-root=<répertoire>[,<interface>]
Les fichiers à fournir dans les transferts TFTP seront cherchés en prenant le
répertoire fourni comme racine. Lorsque cela est fourni, les chemins TFTP
incluant ".." sont rejetés, afin d'éviter que les clients ne puissent sortir de
la racine spécifiée. Les chemins absolus (commençant par "/") sont autorisés,
mais ils doivent être à la racine TFTP fournie. Si l'option interface est
spécifiée, le répertoire n'est utilisé que pour les requêtes TFTP reçues sur
cette interface.
.TP
.B --tftp-unique-root
Ajouter l'adresse IP du client TFTP en temps qu'élément de chemin, à la suite
de la racine tftp (adresse sous forme de 4 chiffres séparés par des points).
Uniquement valable si une racine TFTP est spécifiée et si le répertoire
correspond existe. Ainsi, si la valeur pour tftp-root est "/tftp" et que le
client d'adresse IP ******* requiert le fichier "monfichier", alors le chemin
effective résultant sera "/tftp/*******/monfichier" si /tftp/******* existe, ou
"/tftp/monfichier" dans le cas contraire.
.TP
.B --tftp-secure
Active le mode TFTP sécurisé : sans cela, tout fichier lisible
par Dnsmasq est disponible via TFTP (les règles de contrôle d'accès unix
habituelles s'appliquent). Lorsque l'option
.B --tftp-secure
est spécifiée, seuls les fichiers possédés par l'utilisateur sous lequel tourne
le processus Dnsmasq sont accessibles. Si Dnsmasq est exécuté en temps que
super-utilisateur ("root"), des règles différentes s'appliquent :
.B --tftp-secure
n'a aucun effet, mais seuls les fichiers ayant un droit de lecture pour tout le
monde sont accessibles. Il n'est pas recommandé d'exécuter Dnsmasq sous
l'utilisateur "root" lorsque le service TFTP est activé, et il est formellement
déconseillé de le faire sans fournir l'option
.B --tftp-root.
Sans cela, en effet, l'accès de tous les fichiers du serveur pour lequel le
droit de lecture pour tout le monde est positionné ("world-readable") devient
possible par n'importe quel hôte sur le réseau.
.TP
.B --tftp-lowercase
Converti les noms de fichiers des requêtes TFTP en minuscules. Cela est utile
pour les requêtes effectuées depuis les machines Windows, dont les systèmes
de fichiers sont insensibles à la casse et pour lesquels la détermination
de la casse est parfois un peu aléatoire. A noter que le serveur tftp de
dnsmasq converti systématiquement les "\\" en "/" dans les noms de fichiers.
.TP
.B --tftp-max=<connexions>
Définit le nombre maximum de connexions TFTP simultanées autorisées. La valeur
par défaut est de 50. Lorsqu'un grand nombre de connexions TFTP est spécifié,
il se peut que la limite de nombre de descripteurs de fichiers par processus
soit atteinte. Dnsmasq nécessite quelques descripteurs de fichiers, ainsi qu'un
descripteur de fichier pour chaque connexion TFTP simultanée et pour chacun des
fichiers devant être fournis. De fait, servir le même fichier à n clients ne
nécessitera qu'environ n + 10 descripteurs de fichiers, alors que fournir des
fichiers tous différents à n clients utilisera environ (2*n) + 10 descripteurs.
Si elle est donnée, l'option 
.B --tftp-port-range
peut affecter le nombre maximum de connexions concurrentes.
.TP
.B --tftp-no-blocksize
Empêche le serveur TFTP de négocier l'option "blocksize" (taille de bloc) avec
les clients. Certains clients buggés spécifient cette option mais se comportent
ensuite de manière incorrecte si celle-ci est accordée.
.TP
.B --tftp-port-range=<début>,<fin>
Un serveur TFTP écoute sur le port prédéfini 69 ("well-known port") pour
l'initiation de la connexion, mais utilise également un port dynamiquement
alloué pour chaque connexion. Normalement, ces ports sont alloués par
le système d'exploitation, mais cette option permet de spécifier une gamme
de ports à utiliser pour les transferts TFTP. Cela peut être utile si
TFTP doit traverser un dispositif garde-barrière ("firewall"). La valeur
de début pour la plage de port ne peut être inférieure à 1025 sauf si
dnsmasq tourne en temps que super-utilisateur ("root"). Le nombre de
connexions TFTP concurrentes est limitée par la taille de la gamme de
ports ainsi spécifiée.
.TP
.B --tftp-port-range=<début>,<fin>
Un serveur TFTP écoute sur un numéro de port bien connu (69) pour l'initiation
de la connexion, et alloue dynamiquement un port pour chaque connexion. Ces
numéros de ports sont en principe alloués par le système d'exploitation, mais
cette option permet de spécifier une gamme de ports à utiliser pour les
transferts TFTP. Cela peut être utile lorsque ceux-ci doivent traverser un
dispositif garde-barrière ("firewall"). Le début de la plage ne peut être
inférieur à 1024 à moins que Dnsmasq ne fonctionne en temps que
super-utilisateur ("root"). Le nombre maximal de connexions TFTP concurrentes
est limitée par la taille de la plage de ports ainsi définie. 
.TP
.B \-C, --conf-file=<fichier>
Spécifie un fichier de configuration différent. L'option "conf-file" est
également autorisée dans des fichiers de configuration, ce qui permet
l'inclusion de multiples fichiers de configuration. L'utilisation de "-" comme
nom de fichier permet la lecture par dnsmasq de sa configuration sur l'entrée standard
stdin.
.TP
.B \-7, --conf-dir=<répertoire>[,<extension de fichier>...]
Lis tous les fichiers du répertoire spécifié et les traite comme des fichiers de
configuration. Si des extensions sont données, tout fichier finissant par ces
extensions seront ignorés. Tout fichier dont le nom se termine en ~ ou commence
par ., ainsi que ceux commençant ou se terminant par # seront systématiquement
ignorés.
Cette option peut être donnée en ligne de commande ou dans un fichier de
configuration.
.SH FICHIER DE CONFIGURATION
Au démarrage, Dnsmasq lis
.I /etc/dnsmasq.conf,
si ce fichier existe. (Sur FreeBSD, ce fichier est
.I /usr/local/etc/dnsmasq.conf
) (voir cependant les options 
.B \-C
et
.B \-7
). Le format de ce fichier consiste en une option par ligne, exactement comme
les options longues détaillées dans la section OPTIONS, mais sans être précédées
par "--". Les lignes commençant par # sont des commentaires et sont ignorées.
Pour les options qui ne peuvent-être spécifiées qu'une seule fois, celle du
fichier de configuration prends le pas sur celle fournie en ligne de commande.
Il est possible d'utiliser des guillemets afin d'éviter que les ",",":","." et
"#" ne soient interprétés, et il est possible d'utiliser les séquences
d'échappement suivantes : \\\\ \\" \\t \\e \\b \\r et \\n. Elles correspondent
respectivement à la barre oblique descendante ("anti-slash"), guillemets doubles,
tabulation, caractère d'échappement ("escape"), suppression ("backspace"), retour ("return") et
nouvelle ligne ("newline").
.SH NOTES
A la réception d'un signal SIGHUP,
.B Dnsmasq
vide son cache et recharge les fichiers
.I /etc/hosts
et
.I /etc/ethers 
ainsi que tout autre fichier spécifié par les options
.B --dhcp-hostsfile
,
.B --dhcp-optsfile
ou
.B --addn-hosts.
Le script de changement de bail est appellé pour chaque bail DHCP existant. Si
l'option
.B --no-poll
est positionnée, alors le fichier
.I /etc/resolv.conf
est également rechargé.
SIGHUP ne provoque PAS de rechargement du fichier de configuration.
.PP
A la réception d'un signal SIGUSR1,
.B Dnsmasq 
écrit des statistiques dans les traces système. Les informations fournies sont :
la taille du cache, le nombre de noms ayant été supprimés du cache avant
expiration afin de faire de la place pour les nouveaux noms, ainsi que le nombre
total d'entrées ayant été insérées dans le cache. Pour chaque serveur amont, il fournit
le nomnbre de requêtes transmises ainsi que le nombre de requêtes ayant résulté par une
erreur. Lorsque Dnsmasq a été lancé via
.B --no-daemon
ou lorsque la traçabilité maximale a été activée (
.B -q
), la totalité du contenu du
cache est de surcroît fournie.
.PP 
A la réception d'un signal SIGUSR2 et lorsqu'il enregistre directement ses
traces dans un fichier (voir
.B --log-facility
), alors 
.B Dnsmasq
ferme et rouvre le fichier de traces. Il faut noter que pendant cette
opération Dnsmasq ne s'exécute pas en tant que "root". Lorsqu'il créé un
fichier de traces pour la première fois, Dnsmasq change le propriétaire du
fichier afin de le faire appartenir à l'utilisateur non "root" sous lequel
Dnsmasq s'exécute. Le logiciel de rotation de fichiers de trace logrotate doit
être configuré pour créer un nouveau fichier avec un propriétaire identique au
fichier existant avant d'envoyer le signal SIGUSR2. Si une requête DNS TCP est
en cours, l'ancien fichier de traces reste ouvert dans le processus fils qui
traite la requête TCP et il peut y être écrit. Il existe cependant une limite
de 150 secondes après laquelle tous les processus traitant des requêtes TCP
expirent : pour cette raison, il est préférable de ne pas configurer la
compression des fichiers de traces venant juste de faire l'objet d'une rotation.
Dans le cas de l'utilisation du logiciel logrotate, les options requises sont
.B create 
et
.B delaycompress.
 
.PP
Dnsmasq est un logiciel de transmission de requêtes DNS : il n'est pas capable
d'effectuer une résolution de nom récursive en partant des serveurs DNS racine,
mais transmet de telles requêtes à un serveur DNS amont capable de telles
recherches récursives, ce qui est typiquement le cas d'un serveur DNS de FAI.
Par défaut, Dnsmasq lis
.I /etc/resolv.conf
pour découvrir les adresses IP des serveurs DNS amonts à utiliser, puisque cette
information est en général stockée à cet endroit. A moins que l'option
.B --no-poll
ne soit utilisée,
.B Dnsmasq
vérifie la date de modification du fichier
.I /etc/resolv.conf
(ou l'équivalent si 
.B \--resolv-file 
est utilisé), et le relis lorsqu'il change. Cela permet de définir les serveurs
DNS amont de manière dynamique lorsque PPP ou DHCP sont utilisés, puisque ces
protocoles fournissent cette information.
L'absence du fichier
.I /etc/resolv.conf
ne conduit pas à une erreur, puisqu'il peut très bien ne pas être créé avant
qu'une connexion PPP ne soit établie. Dans ce cas, Dnsmasq vérifie régulièrement
pour voir si un fichier
.I /etc/resolv.conf 
est créé. Dnsmasq peut être configuré pour lire plus d'un fichier resolv.conf.
Cela est utile sur un ordinateur portable où PPP et DHCP peuvent être utilisés :
Dnsmasq peut alors être configuré pour lire à la fois
.I /etc/ppp/resolv.conf 
et
.I /etc/dhcpc/resolv.conf 
et utilisera le contenu du fichier ayant changé en dernier, ce qui permet de
passer automatiquement de serveurs DNS à d'autres.
.PP
Les serveurs amonts peuvent aussi être spécifiés sur la ligne de commande ou
dans un fichier de configuration. Ces spécifications de serveurs peuvent
éventuellement se voir adjoindre d'un nom de domaine qui précise à Dnsmasq quel
serveur utiliser pour trouver les noms d'un domaine donné.
.PP
Pour configurer Dnsmasq afin qu'il se comporte comme un cache pour la machine
sur laquelle il tourne, mettre "nameserver 127.0.0.1" dans le fichier
.I /etc/resolv.conf
afin de forcer les processus locaux à envoyer leurs requêtes à Dnsmasq. Ensuite,
spécifier les serveurs DNS amont soit en les fournissant directement à Dnsmasq
via l'option
.B \--server
ou alors en mettant leurs adresses dans un autre fichier, par exemple
.I /etc/resolv.dnsmasq
et en lançant Dnsmasq avec l'option
.B \-r /etc/resolv.dnsmasq.
Cette deuxième technique permet la mise-à-jour dynamique des adresses de
serveurs DNS amont par le biais de PPP ou DHCP.
.PP
Les adresses dans /etc/hosts prennent le dessus sur celles fournies par le
serveur DNS amont, ainsi "macompagnie.com *******" dans /etc/hosts assure que
les requêtes pour "macompagnie.com" retourneront toujours *******, même si une
requête au serveur DNS amont retournerait une adresse différente. Il y a une
exception à ceci : si le DNS amont contient un CNAME qui pointe vers un nom
présent dans /etc/hosts, alors la recherche du CNAME via Dnsmasq fournira
l'adresse DNS amont. Pour contourner cela, il suffit de mettre l'entrée
correspondant au CNAME dans /etc/hosts.
.PP
le système de label fonctionne comme suit : pour chaque requête DHCP, dnsmasq
associe un ensemble de labels obtenus à partir des lignes de la configuration
incluant set:<label>, y compris un pour la plage d'adresse (
.B dhcp-range
) utilisée pour allouer l'adresse, un pour chaque entrée
.B dhcp-host
associée (auquel est rajouté le mot-clef "known" si une entrée dhcp-host
coïncide).

Le label "bootp" est associé aux requêtes BOOTP, un label dont le nom est le
nom de l'interface sur laquelle la requête est arrivée.

Pour les lignes de configuration comportant des éléments tag:<label>,
seules seront valides celles pour lesquels tous les labels correspondants
seront présents. C'est typiquement le cas des lignes dhcp-options.
Un
.B dhcp-option 
possédant des labels sera utilisé de préférence à un
.B dhcp-option 
sans label, pour peu que _tous_ les labels positionnés correspondent à l'ensemble
de labels décrit plus haut.
Le préfixe '!' sur un label est un indicateur de négation, ainsi
.B --dhcp=option=tag:!purple,3,*******
n'envoie l'option que lorsque le label "purple" n'est pas dans la liste de
labels définis pour l'hôte considéré. (dans le cas de l'utilisation dans une
ligne de commande au lieu d'un fichier de configuration, ne pas oublier
d'échapper le caractère !, qui est un méta-caractère d'interpréteur de commande
shell).

Lors de la sélection d'une option, une étiquette spécifiée par dhcp-range
passe après les autres étiquettes, ce qui permet de facilement remplacer des
option génériques pour des hôtes spécifiques, ainsi :
.B dhcp-range=set:interface1,......
.B dhcp-host=set:monhote,.....
.B dhcp-option=tag:interface1,option:nis-domain,"domaine1"
.B dhcp-option=tag:monhote,option:nis-domain,"domaine2"
va positionner l'option NIS-domain à domaine1 pour les hôtes dans la plage
d'adresse, sauf pour monhote pour lequel cette valeur sera domaine2.

.PP
Veuillez noter que pour
.B dhcp-range
, les éléments tag:<label> et set:<label> sont tous les deux autorisés
pour sélectionner la plage à utiliser selon, par exemple, le dhcp-host,
et pour affecter l'option envoyée, sur la base de la plage sélectionnée.

Ce système a évolué d'un système plus ancien et aux possibilités plus limitées,
et pour des raisons de compatibilité "net:" peut être utilisé à la place de
"tag:" et "set:" peut être omis (à l'exception de
.B dhcp-host,
où "net:" peut être utilisé à la place de "set:"). Pour les mêmes raisons, '#'
peut être utilisé à la place de '!' pour indiquer la négation.
.PP 
Le serveur DHCP intégré dans Dnsmasq fonctionne également en temps que serveur
BOOTP, pour peu que l'adresse MAC et l'adresse IP des clients soient fournies,
que ce soit par le biais de l'option 
.B dhcp-host 
ou dans le fichier
.I /etc/ethers
, et que l'option
.B dhcp-range 
soit présente afin d'activer le serveur DHCP pour un réseau donné (L'option
.B --bootp-dynamic
supprime la nécessité des associations statiques). Le paramètre
"filename" (nom de fichier) de la requête BOOTP est utilisé comme label, ainsi
que le label "bootp", permettant un certain contrôle sur les options retournées
aux différentes classes d'hôtes.


.SH CONFIGURATION EN TEMPS QUE SERVEUR FAISANT AUTORITÉ
.PP 
Configurer dnsmasq pour agir en temps que serveur DNS faisant autorité est
compliqué par le fait que cela implique la configuration de serveurs DNS
externes pour mettre en place la délégation. Seront présentés ci-dessous trois
scénarios de complexité croissante. Le pré-requis pour chacun de ces scénarios
est l'existence d'une adresse IP globalement disponible, d'un enregistrement de
type A ou AAAA pointant vers cette adresse, ainsi que d'un serveur DNS externe
capable d'effectuer la délégation de la zone en question. Pour la première
partie de ces explications, nous allons appeler serveur.exemple.com
l'enregistrement A (ou AAAA) de l'adresse globalement accessible, et
notre.zone.com la zone pour laquelle dnsmasq fait autorité.

La configuration la plus simple consiste en deux lignes de configuration,
sous la forme :
.nf
.B auth-server=serveur.exemple.com,eth0
.B auth-zone=notre.zone.com,*******/24
.fi

ainsi que deux enregistrements dans le DNS externe :

.nf
serveur.exemple.com       A    ***********
notre.zone.com            NS    serveur.exemple.com
.fi

eth0 est l'interface réseau externe sur laquelle dnsmasq écoute, dont l'adresse
IP (globalement accessible) est ***********. 

A noter que l'adresse IP externe peut parfaitement être dynamique (par exemple
attribuée par un FAI via DHCP ou PPP). Dans ce cas, l'enregistrement de type A
doit être lié à cet enregistrement dynamique par l'une ou l'autre des techniques
habituelles de système DNS dynamique.

Un exemple plus complexe mais en pratique plus utile correspond au cas où
l'adresse IP globalement accessible se trouve dans la zone pour laquelle
dnsmasq fait autorité, le plus souvent à la racine. Dans ce cas nous avons :

.nf
.B auth-server=notre.zone.com,eth0
.B auth-zone=notre.zone.com,*******/24
.fi

.nf
notre.zone.com             A    *******
notre.zone.com            NS    our.zone.com
.fi

L'enregistrement A pour notre.zone.com est dorénavant un enregistrement "colle"
qui résout le problème de poule et d'oeuf consistant à trouver l'adresse IP
du serveur de nom pour notre.zone.com lorsque l'enregistrement se trouve dans
la zone en question. Il s'agit du seul rôle de cet enregistrement : comme dnsmasq
fait désormais autorité pour notre.zone.com, il doit également fournir cet
enregistrement. Si l'adresse externe est statique, cela peut être réalisé par
le biais d'une entrée dans
.B /etc/hosts 
ou via un
.B --host-record.

.nf
.B auth-server=notre.zone.com,eth0
.B host-record=notre.zone.com,*******
.B auth-zone=notre.zone.com,*******/24
.fi

Si l'adresse externe est dynamique, l'adresse associée à notre.zone.com doit
être dérivée de l'interface correspondante. Cela peut être fait en utilisant
.B interface-name
Sous la forme :

.nf
.B auth-server=notre.zone.com,eth0
.B interface-name=notre.zone.com,eth0
.B auth-zone=notre.zone.com,*******/24
.fi

La configuration finale rajoute à cette base un serveur DNS secondaire. Il
s'agit d'un autre serveur DNS qui apprend les données DNS de la zone en
effectuant un transfert de zone, et qui joue le rôle de serveur de secours
au cas où le serveur principal devenait inaccessible. La configuration
de ce serveur secondaire sort du cadre de cette page de manuel. Les éléments
de configuration à rajouter dans dnsmasq sont les simples :

.nf
.B auth-sec-servers=secondaire.monfai.com
.fi

et

.nf
notre.zone.com           NS    secondaire.monfai.com
.fi

L'addition d'une option auth-sec-servers active les transferts de zone dans
dnsmasq, ce qui permet au serveur secondaire de venir collecter les données
DNS. Si vous souhaitez restreindre l'accès à ces données à des hôtes
spécifiques, vous pouvez le faire via :

.nf
.B auth-peer=<adresse IP du serveur secondaire>
.fi

Dnsmasq joue le rôle de serveur faisant autorité pour les domaines in-addr.arpa
et ip6.arpa associés aux sous-réseaux définis dans la déclaration de zone
auth-zone, ce qui fait que les requêtes DNS inversées (de l'adresse vers
le nom) peuvent simplement être configurées avec un enregistrement NS
adéquat. Par exemple, comme nous définissons plus haut les adresses
*******/24 :
.nf
 3.2.1.in-addr.arpa  NS    notre.zone.com
.fi

Veuillez noter que pour l'instant, les zones inverses ne sont pas
disponibles dans les transferts de zone, donc il est inutile de configurer
de serveur secondaire pour la résolution inverse.

.PP
Lorsque dnsmasq est configuré en temps que serveur faisant autorité,
les données suivantes sont utilisées pour peupler la zone considérée :
.PP
.B --mx-host, --srv-host, --dns-rr, --txt-record, --naptr-record
, pour autant que les noms des enregistrements se trouvent dans la zone en
question.
.PP
.B --cname
pour peu que le nom soit dans le domaine. Si la cible du CNAME n'est
pas pleinement qualifiée, alors elle est qualifiée avec le nom de la
zone pour laquelle le serveur fait autorité.
.PP
Les adresses IPv4 et IPv6 extraites de /etc/hosts (et
.B --addn-hosts
) ainsi que les options
.B --host-record
fournissant des adresses situées dans l'un des sous-réseaux spécifiés dans 
.B --auth-zone.
.PP
Adresses spécifiées par
.B --interface-name.
Dans ce cas, l'adresse n'est pas limitée à l'un des sous-réseaux donné dans
.B --auth-zone. 

.PP
Les adresses de baux DHCP, si l'adresse est située dans l'un des sous-réseaux de
.B --auth-zone
OU dans une plage DHCP construite. Dans le mode par défaut, où le bail
DHCP a un nom non qualifié, et éventuellement pour un nom qualifié construit
via
.B --domain
, alors le nom dans la zone faisant autorité est construit à partir du nom
non qualifié et du nom de domaine de la zone. Cela peut on non être égal
celui fourni par
.B --domain.
Si l'option
.B --dhcp-fqdn
est fournie, alors les noms pleinement qualifiés associés aux baux DHCP
sont utilisés, dès lors qu'ils correspondent au nom de domaine associé
à la zone.


.SH CODES DE SORTIE
.PP
0 - Dnsmasq s'est correctement lancé en tâche de fond, ou alors s'est
correctement terminé si le lancement en tâche de fond n'a pas été activé.
.PP
1 - Un problème de configuration a été détecté.
.PP
2 - Un problème est survenu avec un accès réseau (adresse déjà utilisée,
tentative d'utiliser un port privilégié sans les permissions nécessaires).
.PP
3 - Un problème est survenu avec une opération sur un système de fichier
(fichier ou répertoire manquant, permissions).
.PP
4 - Impossibilité d'allouer de la mémoire.
.PP
5 - Autre problème.
.PP
11 ou plus - un code de retour différent de 0 a été reçu lors de l'appel au
processus "init" du script des bails. Le code de retour de Dnsmasq correspond
au code de retour du script plus 10.

.SH LIMITES
Les valeurs par défaut pour les limites de ressources de Dnsmasq sont en général
conservatrices et appropriées pour des utilisations embarquées sur des machines
de type routeur ayant des processeurs lents et une mémoire limitée. Sur du
matériel plus performant, il est possible d'augmenter les limites et de gérer
plus de clients. Les remarques suivantes s'appliquent à Dnsmasq version 2.37 et
ultérieur : les versions précédentes ne montaient pas en charge aussi bien.
 
.PP
Dnsmasq est capable de gérer le DNS et DHCP pour au moins un millier de clients.
Pour cela, la durée des bail ne doit pas être très courte (moins d'une heure).
La valeur de
.B --dns-forward-max 
peut être augmentée : commencer par la rendre égale au nombre de clients et
l'augmenter si le DNS semble lent. Noter que la performance du DNS dépends
également de la performance des serveurs amonts. La taille du cache DNS peut-
être augmentée : la limite en dur est de 10000 entrées et la valeur par défaut
(150) est très basse. Envoyer un signal SIGUSR1 à Dnsmasq le fait émettre des
informations utiles pour paramétrer la taille de cache. Voir la section
.B NOTES
pour plus de détails.
.PP
Le serveur TFTP intégré est capable de plusieurs transferts de fichiers
simultanés : La limite absolue est liée au nombre maximal de descripteurs de
fichiers alloué à un processus et à la capacité de l'appel système select() à
gérer un grand nombre de HANDLE de fichier. Si la limite est fixée trop haut par
le biais de
.B --tftp-max
elle sera réduite et la limite actuelle sera enregistrée au démarrage. Il faut
noter que plus de transferts sont possible lorsque le même fichier est transmis
au lieu d'avoir un fichier différent pour chaque transfert.

.PP
Il est possible d'utiliser Dnsmasq pour bloquer la publicité sur la toile
en associant des serveurs de publicité bien connus à l'adresse 127.0.0.1 ou
0.0.0.0 par le biais du fichier
.B /etc/hosts 
ou d'un fichier d'hôte additionnel. Cette liste peut être très longue, Dnsmasq
ayant été testé avec succès avec un million de noms. Cette taille de fichier
nécessite un processeur à 1 Ghz et environ 60 Mo de RAM.

.SH INTERNATIONALISATION
Dnsmasq peut être compilé pour supporter l'internationalisation. Pour cela,
les cibles "all-i18n" et "install-i18n" doivent être données à make, en lieu
et place des cibles standards "all" et "install". Lorsque compilé avec le
support de l'internationalisation, dnsmasq supporte les noms de domaines
internationalisés ("internationalised domain names" ou IDN), et les messages de
traces ("logs") sont écrits dans la langue locale. Les noms de domaines dans
/etc/hosts, /etc/ethers et /etc/dnsmasq.conf contenant des caractères
non-ASCII seront transformés selon la représentation punycode interne
aux DNS. Veuillez noter que dnsmasq détermine la langue pour les messages
ainsi que le jeu de caractères susceptible d'être utilisé dans les fichiers
de configuration à partir de la variable d'environnement LANG. Ceci devrait
être configuré à la valeur par défaut du système par les scripts démarrant
dnsmasq. Lorsque les fichiers de configuration sont édités, veuillez faire
attention à le faire en utilisant la valeur de locale par défaut du système
et non une valeur spécifique à l'utilisateur, puisque dnsmasq n'a aucun
moyen de déterminer directement la valeur de jeu de caractère utilisé,
et assume de ce fait qu'il s'agit de la valeur par défaut du système.

.SH FICHIERS
.IR /etc/dnsmasq.conf 

.IR /usr/local/etc/dnsmasq.conf
.IR /var/run/dnsmasq/resolv.conf
.IR /etc/ppp/resolv.conf
.IR /etc/dhcpc/resolv.conf

.IR /etc/resolv.conf

.IR /etc/hosts

.IR /etc/ethers

.IR /var/lib/misc/dnsmasq.leases 

.IR /var/db/dnsmasq.leases

.IR /var/run/dnsmasq.pid
.SH VOIR AUSSI
.BR hosts (5), 
.BR resolver (5)
.SH AUTEUR
Cette page de manuel a été écrite par Simon Kelley <<EMAIL>>.

La traduction dans un français bancal a été commise par Gildas Le Nadan
<<EMAIL>> : Toute révision/correction permettant de corriger
orthographe ou grammaire mais surtout les éventuelles fautes de sens sera la
bienvenue!
