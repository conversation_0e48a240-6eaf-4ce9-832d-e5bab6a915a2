Q: Why does dnsmasq open UDP ports >1024 as well as port 53.
   Is this a security problem/trojan/backdoor?

A: The high ports that dnsmasq opens are for replies from the upstream
   nameserver(s). Queries from dnsmasq to upstream nameservers are sent
   from these ports and replies received to them. The reason for doing this is
   that most firewall setups block incoming packets _to_ port 53, in order
   to stop DNS queries from the outside world. If dnsmasq sent its queries
   from port 53 the replies would be _to_ port 53 and get blocked.

   This is not a security hole since dnsmasq will only accept replies to that
   port: queries are dropped. The replies must be to outstanding queries
   which dnsmasq has forwarded, otherwise they are dropped too.
 
   Addendum: dnsmasq now has the option "query-port" (-Q), which allows
   you to specify the UDP port to be used for this purpose.  If not
   specified, the operating system will select an available port number
   just as it did before.

   Second addendum: following the discovery of a security flaw in the
   DNS protocol, dnsmasq from version 2.43 has changed behavior. It
   now uses a new, randomly selected, port for each query. The old
   default behaviour (use one port allocated by the OS) is available by
   setting --query-port=0, and setting the query port to a positive
   value still works. You should think hard and know what you are
   doing before using either of these options.
 
Q: Why doesn't dnsmasq support DNS queries over TCP? Don't the RFC's specify
   that?

A: Update: from version 2.10, it does. There are a few limitations:
   data obtained via TCP is not cached, and source-address
   or query-port specifications are ignored for TCP.

Q: When I send SIGUSR1 to dump the contents of the cache, some entries have
   no IP address and are for names like mymachine.mydomain.com.mydomain.com.
   What are these?

A: They are negative entries: that's what the N flag means. Dnsmasq asked 
   an upstream nameserver to resolve that address and it replied "doesn't 
   exist, and won't exist for <n> hours" so dnsmasq saved that information so
   that if _it_ gets asked the same question it can answer directly without
   having to go back to the upstream server again. The strange repeated domains
   result from the way resolvers search short names. See "man resolv.conf" for
   details.


Q: Will dnsmasq compile/run on non-Linux systems?

A: Yes, there is explicit support for *BSD and MacOS X and Solaris. 
   There are start-up scripts for MacOS X Tiger and Panther 
   in /contrib. Dnsmasq will link with uclibc to provide small
   binaries suitable for use in embedded systems such as
   routers. (There's special code to support machines with flash
   filesystems and no battery-backed RTC.)
   If you encounter make errors with *BSD, try installing gmake from
   ports and building dnsmasq with "make MAKE=gmake" 
   For other systems, try altering the settings in config.h.
 
Q: My company's nameserver knows about some names which aren't in the
   public DNS. Even though I put it first in /etc/resolv.conf, it
   doesn't work: dnsmasq seems not to use the nameservers in the order
   given. What am I doing wrong?

A: By default, dnsmasq treats all the nameservers it knows about as
   equal: it picks the one to use using an algorithm designed to avoid 
   nameservers which aren't responding. To make dnsmasq use the
   servers in order, give it the -o flag. If you want some queries
   sent to a special server, think about using the -S flag to give the
   IP address of that server, and telling dnsmasq exactly which
   domains to use the server for.

Q: OK, I've got queries to a private nameserver working, now how about 
   reverse queries for a range of IP addresses?

A: Use the standard DNS convention of <reversed address>.in-addr.arpa.
   For instance to send reverse queries on the range *********** to 
   ************* to a nameserver at ******** do
   server=/0.168.192.in-addr.arpa/********
   Note that the "bogus-priv" option take priority over this option,
   so the above will not work when the bogus-priv option is set.

Q: Dnsmasq fails to start with an error like this: "dnsmasq: bind
   failed: Cannot assign requested address". What's the problem?

A: This has been seen when a system is bringing up a PPP interface at
   boot time: by the time dnsmasq start the interface has been
   created, but not brought up and assigned an address. The easiest
   solution is to use --interface flags to specify which interfaces
   dnsmasq should listen on. Since you are unlikely to want dnsmasq to
   listen on a PPP interface and offer DNS service to the world, the
   problem is solved.

Q: I'm running on BSD and dnsmasq won't accept long options on the
   command line. 

A: Dnsmasq when built on some BSD systems doesn't use GNU getopt by
   default. You can either just use the single-letter options or
   change config.h and the Makefile to use getopt-long. Note that
   options in /etc/dnsmasq.conf must always be the long form,
   on all platforms.

Q: Names on the internet are working fine, but looking up local names 
   from /etc/hosts or DHCP doesn't seem to work.

A: Resolver code sometime does strange things when given names without
   any dots in. Win2k and WinXP may not use the DNS at all and just
   try and look up the name using WINS. On unix look at "options ndots:"
   in "man resolv.conf" for details on this topic. Testing lookups
   using "nslookup" or "dig" will work, but then attempting to run
   "ping" will get a lookup failure, appending a dot to the end of the
   hostname  will fix things. (ie "ping myhost" fails, but "ping
   myhost." works. The solution is to make sure that all your hosts
   have a domain set ("domain" in resolv.conf, or set a domain in 
   your DHCP server, see below for Windows XP and Mac OS X). 
   Any domain  will do, but "localnet" is traditional. Now when you
   resolve "myhost" the resolver will attempt to look up 
   "myhost.localnet" so you need to have dnsmasq reply to that name. 
   The way to do that is to include the domain in each name on
   /etc/hosts  and/or to use the --expand-hosts and --domain options.

Q: How do I set the DNS domain in Windows XP or MacOS X (ref: previous
   question)?

A: for XP, Control Panel > Network Connections > { Connection to gateway /
   DNS } > Properties > { Highlight TCP/IP } > Properties > Advanced >
   DNS Tab > DNS suffix for this connection: 

A: for OS X, System Preferences > Network > {Connection to gateway / DNS } >
   Search domains:

Q: Can I get dnsmasq to save the contents of its cache to disk when
   I shut my machine down and re-load when it starts again?

A: No, that facility is not provided. Very few names in the DNS have
   their time-to-live set for longer than a few hours so most of the
   cache entries would have expired after a shutdown. For longer-lived
   names it's much cheaper to just reload them from the upstream
   server. Note that dnsmasq is not shut down between PPP sessions so
   go off-line and then on-line again will not lose the contents of
   the cache.

Q: Who are Verisign, what do they have to do with the bogus-nxdomain
   option in dnsmasq and why should I wory about it?

A: [note: this was written in September 2003, things may well change.]
   Verisign run the .com and .net top-level-domains. They have just
   changed the configuration of their servers so that unknown .com and
   .net domains, instead of returning an error code NXDOMAIN, (no such
   domain) return the address of a host at Verisign which runs a web
   server showing a search page. Most right-thinking people regard
   this new behaviour as broken :-).  You can test to see if you are
   suffering Verisign brokenness by run a command like 
   
   host jlsdajkdalld.com

   If you get "jlsdajkdalld.com" does not exist, then all is fine, if
   host returns an IP address, then the DNS is broken. (Try a few
   different unlikely domains, just in case you picked a weird one
   which really _is_ registered.)

   Assuming that your DNS is broken, and you want to fix it, simply
   note the IP address being returned and pass it to dnsmasq using the
   --bogus-nxdomain flag. Dnsmasq will check for results returning
   that address and substitute an NXDOMAIN instead. 

   As of writing, the IP address in question for the .com and .net
   domains is is ************. Various other, less prominent,
   registries pull the same stunt; there is a list of them all, and
   the addresses to block, at http://winware.org/bogus-domains.txt

Q: This new DHCP server is well and good, but it doesn't work for me.
   What's the problem?

A: There are a couple of configuration gotchas which have been
   encountered by people moving from the ISC dhcpd to the dnsmasq
   integrated DHCP daemon. Both are related to differences in 
   in the way the two daemons bypass the IP stack to do "ground up"
   IP configuration and can lead to the dnsmasq daemon failing
   whilst the ISC one works.

   The first thing to check is the broadcast address set for the
   ethernet interface. This is normally the address on the connected
   network with all ones in the host part. For instance if the 
   address of the ethernet interface is ************ and the netmask
   is ************* then the broadcast address should be
   **************. Having a broadcast address which is not on the
   network to which the interface is connected kills things stone
   dead.

   The second potential problem relates to firewall rules: since the ISC 
   daemon in some configurations bypasses the kernel firewall rules 
   entirely, the ability to run the ISC daemon does not indicate 
   that the current configuration is OK for the dnsmasq daemon.
   For the dnsmasq daemon to operate it's vital that UDP packets to 
   and from ports 67 and 68 and broadcast packets with source 
   address 0.0.0.0 and destination address *************** are not 
   dropped by iptables/ipchains.

Q: I'm running Debian, and my machines get an address fine with DHCP,
   but their names are not appearing in the DNS.

A: By default, none of the DHCP clients send the host-name when asking
   for a lease. For most of the clients, you can set the host-name to
   send with the "hostname" keyword in /etc/network/interfaces. (See
   "man interfaces" for details.) That doesn't work for dhclient, were
   you have to add something like "send host-name daisy" to
   /etc/dhclient.conf [Update: the latest dhcpcd packages _do_ send
   the hostname by default.

Q: I'm network booting my machines, and trying to give them static
   DHCP-assigned addresses. The machine gets its correct address
   whilst booting, but then the OS starts and it seems to get
   allocated a different address.

A: What is happening is this: The boot process sends a DHCP
   request and gets allocated the static address corresponding to its
   MAC address. The boot loader does not send a client-id. Then the OS
   starts and repeats the DHCP process, but it it does send a
   client-id. Dnsmasq cannot assume that the two requests are from the
   same machine (since the client ID's don't match) and even though
   the MAC address has a static allocation, that address is still in
   use by the first incarnation of the machine (the one from the boot,
   without a client ID.) dnsmasq therefore has to give the machine a
   dynamic address from its pool. There are three ways to solve this:
   (1) persuade your DHCP client not to send a client ID, or (2) set up
   the static assignment to the client ID, not the MAC address. The
   default client-id will be 01:<MAC address>, so change the dhcp-host
   line from "dhcp-host=11:22:33:44:55:66,*******" to
   "dhcp-host=id:01:11:22:33:44:55:66,*******" or (3) tell dnsmasq to
   ignore client IDs for a particular MAC address, like this:
   dhcp-host=11:22:33:44:55:66,id:*

Q: What network types are supported by the DHCP server?
  
A: Ethernet (and 802.11 wireless) are supported on all platforms. On
   Linux all network types (including FireWire) are supported.

Q: What are these strange "bind-interfaces" and "bind-dynamic" options?

A: Dnsmasq from v2.63 can operate in one of three different "networking
   modes". This is unfortunate as it requires users configuring dnsmasq
   to take into account some rather bizarre constraints and select the
   mode which best fits the requirements of a particular installation.
   The origin of these are deficiencies in the Unix networking
   model and APIs and each mode has different advantages and
   problems. Just to add to the confusion, not all modes are available on
   all platforms (due the to lack of supporting network APIs).To further
   add to the confusion, the rules for the DHCP subsystem on dnsmasq are
   different to the rules for the DNS and TFTP subsystems.

   The three modes are "wildcard", "bind-interfaces" and "bind-dynamic".

   In "wildcard" mode, dnsmasq binds the wildcard IP address (0.0.0.0 or
   ::). This allows it to receive all the packets sent to the server on
   the relevant port. Access control (--interface, --except-interface,
   --listen-address, etc) is implemented by dnsmasq: it queries the
   kernel to determine the interface on which a packet was received and
   the address to which it was sent, and applies the configured
   rules. Wildcard mode is the default if neither of the other modes are
   specified. 

   In "bind-interfaces" mode, dnsmasq runs through all the network
   interfaces available when it starts, finds the set of IP addresses on
   those interfaces, filters that set using the access control
   configuration, and then binds the set of IP addresses. Only packets
   sent to the allowed addresses are delivered by the kernel to dnsmasq.

   In "bind-dynamic" mode, access control filtering is done both by
   binding individual IP addresses, as for bind-interfaces, and by
   inspecting individual packets on arrival as for wildcard mode. In
   addition, dnsmasq notices when new interfaces appear or new addresses
   appear on existing interfaces, and the resulting IP addresses are
   bound automatically without having to restart dnsmasq.

   The mode chosen has four different effects: co-existence with other
   servers, semantics of --interface access control, effect of new
   interfaces, and legality of --interface specifications for
   non-existent interfaces. We will deal with these in order.

   A dnsmasq instance running in wildcard mode precludes a machine from
   running a second instance of dnsmasq or any other DNS, TFTP or DHCP
   server. Attempts to do so will fail with an "address in use" error. 
   Dnsmasq running in --bind-interfaces or bind-dynamic mode allow other
   instances of dnsmasq or other servers, as long as no two servers are
   configured to listen on the same interface address. 

   The semantics of --interface varies subtly between wildcard or
   bind-dynamic mode and bind-interfaces mode. The situation where this
   matters is a request which arrives via one interface (A), but with a
   destination address of a second interface (B) and when dnsmasq is
   configured to listen only on B. In wildcard or bind-dynamic mode, such
   a request will be ignored, in bind-interfaces mode, it will be
   accepted.

   The creation of new network interfaces after dnsmasq starts is ignored
   by dnsmasq when in --bind-interfaces mode. In wildcard or bind-dynamic
   mode, such interfaces are handled normally.

   An --interface specification for a non-existent interface is a fatal
   error at start-up when in --bind-interfaces mode, by just generates a
   warning in wildcard or bind-dynamic mode.

Q: Why doesn't Kerberos work/why can't I get sensible answers to
   queries for SRV records.

A: Probably because you have the "filterwin2k" option set. Note that
   it was on by default in example configuration files included in 
   versions before 2.12, so you might have it set on without
   realising.

Q: Can I get email notification when a new version of dnsmasq is
   released?

A: Yes, new releases of dnsmasq are always announced through
   freshmeat.net, and they allow you to subscribe to email alerts when
   new versions of particular projects are released. New releases are
   also announced in the dnsmasq-discuss mailing list, subscribe at 
   http://lists.thekelleys.org.uk/mailman/listinfo/dnsmasq-discuss

Q: What does the dhcp-authoritative option do? 

A: The DHCP spec says that when a DHCP server receives a renewal request
   from a client it has no knowledge of, it should just ignore it.
   This is because it's supported to have more than one DHCP server
   on a network, and another DHCP server may be dealing with the client.
   This has the unfortunate effect that when _no_ DHCP replies to 
   the client, it takes some time for the client to time-out and start 
   to get a new lease. Setting this option makes dnsmasq violate the
   standard to the extent that it will send a NAK reply to the client, 
   causing it to immediately start to get a new lease. This improves 
   behaviour when machines move networks, and in the case that the DHCP
   lease database is lost. As long as there are not more tha one DHCP
   server on the network, it's safe to enable the option.

Q: Why does my Gentoo box pause for a minute before getting a new
   lease?

A: Because when a Gentoo box shuts down, it releases its lease with
   the server but remembers it on the client; this seems to be a 
   Gentoo-specific patch to dhcpcd. On restart it tries to renew
   a lease which is long gone, as far as dnsmasq is concerned, and
   dnsmasq ignores it until is times out and restarts the process.
   To fix this, set the dhcp-authoritative flag in dnsmasq.

Q: My laptop has two network interfaces, a wired one and a wireless
   one. I never use both interfaces at the same time, and I'd like the
   same IP and configuration to be used irrespective of which
   interface is in use. How can I do that?

A: By default, the identity of a machine is determined by using the
   MAC address, which is associated with interface hardware. Once an
   IP is bound to the MAC address of one interface, it cannot be
   associated with another MAC address until after the DHCP lease
   expires. The solution to this is to use a client-id as the machine
   identity rather than the MAC address. If you arrange for the same
   client-id to sent when either interface is in use, the DHCP server
   will recognise the same machine, and use the same address. The
   method for setting the client-id varies with DHCP client software,
   dhcpcd uses the "-I" flag. Windows uses a registry setting,
   see http://www.jsiinc.com/SUBF/TIP2800/rh2845.htm

Addendum:
   From version 2.46, dnsmasq has a solution to this which doesn't
   involve setting client-IDs. It's possible to put more than one MAC
   address in a --dhcp-host configuration. This tells dnsmasq that it
   should use the specified IP for any of the specified MAC addresses,
   and furthermore it gives dnsmasq permission to summarily abandon a
   lease to one of the MAC addresses if another one comes along. Note
   that this will work fine only as longer as only one interface is
   up at any time. There is no way for dnsmasq to enforce this
   constraint: if you configure multiple MAC addresses and violate 
   this rule, bad things will happen.

Addendum-II: The link above is dead, the former contents of the link are:

------------------------------------------------------------------------------
How can I keep the same DHCP client reservation, if the MAC address changes?

When you reserve an IP address for a DHCP client, you provide the
MAC address of the client's NIC.

It is possible to use a custom identifier, which is sent as 
option 61 in the client's DHCP Discover and Request packet.

The DhcpClientIdentifier is a REG_DWORD value that is located at:

Windows NT 4.0 SP2+

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\<Adapter Name>'X'\Parameters\Tcpip

where <Adapter Name> is the NIC driver name and 'X' is the number of the NIC.

Windows 2000

HKEY_LOCAL_MACHINE\System\CurrentControlSet\Services\TcpIp\Parameters\Interfaces\<NIC GUID>

where <NIC GUID> is the GUID of the NIC.

The valid range of data is 0x0 - 0xFFFFFFFF. The custom identifier is send as 4 bytes, 
8 hexadecimal character, in groups of 2 hexadecimal characters, with the groups being 
sent in reverse order. If the custom identifier is less than 8 hexadeciaml characters, 
it is zero padded at the end. Examples:

Custom Client                 Client Reservation
Identifier                    on DHCP Server
12345678                      78563412
123456                        *************                          34120000
1234567                       67452301
12345                         45230100
123                           23010000
A18F42                        428FA100
CF432                         32F40C00
C32D1BE                       BED1320C

-------------------------------------------------------------------------------------------------------


Q: Can dnsmasq do DHCP on IP-alias interfaces?

A: Yes, from version-2.21. The support is only available running under
   Linux, on a kernel which provides the RT-netlink facility. All 2.4 
   and 2.6 kernels provide RT-netlink and it's an option in 2.2
   kernels. 
   
   If a physical interface has more than one IP address or aliases
   with extra IP addresses, then any dhcp-ranges corresponding to
   these addresses can be used for address allocation. So if an
   interface has addresses ***********/24 and ***********/24 and there
   are DHCP ranges *************-************* and
   *************-************* then both ranges would be used for host
   connected to the physical interface. A more typical use might be to
   have one of the address-ranges as static-only, and have known
   hosts allocated addresses on that subnet using dhcp-host options,
   while  anonymous hosts go on the other.


Q: Dnsmasq sometimes logs "nameserver xxx.xxx.xxx.xxx refused
   to do a recursive query" and DNS stops working. What's going on?

A: Probably the nameserver is an authoritative nameserver for a
   particular domain, but is not configured to answer general DNS
   queries for an arbitrary domain. It is not suitable for use by
   dnsmasq as an upstream server and should be removed from the
   configuration. Note that if you have more than one upstream
   nameserver configured dnsmasq will load-balance across them and
   it may be some time before dnsmasq gets around to using a 
   particular nameserver. This means that a particular configuration
   may work for sometime with a broken upstream nameserver
   configuration.


Q: Does the dnsmasq DHCP server probe addresses before allocating
   them, as recommended in RFC2131?

A: Yes, dynamically allocated IP addresses are checked by sending an
   ICMP echo request (ping). If a reply is received, then dnsmasq
   assumes that the address is in use, and attempts to allocate an
   different address. The wait for a reply is between two and three
   seconds. Because the DHCP server is not re-entrant, it cannot serve
   other DHCP requests during this time. To avoid dropping requests,
   the address probe may be skipped when dnsmasq is under heavy load.


Q: I'm using dnsmasq on a machine with the Firestarter firewall, and
   DHCP doesn't work. What's the problem?

A: This a variant on the iptables problem. Explicit details on how to
   proceed can be found at 
   http://lists.thekelleys.org.uk/pipermail/dnsmasq-discuss/2005q3/000431.html
 

Q: I'm using dnsmasq on a machine with the shorewall firewall, and
   DHCP doesn't work. What's the problem?

A: This a variant on the iptables problem. Explicit details on how to
   proceed can be found at 
   http://lists.thekelleys.org.uk/pipermail/dnsmasq-discuss/2007q4/001764.html


Q: Dnsmasq fails to start up with a message about capabilities.
   Why did that happen and what can do to fix it?

A: Change your kernel configuration: either deselect CONFIG_SECURITY
   _or_ select CONFIG_SECURITY_CAPABILITIES. Alternatively, you can 
   remove the need to set capabilities by running dnsmasq as root.


Q: Where can I get .rpms Suitable for openSUSE/SLES?

A: Dnsmasq is in openSUSE itself, and the latest releases are also
   available at http://download.opensuse.org/repositories/network/


Q: Can I run dnsmasq in a Linux vserver?

A: Yes, as a DNS server, dnsmasq will just work in a vserver.
   To use dnsmasq's DHCP function you need to give the vserver
   extra system capabilities. Please note that doing so will lesser 
   the overall security of your system. The capabilities 
   required are NET_ADMIN and NET_RAW. NET_ADMIN is essential, NET_RAW
   is required to do an ICMP "ping" check on newly allocated
   addresses. If you don't need this check, you can disable it with
   --no-ping and omit the NET_RAW capability. 
   Adding the capabilities is done by adding them, one per line, to
   either /etc/vservers/<vservername>/ccapabilities for a 2.4 kernel or
   /etc/vservers/<vservername>/bcapabilities for a 2.6 kernel (please
   refer to the vserver documentation for more information).


Q: What's the problem with syslog and dnsmasq?

A: In almost all cases: none. If you have the normal arrangement with
   local daemons logging to a local syslog, which then writes to disk,
   then there's never a problem. If you use network logging, then
   there's a potential problem with deadlock: the syslog daemon will
   do DNS lookups so that it can log the source of log messages,
   these lookups will (depending on exact configuration) go through
   dnsmasq, which also sends log messages. With bad timing, you can 
   arrive at a situation where syslog is waiting for dnsmasq, and
   dnsmasq is waiting for syslog; they will both wait forever. This
   problem is fixed from dnsmasq-2.39, which introduces asynchronous
   logging: dnsmasq no longer waits for syslog and the deadlock is
   broken. There is a remaining problem in 2.39, where "log-queries"
   is in use. In this case most DNS queries generate two log lines, if
   these go to a syslog which is doing a DNS lookup for each log line,
   then those queries will in turn generate two more log lines, and a 
   chain reaction runaway will occur. To avoid this, use syslog-ng
   and turn on syslog-ng's dns-cache function.


Q: DHCP doesn't work with windows Vista, but everything else is fine.

A: The DHCP client on windows Vista (and possibly later versions)
   demands that the DHCP server send replies as broadcasts. Most other
   clients don't do this. The broadcasts are send to
   ***************. A badly configured firewall which blocks such
   packets will show exactly these symptoms (Vista fails, others
   work).

  
Q: DHCP doesn't work with windows 7 but everything else is fine.

A: There seems to be a problem if Windows 7 doesn't get a value for
   DHCP option 252 in DHCP packets it gets from the server. The
   symptoms have been variously reported as continual DHCPINFORM
   requests in an attempt to get an option-252, or even ignoring DHCP
   offers completely (and failing to get an IP address) if there is no
   option-252 supplied. DHCP option 252 is for WPAD, WWW Proxy 
   Auto Detection and if you don't want or need to use that, then 
   simplest fix seems to be to supply an empty option with:

   dhcp-option=252,"\n"



 




   

	      
