#ifndef __OPENDNS_H__
#define __OPENDNS_H__ 1

char *opendns_parse_device_id_opt(char * const arg);

const unsigned char *
opendns_ssid_to_device_id(size_t * const device_id_len_p,
                          const unsigned char * const ssid,
                          const size_t ssid_len);

int opendns_pop_tag_from_query(unsigned char * const packet,
                               size_t * const packet_size_p,
                               unsigned char * * const edns_options,
                               size_t *edns_options_len_p);
#endif
