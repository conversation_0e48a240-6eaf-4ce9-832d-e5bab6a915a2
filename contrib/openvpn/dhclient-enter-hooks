#!/bin/bash

function save_previous() {
  if [ -e $1 -a ! -e $1.predhclient ]; then
    mv $1 $1.predhclient 
  fi
}

function write_resolv_conf() {
  RESOLVCONF=$1
  if [ -n "$new_domain_name" ] || [ -n "$new_domain_name_servers" ]; then
    save_previous $RESOLVCONF
    echo '; generated by /etc/dhclient-enter-hooks' > $RESOLVCONF
    if [ -n "$SEARCH" ]; then
 	echo search $SEARCH >> $RESOLVCONF
    else
	if [ -n "$new_domain_name" ]; then
 	    echo search $new_domain_name >> $RESOLVCONF
 	fi
    fi
    chmod 644 $RESOLVCONF
    for nameserver in $new_domain_name_servers; do
      echo nameserver $nameserver >>$RESOLVCONF
    done
  fi
}

make_resolv_conf() {
  write_resolv_conf /etc/resolv.conf
}
