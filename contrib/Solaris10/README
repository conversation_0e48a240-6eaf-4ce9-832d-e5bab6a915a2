From: <PERSON> <<EMAIL>>
Date: Mon, Apr 7, 2008 at 3:31 AM
Subject: Solaris 10 service manifest
To: <EMAIL>


I've found dnsmasq much easier to set up on my home server running Solaris
10 than the stock dhcp/dns server, which is probably overkill anyway for my
simple home network needs. Since Solaris now uses SMF (Service Management
Facility) to manage services I thought I'd create a simple service manifest
for the dnsmasq service. The manifest currently assumes that dnsmasq has
been installed in '/usr/local/sbin/dnsmasq' and the configuration file in
'/usr/local/etc/dnsmasq.conf', so you may have to adjust these paths for
your local installation. Here are the steps I followed to install and enable
the dnsmasq service:
  # svccfg import dnsmasq.xml
  # svcadm enable dnsmasq

To confirm that the service is enabled and online:

  # svcs -l dnsmasq

I've just started learning about SMF so if anyone has any
corrections/feedback they are more than welcome.

Thanks,
<PERSON>

