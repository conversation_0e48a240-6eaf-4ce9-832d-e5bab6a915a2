<?xml version='1.0'?>
<!DOCTYPE service_bundle SYSTEM "/usr/share/lib/xml/dtd/service_bundle.dtd.1">

<!-- Service manifest for dnsmasq -->

<service_bundle type='manifest' name='dnsmasq'>
  <service name='network/dnsmasq' type='service' version='1'>

    <create_default_instance enabled='false'/>
    <single_instance/>

    <dependency name='multi-user'
                grouping='require_all'
                restart_on='refresh'
                type='service'>
      <service_fmri value='svc:/milestone/multi-user'/>
    </dependency>

    <dependency name='config'
		grouping='require_all'
		restart_on='restart'
		type='path'>
      <service_fmri value='file:///usr/local/etc/dnsmasq.conf'/>
    </dependency>

    <dependent name='dnsmasq_multi-user-server'
               grouping='optional_all'
               restart_on='none'>
      <service_fmri value='svc:/milestone/multi-user-server' />
    </dependent>

    <exec_method type='method' name='start'
                 exec='/usr/local/sbin/dnsmasq -C /usr/local/etc/dnsmasq.conf'
                 timeout_seconds='60' >
      <method_context>
        <method_credential user='root' group='root' privileges='all'/>
      </method_context>
    </exec_method>

    <exec_method type='method'
                 name='stop'
                 exec=':kill'
                 timeout_seconds='60'/>

    <exec_method type='method'
                 name='refresh'
                 exec=':kill -HUP'
                 timeout_seconds='60' />

    <template>
      <common_name>
        <loctext xml:lang='C'>dnsmasq server</loctext>
      </common_name>
      <description>
        <loctext xml:lang='C'>
dnsmasq - A lightweight DHCP and caching DNS server.
        </loctext>
      </description>
      <documentation>
        <manpage title='dnsmasq' section='8' manpath='/usr/local/man'/>
      </documentation>
    </template>

  </service>
</service_bundle>
