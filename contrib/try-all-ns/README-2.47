A remake of patch <PERSON> had posted to dnsmasq,
now compatible with version 2.47. Hopefully he doesn't 
mind (sending a copy of this mail to him too).

Maybe the patch in question is not acceptable
as it doesn't add new switch, rather it binds itself to "strict-order".

What it does is: if you have strict-order in the 
dnsmasq config file and query a domain that would result 
in NXDOMAIN, it iterates the whole given nameserver list 
until the last one says NXDOMAIN.
